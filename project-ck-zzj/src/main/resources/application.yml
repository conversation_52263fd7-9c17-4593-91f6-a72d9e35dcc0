server:
  servlet:
    context-path: /
  http2:
    enabled: true
  port: 8003
  tomcat:
    remoteip:
      remote-ip-header: 'x-forwarded-for'

mybatis-flex:
  global-config:
    deleted-value-of-logic-delete: "1"
    normal-value-of-logic-delete: "0"
#    logic-delete-column: "scbz"

spring:
  application:
    name: ck-zzj
  main:
    allow-circular-references: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:dd
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  profiles:
    active: prod

knife4j:
  # 是否开启Knife4j增强模式
  enable: true
  # 是否开启生产环境保护策略
  production: true
  # 前端Ui的个性化配置属性
  setting:
    # 默认语言
    language: zh-CN
    # 是否显示Footer
    enableFooter: false
    # 是否开启动态参数调试功能
    enableDynamicParameter: true
    # 是否在每个Debug调试栏后显示刷新变量按钮
    enableReloadCacheParameter: true
  # 生成模式需要登陆验证才能查看接口文档
  basic:
    username: zjjc
    password: 5A881C07726D56F79EEFE102EB577440
    enable: false


springdoc:
  default-flat-param-object: true
  group-configs:
    - group: 'system-api'
      packages-to-scan: com.zjjcnt.project.ck.sysadmin.web.controller
    - group: 'ck-zzj-api'
      packages-to-scan: com.zjjcnt.project.ck.zzj.web.controller

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 标题
  title: '常口自助机系统'
  # 描述
  description: '项目API接口文档'
  # 版本
  version: '版本号: 1.0'
  # 作者信息
  contact-name: zjjc
  contact-email: <EMAIL>
  contact-url: https://zjjc.com.cn
  token-url: http://localhost:8003/oauth2/token

logging:
  config: classpath:logback-prod.xml

zjjcnt:
  file-transport:
    ck-zzj:
      ywslcl:
        type: database
        oss-bucket-name: ck2016-ywsl
      zpyt:
        type: database
        oss-bucket-name: ck2016-ywsl
      ck-file:
        type: database
        oss-bucket-name: ck2016
      ryzp:
        type: database
        oss-bucket-name: o-zazd-ck2016
      yztyzp:
        type: database
        oss-bucket-name: ck2016
      oss:
        access-key-id: access-key-id
        access-key-secret: access-key-secret
        endpoint: https://oss-cn-hangzhou.aliyuncs.com
  crypto:
    database:
      enabled: false
  xtazd: 33
  security:
    login-log:
      enabled: true
    single-device-auth: true
    service:
      - userDetailsService
    web:
      enabled: false
      forcible: false
      timeout: 300000
    url-filter:
      urls:
        # 静态资源
        - /*.html
        - /**/*.html
        - /**/*.css
        - /**/*.js
        #swagger
        - /doc.html
        - /webjars/**
        - /favicon.ico
        - /swagger-resources
        - /v3/api-docs/**
        - /zzjBbgxpz/latestVersion
  rop:
    app-key: czrkzdyh
    url: http://127.0.0.1/router
    yzty:
      app-key: czrkzpcrj
      url: http://127.0.0.1/router
