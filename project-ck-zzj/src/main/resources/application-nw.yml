spring:
  data:
    redis:
      database: 5
      host: 127.0.0.1
      port: 6379
      password:
      timeout: 5000

mybatis-flex:
  datasource:
    ckdshz:
      url: *******************************************
      username: hz2015
      password: 123456
    ck:
      url: ***********************************************************************************************************************************************************************************************************************************************************************
      username: all2016
      password: all2016123456
    fw:
      url: ***********************************************************************************************************************************************************************************************************************************************************************
      username: all2016
      password: all2016123456
    jk:
      url: ***********************************************************************************************************************************************************************************************************************************************************************
      username: all2016
      password: all2016123456


zjjcnt:
  task:
    zlpz-lxtb:
      enabled: false
  xtazd: 33

knife4j:
  # 是否开启Knife4j增强模式
  enable: true
  # 是否开启生产环境保护策略
  production: false
  # 前端Ui的个性化配置属性
