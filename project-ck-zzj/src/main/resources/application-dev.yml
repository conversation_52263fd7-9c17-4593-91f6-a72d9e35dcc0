spring:
  data:
    redis:
      database: 15
      host: ************
      port: 6379
      password: jcecc0801new
      timeout: 5000

mybatis-flex:
  datasource:
    ckdshz:
      url: ***************************************
      username: hz2015
      password: hz@2023
    ck:
      url: *****************************************************************************************************************************************************************************************************************************************************************
      username: ck
      password: hc*#eC4yJ@bnesaF
    fw:
      url: *****************************************************************************************************************************************************************************************************************************************************************
      username: ck
      password: hc*#eC4yJ@bnesaF
    jk2016:
      url: *****************************************************************************************************************************************************************************************************************************************************************
      username: ck
      password: hc*#eC4yJ@bnesaF


zjjcnt:
  xtazd: 33

knife4j:
  # 是否开启Knife4j增强模式
  enable: true
  # 是否开启生产环境保护策略
  production: false
  # 前端Ui的个性化配置属性
