spring:
  data:
    redis:
      database: 1
      host: 127.0.0.1
      port: 6379
      password: jcecc0801new
      timeout: 5000

mybatis-flex:
  datasource:
    ckdshz:
      url: jdbc:polardb://************:3433/ckdshz
      username: hz2015
      password: 123456
    ck:
      url: *******************************************************************************************************************************************************************************************************************************************************************
      username: rkxtcs
      password: 123456
    fw:
      url: *******************************************************************************************************************************************************************************************************************************************************************
      username: rkxtcs
      password: 123456
    jk2016:
      url: *******************************************************************************************************************************************************************************************************************************************************************
      username: rkxtcs
      password: 123456


zjjcnt:
  task:
    zlpz-lxtb:
      enabled: false
  file-transport:
    ck-zzj:
      ywslcl:
        type: aliyun_oss
        oss-bucket-name: ck2016-ywsl
      zpyt:
        type: aliyun_oss
        oss-bucket-name: ck2016-ywsl
      ck-file:
        type: aliyun_oss
        oss-bucket-name: ck2016
      ryzp:
        type: aliyun_oss
        oss-bucket-name: o-zazd-ck2016
      oss:
        access-key-id: access-key-id
        access-key-secret: access-key-secret
        endpoint: https://oss-cn-hangzhou.aliyuncs.com
  xtazd: 33

knife4j:
  # 是否开启Knife4j增强模式
  enable: true
  # 是否开启生产环境保护策略
  production: false
  # 前端Ui的个性化配置属性
