package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.project.ck.zzj.dto.ck.RzbJkdyrzbDTO;
import lombok.Data;

import com.zjjcnt.common.core.entity.IdEntity;
import com.zjjcnt.common.core.annotation.Crypto;

import java.lang.String;

/**
 * 第三方接口调用日志表DO
 *
 * <AUTHOR>
 * @date 2025-05-09 13:40:14
 * @see RzbJkdyrzbDTO
 */
@Crypto
@Data
@Table("rzb_jkdyrzb")
public class RzbJkdyrzbDO implements IdEntity<String> {
    private static final long serialVersionUID = 4633326827380465047L;

    /**
     *
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    /**
     * 接口编号
     */
    @Column(value = "jkbh")
    private String jkbh;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 开始时间
     */
    @Column(value = "kssj")
    private String kssj;

    /**
     * 结束时间
     */
    @Column(value = "jssj")
    private String jssj;

    /**
     * 返回结果
     */
    @Column(value = "fhjg")
    private String fhjg;

    /**
     * 返回描述
     */
    @Column(value = "fhms")
    private String fhms;

    /**
     * 返回记录数
     */
    @Column(value = "fhjls")
    private String fhjls;

    /**
     * 入库时间
     */
    @Column(value = "rksj")
    private String rksj;


    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }
}
