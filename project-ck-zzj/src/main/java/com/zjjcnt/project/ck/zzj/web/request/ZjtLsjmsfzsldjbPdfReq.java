package com.zjjcnt.project.ck.zzj.web.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "临时居民身份证申领登记pf入参")
public class ZjtLsjmsfzsldjbPdfReq {

    @Schema(description = "受理单位")
    private String sldsjgsdwmc;

    @Schema(description = "公民身份号码")
    private String gmsfhm;
    @Schema(description = "姓名")
    private String xm;
    @Schema(description = "出生日期")
    private String csrq;
    @Schema(description = "性别")
    private String xb;
    @Schema(description = "民族")
    private String mz;
    @Schema(description = "常住户口所在地住址")
    private String zz;
    @Schema(description = "证卡管理号")
    private String zkglh;
    @Schema(description = "领证方式")
    private String lzfs;
    @Schema(description = "收件人姓名")
    private String sjrxm;
    @Schema(description = "收件人电话")
    private String sjrdh;
    @Schema(description = "收件人地址")
    private String sjrtxdz;
    @Schema(description = "邮政编码")
    private String yzbm;
    @Schema(description = "邮寄地址")
    private String yjdz;
    @Schema(description = "受理时间")
    private String czsj;
    @Schema(description = "操作人姓名")
    private String czyxm;
    @Schema(description = "人像zp")
    private String base64zp;
    @Schema(description = "签名")
    private String base64qm;
    @Schema(description = "年")
    private String year;
    @Schema(description = "月")
    private String month;
    @Schema(description = "日")
    private String day;
}
