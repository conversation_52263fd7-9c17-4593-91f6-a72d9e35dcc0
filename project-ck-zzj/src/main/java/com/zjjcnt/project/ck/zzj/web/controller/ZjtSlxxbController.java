package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtSlxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSlxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSlxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtSlxxbService;
import com.zjjcnt.project.ck.zzj.web.request.ZjtSlxxbLssfzblReq;
import com.zjjcnt.project.ck.zzj.web.request.ZjtSlxxbZjZjslYsfReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
* 居民身份证受理信息表前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/

@Tag(name = "居民身份证受理信息表")
@RestController
@RequestMapping("/zjtSlxxb")
public class ZjtSlxxbController extends AbstractCrudController<ZjtSlxxbDTO> {

    @Autowired
    private ZjtSlxxbService zjtSlxxbService;

    @Override
    protected IBaseService getService() {
        return zjtSlxxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询居民身份证受理信息表 N23001")
    public CommonResult<PageResult<ZjtSlxxbPageResp>> page(ZjtSlxxbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZjtSlxxbConvert.INSTANCE::convertToDTO, ZjtSlxxbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看居民身份证受理信息表详情 N23001")
    public CommonResult<ZjtSlxxbViewResp> view(String id) {
        return super.view(id, ZjtSlxxbConvert.INSTANCE::convertToViewResp);
    }

    @GetMapping("viewLssfzsl")
    @Operation(summary = "查看临时身份证受理信息表详情 N23011")
    public CommonResult<ZjtSlxxbViewResp> view(ZjtSlxxbLssfzblReq req) {
        ZjtSlxxbDTO lssfzSlxxb = zjtSlxxbService.findLssfzSlxxb(req.getGmsfhm(), req.getCzsj());
        return CommonResult.success(ZjtSlxxbConvert.INSTANCE.convertToViewResp(lssfzSlxxb));
    }

    @PostMapping("create")
    @Operation(summary = "新增居民身份证受理信息表 F23301")
    public CommonResult<ZjtSlxxbCreateResp> create(@RequestBody ZjtSlxxbCreateReq req) {
        return super.create(req, ZjtSlxxbConvert.INSTANCE::convertToDTO, ZjtSlxxbConvert.INSTANCE::convertToCreateResp);
    }

    @PostMapping("updateYsf")
    @Operation(summary = "受理信息已收费 F23307")
    public CommonResult<Boolean> updateYsf(@Validated @RequestBody ZjtSlxxbZjZjslYsfReq req) {
        zjtSlxxbService.processZjslYsfcl(req.getNbslid());
        return CommonResult.success(true);
    }

//    @PostMapping("update")
//    @Operation(summary = "编辑居民身份证受理信息表")
//    public CommonResult<Boolean> update(@RequestBody ZjtSlxxbUpdateReq req) {
//        return super.update(req, ZjtSlxxbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除居民身份证受理信息表")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }
//
//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(ZjtSlxxbExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出居民身份证受理信息表")
//    public void export(ZjtSlxxbPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "居民身份证受理信息表" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, ZjtSlxxbExp.class,
//                    ZjtSlxxbConvert.INSTANCE::convertToDTO, ZjtSlxxbConvert.INSTANCE::convertToExp, response);
//    }

}
