package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-05-15 16:18:06
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.RxZpcjxxbDTO
 */
@Crypto
@Data
@Table("rx_zpcjxxb")
public class RxZpcjxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -3296846286705774328L;

    /**
     * 照片信息ID
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 + SID_RX_ZPCJXXB.nextval FROM dual")
    private Long zpxxid;

    /**
     * 设备ID
     */
    @Column(value = "sbid")
    private Long sbid;

    /**
     * 证件种类
     */
    @Column(value = "zjzl")
    private String zjzl;

    /**
     * 证件号码
     */
    @Column(value = "zjhm")
    private String zjhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 国家代码
     */
    @Column(value = "gjdm")
    private String gjdm;

    /**
     * 国家名称
     */
    @Column(value = "gjmc")
    private String gjmc;

    /**
     * 采集用途
     */
    @Column(value = "cjyt")
    private String cjyt;

    /**
     * 原图照片ID
     */
    @Column(value = "ytzpid")
    private Long ytzpid;

    /**
     * 身份证照片ID
     */
    @Column(value = "sfzzpid")
    private Long sfzzpid;

    /**
     * 出入境照片ID
     */
    @Column(value = "crjzpid")
    private Long crjzpid;

    /**
     * 驾驶证照片ID
     */
    @Column(value = "jszzpid")
    private Long jszzpid;

    /**
     * 其他照片ID
     */
    @Column(value = "qtzpid")
    private Long qtzpid;

    /**
     * 其他照片类别
     */
    @Column(value = "qtzplb")
    private String qtzplb;

    /**
     * 其他照片名称
     */
    @Column(value = "qtzpmc")
    private String qtzpmc;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 派出所名称
     */
    @Column(value = "pcsmc")
    private String pcsmc;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private Long czyid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     * 操作员公民身份号码
     */
    @Crypto
    @Column(value = "czygmsfhm")
    private String czygmsfhm;

    /**
     * 操作员IP
     */
    @Column(value = "czyip")
    private String czyip;

    /**
     * 操作时间
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 同步标志
     */
    @Column(value = "tbbz")
    private String tbbz;

    /**
     * 同步时间
     */
    @Column(value = "tbsj")
    private String tbsj;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 待同步系统
     */
    @Column(value = "dtbxt")
    private String dtbxt;

    /**
     * 是否上传常口
     */
    @Column(value = "sfscck")
    private String sfscck;

    /**
     * 是否上传交警
     */
    @Column(value = "sfscjj")
    private String sfscjj;

    /**
     * 是否上传出入境
     */
    @Column(value = "sfsccrj")
    private String sfsccrj;

    /**
     * 是否上传长三角
     */
    @Column(value = "sfsccsj")
    private String sfsccsj;

    /**
     * 采集时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 设备点位编号
     */
    @Column(value = "zlpzsbdwbh")
    private String zlpzsbdwbh;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 采集来源
     */
    @Column(value = "cjly")
    private String cjly;

    /**
     * 10 身份证业务, 20 出入境业务, 30 驾驶证业务, 99 其它业务
     */
    @Column(value = "ywbllxdm")
    private String ywbllxdm;

    /**
     * 母片质检结果 0-未质检,1母片质检合格，2人工强制
     */
    @Column(value = "mpzjjg")
    private String mpzjjg;

    /**
     * 母片质检描述
     */
    @Column(value = "mpzjms")
    private String mpzjms;

    /**
     * 母片质检通过人姓名
     */
    @Column(value = "mpzjtgrxm")
    private String mpzjtgrxm;

    /**
     * 母片质检通过人身份号码
     */
    @Column(value = "mpzjtgrsfhm")
    private String mpzjtgrsfhm;

    /**
     * 出入境质检结果 1质检通过,2强行打标
     */
    @Column(value = "crjzjjg")
    private String crjzjjg;

    /**
     * 出入境质检描述
     */
    @Column(value = "crjzjms")
    private String crjzjms;

    /**
     * 出入境质检通过人姓名
     */
    @Column(value = "crjzjtgrxm")
    private String crjzjtgrxm;

    /**
     * 出入境质检通过人身份号码
     */
    @Column(value = "crjzjtgrsfhm")
    private String crjzjtgrsfhm;

//    /**
//     * 设备硬件码
//     */
//    @Column(value = "sbyjm")
//    private String sbyjm;


    @Override
    public Long getId() {
        return this.zpxxid;
    }

    @Override
    public void setId(Long id) {
        this.zpxxid = id;
    }
}
