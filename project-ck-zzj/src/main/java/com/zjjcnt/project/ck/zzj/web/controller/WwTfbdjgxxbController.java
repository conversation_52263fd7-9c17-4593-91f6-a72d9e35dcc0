package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwTfbdjgxxbDTO;
import com.zjjcnt.project.ck.zzj.manager.TfbdManager;
import com.zjjcnt.project.ck.zzj.service.ckdshz.WwTfbdjgxxbService;
import com.zjjcnt.project.ck.zzj.web.request.TfbdReq;
import com.zjjcnt.project.ck.zzj.web.response.TfbdRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 逃犯比对结果信息表前端控制器
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@RequiredArgsConstructor
@Tag(name = "逃犯比对结果信息表")
@RestController
@RequestMapping("/wwTfbdjgxxb")
public class WwTfbdjgxxbController extends AbstractCrudController<WwTfbdjgxxbDTO> {

    private final WwTfbdjgxxbService wwTfbdjgxxbService;
    private final TfbdManager tfbdManager;

    @Override
    protected IBaseService<WwTfbdjgxxbDTO> getService() {
        return wwTfbdjgxxbService;
    }

    @PostMapping("tfbd")
    @Operation(summary = "逃犯比对")
    public CommonResult<TfbdRes> tfbd(@Validated @RequestBody TfbdReq req) {
        return CommonResult.success(tfbdManager.doTfbd(req.getGmsfhm()));
    }

}
