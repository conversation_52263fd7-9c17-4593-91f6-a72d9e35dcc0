package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZzjSbpzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjSbpzbBatchSaveItem;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjSbpzbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjSbpzbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjSbpzbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.*;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjSbpzbDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZzjSbpzbExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
* 自助设备配置表Convert
*
* <AUTHOR>
* @date 2024-07-19 14:06:40
*/
@Mapper
public interface ZzjSbpzbConvert {

    ZzjSbpzbConvert INSTANCE = Mappers.getMapper(ZzjSbpzbConvert.class);

    ZzjSbpzbDTO convert(ZzjSbpzbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZzjSbpzbDO convertToDO(ZzjSbpzbDTO dto);

    List<ZzjSbpzbDO> convertToDO(List<ZzjSbpzbDTO> dto);

    ZzjSbpzbDTO convertToDTO(ZzjSbpzbPageReq req);

    ZzjSbpzbDTO convertToDTO(ZzjSbpzbCreateReq req);

    ZzjSbpzbDTO convertToDTO(ZzjSbpzbUpdateReq req);

    ZzjSbpzbPageResp convertToPageResp(ZzjSbpzbDTO dto);

    ZzjSbpzbListResp convertToListResp(ZzjSbpzbDTO dto);

    List<ZzjSbpzbListResp> convertToListResp(List<ZzjSbpzbDTO> dto);

    ZzjSbpzbViewResp convertToViewResp(ZzjSbpzbDTO dto);

    ZzjSbpzbCreateResp convertToCreateResp(ZzjSbpzbDTO dto);

    ZzjSbpzbExp convertToExp(ZzjSbpzbDTO dto);

    ZzjSbpzbDTO convertToDTO(ZzjSbpzbBatchSaveItem req);

    List<ZzjSbpzbDTO> convertToDTO(List<ZzjSbpzbBatchSaveItem> req);

    CameraParamTemplateResp convertToCameraParamTemplateResp(Map<String, Map<String, String>> map);

}
