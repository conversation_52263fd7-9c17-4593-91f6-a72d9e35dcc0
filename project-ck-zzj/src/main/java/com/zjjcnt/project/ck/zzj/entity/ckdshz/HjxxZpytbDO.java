package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 照片原图表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxZpytbDTO
 */
@Crypto
@Data
@Table("hjxx_zpytb")
public class HjxxZpytbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -1779511724716637604L;

    /**
     * 照片原图ID
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 + SID_HJXX_ZPYTB.nextval FROM dual")
    private Long zpytbid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 保存时间
     */
    @Column(value = "bcsj")
    private String bcsj;

    /**
     * 照片大小
     */
    @Column(value = "zpdx")
    private Long zpdx;

    /**
     * 照片文件类型
     */
    @Column(value = "zpwjlx")
    private String zpwjlx;

    /**
     * 照片数据地址类型
     */
    @Column(value = "zpsjdzlx")
    private String zpsjdzlx;

    /**
     * 照片数据地址
     */
    @Column(value = "zpsjdz")
    private String zpsjdz;

    /**
     * 照片HASH
     */
    @Column(value = "zphash")
    private String zphash;

    /**
     * 照片采集类型
     */
    @Column(value = "zpcjlx")
    private String zpcjlx;

    /**
     * 设备标识号
     */
    @Column(value = "zpsbbsh")
    private String zpsbbsh;

    /**
     * 设备品牌型号
     */
    @Column(value = "zpsbppxh")
    private String zpsbppxh;

    /**
     * 设备品牌型号代码
     */
    @Column(value = "zpsbppxhdm")
    private String zpsbppxhdm;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;


    @Override
    public Long getId() {
        return this.zpytbid;
    }

    @Override
    public void setId(Long id) {
        this.zpytbid = id;
    }
}
