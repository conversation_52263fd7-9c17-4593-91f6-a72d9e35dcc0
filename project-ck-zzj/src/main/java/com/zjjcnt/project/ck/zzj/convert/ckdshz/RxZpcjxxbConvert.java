package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.RxZpcjxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.RxZpcjxxbLxDO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RxZpcjxxbDO;
import com.zjjcnt.project.ck.zzj.web.request.ZlpzZpxxSaveReq;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-15 16:18:06
*/
@Mapper
public interface RxZpcjxxbConvert {

    RxZpcjxxbConvert INSTANCE = Mappers.getMapper(RxZpcjxxbConvert.class);

    RxZpcjxxbDTO convert(RxZpcjxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    RxZpcjxxbDO convertToDO(RxZpcjxxbDTO dto);

    @Mapping(target = "sfscck", defaultValue = Constants.NO)
    @Mapping(target = "sfscjj", defaultValue = Constants.NO)
    @Mapping(target = "sfsccrj", defaultValue = Constants.NO)
    @Mapping(target = "sfsccsj", defaultValue = Constants.YES)
    RxZpcjxxbDTO convertToDTO(ZlpzZpxxSaveReq req);

    @Mapping(ignore = true, target = "sbid")
    @Mapping(ignore = true, target = "tbsj")
    RxZpcjxxbDO convertToDO(RxZpcjxxbLxDO rxZpcjxxbLxDO);

}
