package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.RzZlpzFsrzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RzZlpzFsrzbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-09-23 16:21:37
*/
@Mapper
public interface RzZlpzFsrzbConvert {

    RzZlpzFsrzbConvert INSTANCE = Mappers.getMapper(RzZlpzFsrzbConvert.class);

    RzZlpzFsrzbDTO convert(RzZlpzFsrzbDO entity);

    @InheritInverseConfiguration(name="convert")
    RzZlpzFsrzbDO convertToDO(RzZlpzFsrzbDTO dto);

}