package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxHxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxHxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxHxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.HjxxHxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxHxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 户信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@Service
public class HjxxHxxbServiceImpl extends AbstractBaseServiceImpl<HjxxHxxbMapper, HjxxHxxbDO, HjxxHxxbDTO> implements HjxxHxxbService {

    HjxxHxxbConvert convert = HjxxHxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(HjxxHxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(HjxxHxxbDO::getHhid, dto.getHhid(), Objects.nonNull(dto.getHhid()));
        query.eq(HjxxHxxbDO::getMlpnbid, dto.getMlpnbid(), Objects.nonNull(dto.getMlpnbid()));
        query.eq(HjxxHxxbDO::getHh, dto.getHh(), Objects.nonNull(dto.getHh()));
        query.eq(HjxxHxxbDO::getHlx, dto.getHlx(), Objects.nonNull(dto.getHlx()));
        query.eq(HjxxHxxbDO::getJhlb, dto.getJhlb(), Objects.nonNull(dto.getJhlb()));
        query.eq(HjxxHxxbDO::getChlb, dto.getChlb(), Objects.nonNull(dto.getChlb()));
        query.ge(HjxxHxxbDO::getJhsj, dto.getJhsjStart(), StringUtils.isNotEmpty(dto.getJhsjStart()));
        query.le(HjxxHxxbDO::getJhsj, dto.getJhsjEnd(), StringUtils.isNotEmpty(dto.getJhsjEnd()));
        query.ge(HjxxHxxbDO::getChsj, dto.getChsjStart(), StringUtils.isNotEmpty(dto.getChsjStart()));
        query.le(HjxxHxxbDO::getChsj, dto.getChsjEnd(), StringUtils.isNotEmpty(dto.getChsjEnd()));
        query.eq(HjxxHxxbDO::getCjhjywid, dto.getCjhjywid(), Objects.nonNull(dto.getCjhjywid()));
        query.eq(HjxxHxxbDO::getCchjywid, dto.getCchjywid(), Objects.nonNull(dto.getCchjywid()));
        query.eq(HjxxHxxbDO::getBdfw, dto.getBdfw(), Objects.nonNull(dto.getBdfw()));
        query.eq(HjxxHxxbDO::getBdyy, dto.getBdyy(), Objects.nonNull(dto.getBdyy()));
        query.eq(HjxxHxxbDO::getHhzt, dto.getHhzt(), Objects.nonNull(dto.getHhzt()));
        query.eq(HjxxHxxbDO::getLxdbid, dto.getLxdbid(), Objects.nonNull(dto.getLxdbid()));
        query.eq(HjxxHxxbDO::getJlbz, dto.getJlbz(), Objects.nonNull(dto.getJlbz()));
        query.ge(HjxxHxxbDO::getQysj, dto.getQysjStart(), StringUtils.isNotEmpty(dto.getQysjStart()));
        query.le(HjxxHxxbDO::getQysj, dto.getQysjEnd(), StringUtils.isNotEmpty(dto.getQysjEnd()));
        query.ge(HjxxHxxbDO::getJssj, dto.getJssjStart(), StringUtils.isNotEmpty(dto.getJssjStart()));
        query.le(HjxxHxxbDO::getJssj, dto.getJssjEnd(), StringUtils.isNotEmpty(dto.getJssjEnd()));
        query.eq(HjxxHxxbDO::getCxbz, dto.getCxbz(), Objects.nonNull(dto.getCxbz()));
        query.eq(HjxxHxxbDO::getHdzdzbm, dto.getHdzdzbm(), Objects.nonNull(dto.getHdzdzbm()));
        query.likeLeft(HjxxHxxbDO::getHdzssxq, dto.getHdzssxq(), StringUtils.isNotEmpty(dto.getHdzssxq()));
        query.eq(HjxxHxxbDO::getHdzxxdz, dto.getHdzxxdz(), Objects.nonNull(dto.getHdzxxdz()));
        query.ge(HjxxHxxbDO::getGxsj, dto.getGxsjStart(), StringUtils.isNotEmpty(dto.getGxsjStart()));
        query.le(HjxxHxxbDO::getGxsj, dto.getGxsjEnd(), StringUtils.isNotEmpty(dto.getGxsjEnd()));
        query.eq(HjxxHxxbDO::getSjgsdwdm, dto.getSjgsdwdm(), Objects.nonNull(dto.getSjgsdwdm()));
        query.eq(HjxxHxxbDO::getSjgsdwmc, dto.getSjgsdwmc(), Objects.nonNull(dto.getSjgsdwmc()));
        return query;
    }

    @Override
    public HjxxHxxbDTO convertToDTO(HjxxHxxbDO hjxxHxxbDO) {
        return convert.convert(hjxxHxxbDO);
    }

    @Override
    public HjxxHxxbDO convertToDO(HjxxHxxbDTO hjxxHxxbDTO) {
        return convert.convertToDO(hjxxHxxbDTO);
    }
}
