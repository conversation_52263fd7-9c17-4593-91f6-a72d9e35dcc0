package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 离线照片采集信息表DO
 *
 * <AUTHOR>
 * @date 2024-06-21 09:30:28
 * @see com.zjjcnt.project.ck.zzj.dto.ck.RxZpcjxxbLxDTO
 */
@Crypto
@Data
@Table("rx_zpcjxxb_lx")
public class RxZpcjxxbLxDO implements IdEntity<Long> {
    private static final long serialVersionUID = 3799914942839127425L;

    /**
     * id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 浙里拍照设备点位编号
     */
    @Column(value = "zlpzsbdwbh")
    private String zlpzsbdwbh;

    /**
     * 设备硬件码
     */
    @Column(value = "sbyjm")
    private String sbyjm;

    /**
     * 设备ID
     */
    @Column(value = "sbid")
    private Long sbid;

    /**
     * 照片信息id
     */
    @Column(value = "zpxxid")
    private Long zpxxid;

    /**
     * 内部受理编号
     */
    @Column(value = "nbslbh")
    private String nbslbh;

    /**
     * 证件种类
     */
    @Column(value = "zjzl")
    private String zjzl;

    /**
     * 证件号码
     */
    @Column(value = "zjhm")
    private String zjhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 国家代码
     */
    @Column(value = "gjdm")
    private String gjdm;

    /**
     * 国家名称
     */
    @Column(value = "gjmc")
    private String gjmc;

    /**
     * 采集用途
     */
    @Column(value = "cjyt")
    private String cjyt;

    /**
     * 原图照片
     */
    @Column(value = "ytzp")
    private byte[] ytzp;

    /**
     * 身份证照片
     */
    @Column(value = "sfzzp")
    private byte[] sfzzp;

    /**
     * 出入境照片
     */
    @Column(value = "crjzp")
    private byte[] crjzp;

    /**
     * 驾驶证照片
     */
    @Column(value = "jszzp")
    private byte[] jszzp;

    /**
     * 其他照片
     */
    @Column(value = "qtzp")
    private byte[] qtzp;

    /**
     * 其他照片类别
     */
    @Column(value = "qtzplb")
    private String qtzplb;

    /**
     * 其他照片名称
     */
    @Column(value = "qtzpmc")
    private String qtzpmc;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 派出所名称
     */
    @Column(value = "pcsmc")
    private String pcsmc;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private Long czyid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     * 操作员公民身份号码
     */
    @Crypto
    @Column(value = "czygmsfhm")
    private String czygmsfhm;

    /**
     * 操作员IP
     */
    @Column(value = "czyip")
    private String czyip;

    /**
     * 操作时间
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 同步标志
     */
    @Column(value = "tbbz")
    private String tbbz;

    /**
     * 同步时间
     */
    @Column(value = "tbsj")
    private String tbsj;

    /**
     * 同步信息
     */
    @Column(value = "tbxx")
    private String tbxx;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 待同步系统
     */
    @Column(value = "dtbxt")
    private String dtbxt;

    /**
     * 是否上传常口
     */
    @Column(value = "sfscck")
    private String sfscck;

    /**
     * 是否上传交警
     */
    @Column(value = "sfscjj")
    private String sfscjj;

    /**
     * 是否上传出入境
     */
    @Column(value = "sfsccrj")
    private String sfsccrj;

    /**
     * 是否上传长三角
     */
    @Column(value = "sfsccsj")
    private String sfsccsj;

    /**
     * 采集时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 采集来源
     */
    @Column(value = "cjly")
    private String cjly;


    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
