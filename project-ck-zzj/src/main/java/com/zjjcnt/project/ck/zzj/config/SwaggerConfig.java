package com.zjjcnt.project.ck.zzj.config;

import com.zjjcnt.common.core.config.BaseSwaggerConfig;
import com.zjjcnt.project.ck.core.property.CkSwaggerProperties;
import jakarta.annotation.Resource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger 文档配置
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "swagger", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SwaggerConfig extends BaseSwaggerConfig {

    @Resource
    private CkSwaggerProperties swaggerProperties;

    @Override
    public com.zjjcnt.common.core.domain.SwaggerProperties swaggerProperties() {
        return swaggerProperties;
    }

}
