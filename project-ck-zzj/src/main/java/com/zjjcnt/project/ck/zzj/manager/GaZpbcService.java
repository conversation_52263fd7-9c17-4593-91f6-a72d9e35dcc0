package com.zjjcnt.project.ck.zzj.manager;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.service.XtXtkzcsbService;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxPzrzbDTO;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxPzrzbService;
import com.zjjcnt.project.ck.zzj.util.CkHttpUtils;
import com.zjjcnt.project.ck.zzj.web.response.VoCrjzpbcfhRes;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 调用广安照片保存接口，上传出入境照片
 * Created by hubin on 2022/05/19.
 */
@RequiredArgsConstructor
@Service
public class GaZpbcService {

    private final XtXtkzcsbService xtXtkzcsbService;
    private final HjxxPzrzbService hjxxPzrzbService;

    /**
     * 调用广安接口，上传出入境照片
     */
    public VoCrjzpbcfhRes saveToCrjZp(String gmsfhm, String base64zp) {

        //http://41.188.128.52/api/Upload/SavePhoto
        StringBuilder fwqurl = new StringBuilder();
        fwqurl.append(xtXtkzcsbService.findBzByKzlb("1616"));
        fwqurl.append("/Upload/SavePhoto");

        JSONObject json = new JSONObject();
        json.put("xpbh", gmsfhm);
        json.put("xpnr", base64zp);

        String result = "";
        try {
            result = CkHttpUtils.sendPost2(fwqurl.toString(), json.toJSONString(),"application/json;charset=utf-8");
//          result = "{\"success\":1,\"state\":null,\"message\":null,\"data\":true}";
            System.out.println("出入境照片上传接口返回：" + result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        VoCrjzpbcfhRes vofh = JSON.parseObject(result, VoCrjzpbcfhRes.class);

        if ("1".equals(vofh.getSuccess())){
           HjxxPzrzbDTO pzrzb = new HjxxPzrzbDTO();
            pzrzb.setZplsid(0L);
            pzrzb.setNbslid(Long.valueOf("0"));
            pzrzb.setYhid(Long.valueOf("0"));
            pzrzb.setGmsfhm(gmsfhm);
            pzrzb.setBcsj(ServerTimeUtils.getCurrentTime());
            pzrzb.setBz1("出入境照片");

            CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
            pzrzb.setYhid(currentUser.getUserId());
            pzrzb.setYhdlm(currentUser.getUsername());
            pzrzb.setYhdw(currentUser.getDeptCode());
            pzrzb.setYhxm(currentUser.getName());
            pzrzb.setIpdz(currentUser.getRemoteAddress());
            hjxxPzrzbService.insert(pzrzb);
        }

        return vofh;
    }


}
