package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjYztyZpSjConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYztyZpSjDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjYztyZpSjDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZzjYztyZpSjMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjYztyZpSjService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 自助机一照通用照片数据ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-01-14 16:13:45
 */
@Service
public class ZzjYztyZpSjServiceImpl extends AbstractBaseServiceImpl<ZzjYztyZpSjMapper, ZzjYztyZpSjDO, ZzjYztyZpSjDTO> implements ZzjYztyZpSjService {

    ZzjYztyZpSjConvert convert = ZzjYztyZpSjConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZzjYztyZpSjDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZzjYztyZpSjDO::getZpsj, dto.getZpsj(), Objects.nonNull(dto.getZpsj()));
        return query;
    }

    @Override
    public ZzjYztyZpSjDTO convertToDTO(ZzjYztyZpSjDO zzjYztyZpSjDO) {
        return convert.convert(zzjYztyZpSjDO);
    }

    @Override
    public ZzjYztyZpSjDO convertToDO(ZzjYztyZpSjDTO zzjYztyZpSjDTO) {
        return convert.convertToDO(zzjYztyZpSjDTO);
    }
}
