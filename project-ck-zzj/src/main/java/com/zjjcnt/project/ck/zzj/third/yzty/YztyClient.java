package com.zjjcnt.project.ck.zzj.third.yzty;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zjjcnt.common.core.dict.Dictionary;
import com.zjjcnt.common.core.dict.DictionaryNames;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.dto.XtDwxxbDTO;
import com.zjjcnt.project.ck.sysadmin.dto.XtXtcsbDTO;
import com.zjjcnt.project.ck.sysadmin.dto.XtXtkzcsbDTO;
import com.zjjcnt.project.ck.sysadmin.service.XtDwxxbService;
import com.zjjcnt.project.ck.sysadmin.service.XtXtcsbService;
import com.zjjcnt.project.ck.sysadmin.service.XtXtkzcsbService;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.constant.CkZzjDictTypeConstants;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.third.yzty.req.*;
import com.zjjcnt.project.ck.zzj.third.yzty.resp.*;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * 一照通用client
 *
 * <AUTHOR>
 * @date 2024-09-20 15:55:00
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class YztyClient {

    private final XtXtkzcsbService xtXtkzcsbService;
    private final XtDwxxbService xtDwxxbService;
    private final XtXtcsbService xtXtcsbService;

    private static final String URL_MPZJ = "/api/v1/330000-yzty-zpfy/zpzjfw";
    private static final String URL_CJDWSC = "/api/v1/330000-yzty-zpfy/cjdwscfw";
    private static final String URL_CJDWCX = "/api/v1/330000-yzty-zpfy/cjdwcxfw";

    private static final String APP_NAME = "浙里拍照自助端(临海版)";

    /**
     * 照片类型代码 00-母片
     */
    private static final String ZPLXDM_MP = "00";
    /**
     * 控制参数-部长三角接口访问地址参数
     */
    private static final String KZLB_CSJ_API = "1645";

    /**
     * 采集终端类型代码 00-公安窗口 01-自助机
     */
    private static final String CJDLXDM_GACK = "00";
    private static final String CJDLXDM_ZZJ = "01";
    private static final String CODE_SUCCESS = "200";
    private static final String CODE_SUCCESS_2 = "907";

    private static final long READ_TIMEOUT_SECONDS = 30L;
    private static final long CONNECT_TIMEOUT_SECONDS = 2L;

    private RestTemplate restTemplate;

    @PostConstruct
    public void init() {
        restTemplate = new RestTemplateBuilder()
                .readTimeout(Duration.ofSeconds(READ_TIMEOUT_SECONDS))
                .connectTimeout(Duration.ofSeconds(CONNECT_TIMEOUT_SECONDS))
                .build();
    }

    /**
     * 母片质检
     *
     * @param mpzjReq
     * @return
     */
    public YztyMpzjResp mpzj(YztyMpzjReq mpzjReq) {
        XtXtkzcsbDTO xtXtkzcsb = xtXtkzcsbService.findByKzlb(KZLB_CSJ_API);
        String url = xtXtkzcsb.getBz() + URL_MPZJ;
        try {
            // 入参拼接
            YztyReq yztyReq = new YztyReq();
            yztyReq.setRequestId(xtXtkzcsb.getKzz());
            yztyReq.setAppName(APP_NAME);
            yztyReq.setGmsfhm(mpzjReq.getGmsfhm());
            yztyReq.setXm(mpzjReq.getXm());
            yztyReq.setJgdm(mpzjReq.getJgdm());
            yztyReq.setJgdmmc(mpzjReq.getJgdmmc());
            yztyReq.setZdbs(mpzjReq.getZdbs());

            YztyMpzjRequestData yztyMpzjRequestData = new YztyMpzjRequestData();
            yztyMpzjRequestData.setSsdm("330000");

            YztyYwblxxReq ywblxx = new YztyYwblxxReq();
            ywblxx.setYwblxtmc(APP_NAME);
            ywblxx.setYwbldmc(mpzjReq.getJgdmmc());
            ywblxx.setYwbldwdm(mpzjReq.getJgdm());
            ywblxx.setYwbldwmc(mpzjReq.getJgdmmc());
            ywblxx.setYwjbrxm(mpzjReq.getXm());
            ywblxx.setYwjbrsfzh(mpzjReq.getGmsfhm());

            YztyBlrxxReq blrxx = new YztyBlrxxReq();
            if (StringUtils.isNotBlank(mpzjReq.getBlrzjhm())) {
                blrxx.setBlrxm(mpzjReq.getBlrxm());
                blrxx.setBlrzjhm(mpzjReq.getBlrzjhm());
                blrxx.setBlrzjlxdm(mpzjReq.getBlrzjlxdm());
            }

            YztyCjzdxxReq cjzdxx = new YztyCjzdxxReq();
            cjzdxx.setCjzdbm(mpzjReq.getCjzdbm());
            cjzdxx.setCjdlxdm(CJDLXDM_GACK);
            cjzdxx.setSfyrzs(Constants.YES);

            YztyZpxxReq zpxx = new YztyZpxxReq();
            zpxx.setZplxdm(ZPLXDM_MP);
            zpxx.setMpsj(mpzjReq.getMpsj());
            if (StringUtils.isNotBlank(mpzjReq.getTxmsj())) {
                yztyMpzjRequestData.setTxmsj(mpzjReq.getTxmsj());
                zpxx.setTyzpbm(mpzjReq.getTxmsj());
            } else {
                zpxx.setTyzpbm(mpzjReq.getBlrzjhm());
            }

            yztyMpzjRequestData.setBlrxx(blrxx);
            yztyMpzjRequestData.setCjzdxx(cjzdxx);
            yztyMpzjRequestData.setYwblxx(ywblxx);
            yztyMpzjRequestData.setZpxx(zpxx);
            yztyReq.setRequestData(yztyMpzjRequestData);

            String result = postForStr(url, yztyReq);

            YztyResponse<YztyDataResponse<YztyMpzjResponse>> yztyResponse = JsonUtils.parseObject(result,
                    new TypeReference<YztyResponse<YztyDataResponse<YztyMpzjResponse>>>() {});
            if (!CODE_SUCCESS.equals(yztyResponse.getStatus())) {
                return YztyMpzjResp.error(yztyResponse.getMessage());
            }

            YztyDataResponse<YztyMpzjResponse> yztyDataResp = yztyResponse.getData();
            if (!CODE_SUCCESS.equals(yztyDataResp.getStatus())) {
                return YztyMpzjResp.error(yztyDataResp.getMessage());
            }

            YztyMpzjResponse yztyMpzjResponse = yztyDataResp.getData();
            if (Constants.YES.equals(yztyMpzjResponse.getSfhg())) {
                return YztyMpzjResp.success(yztyMpzjResponse.getZjbhgjgms());
            } else {
                String message = StringUtils.isNotBlank(yztyMpzjResponse.getZjbhgjgms()) ?
                        yztyMpzjResponse.getZjbhgjgms() : yztyMpzjResponse.getInfo();
                return YztyMpzjResp.fail(message);
            }
        } catch (Exception e) {
            log.error("一照通用母片质检请求错误", e);
            return YztyMpzjResp.error(CkZzjErrorCode.YZTY_MPZJ_API_ERROR.getMessage());
        }
    }

    /**
     * 采集终端注册信息查询服务
     * @param yztyCjdwsc
     * @return
     * @throws ServiceException
     */
    public String cjdwcx(YztyCjdwsc yztyCjdwsc) throws ServiceException {

        XtXtkzcsbDTO xtXtkzcsb = xtXtkzcsbService.findByKzlb(KZLB_CSJ_API);

        String url = xtXtkzcsb.getBz() + URL_CJDWCX;
        YztyReq yztyReq = new YztyReq();
        yztyReq.setGmsfhm(yztyCjdwsc.getZddjrgmsfhm());
        yztyReq.setXm(yztyCjdwsc.getZddjrxm());
        yztyReq.setJgdm(yztyCjdwsc.getDwjgdm());
        if (yztyCjdwsc.getDwjgmc() == null || yztyCjdwsc.getDwjgmc().isEmpty()) {
            yztyReq.setJgdmmc(yztyCjdwsc.getPcsmc());
        } else {
            yztyReq.setJgdmmc(yztyCjdwsc.getDwjgmc());
        }
        yztyReq.setZdbs(yztyCjdwsc.getSbip());
        yztyReq.setRequestId(xtXtkzcsb.getKzz());
        yztyReq.setAppName(APP_NAME);

        YztyCjdwcxRequestData requestData = new YztyCjdwcxRequestData();
        requestData.setSbdwbh(yztyCjdwsc.getZlpzsbdwbh());
        yztyReq.setRequestData(requestData);

        String result;
        try {
            result = postForStr(url, yztyReq);
        } catch (Exception e) {
            log.error("一照通用采集终端查询接口请求失败", e);
            throw new ServiceException(CkZzjErrorCode.YZTY_CJDWCX_API_ERROR);
        }

        YztyResponse<YztyCjdwcxDataResponse> yztyResponse = JsonUtils.parseObject(result,
                new TypeReference<YztyResponse<YztyCjdwcxDataResponse>>() {});

        if (!CODE_SUCCESS.equals(yztyResponse.getStatus())) {
            log.error("一照通用采集点位查询失败， status={}, message={}", yztyResponse.getStatus(), yztyResponse.getMessage());
            return "";
        }

        YztyCjdwcxDataResponse dataResponse = yztyResponse.getData();
        if (StringUtils.isNotBlank(dataResponse.getCjzdbm())) {
            return dataResponse.getCjzdbm();
        }
        return "";
    }

    /**
     * 采集终端上传服务
     * @param yztyCjdwsc
     * @param czlx
     * @return
     * @throws ServiceException
     */
    public String cjdwsc(YztyCjdwsc yztyCjdwsc, String czlx) throws ServiceException {
        XtXtkzcsbDTO xtXtkzcsb = xtXtkzcsbService.findByKzlb(KZLB_CSJ_API);
        String url = xtXtkzcsb.getBz() + URL_CJDWSC;

        // 单位信息
        XtDwxxbDTO xtDwxxbDTO = xtDwxxbService.findById(yztyCjdwsc.getPcs());
        YztyReq yztyReq = new YztyReq();
        yztyReq.setGmsfhm(yztyCjdwsc.getZddjrgmsfhm());
        yztyReq.setXm(yztyCjdwsc.getZddjrxm());
        yztyReq.setJgdm(yztyCjdwsc.getDwjgdm());
        if (StringUtils.isBlank(yztyCjdwsc.getDwjgmc())) {
            yztyReq.setJgdmmc(yztyCjdwsc.getPcsmc());
        } else {
            yztyReq.setJgdmmc(yztyCjdwsc.getDwjgmc());
        }
        yztyReq.setZdbs(yztyCjdwsc.getSbip());
        yztyReq.setRequestId(xtXtkzcsb.getKzz());
        yztyReq.setAppName(APP_NAME);

        YztyCjdwscRequestData cjdwscRequestData = new YztyCjdwscRequestData();
        YztyCjdwscSqzdxxReq sqzdxx = new YztyCjdwscSqzdxxReq();
        sqzdxx.setSqssdm(xtDwxxbDTO.getQhdm());

        String xzqhmc = Dictionary.getValue(DictionaryNames.DM_XZQH, xtDwxxbDTO.getQhdm());

        sqzdxx.setSqssmc(xzqhmc);
        sqzdxx.setSqdwdm(yztyReq.getJgdm());
        sqzdxx.setSqdwmc(yztyReq.getJgdmmc());
        sqzdxx.setSqrxm(yztyReq.getXm());
        sqzdxx.setSqrzjhm(yztyReq.getGmsfhm());
        sqzdxx.setSqrdh(yztyCjdwsc.getZddjrlxdh());

        YztyCjdwscZdxxReq zdxx = new YztyCjdwscZdxxReq();
        zdxx.setSbdwbh(yztyCjdwsc.getZlpzsbdwbh());
        String fwckbh = yztyCjdwsc.getZlpzsbdwbh().substring(0, 12);
        zdxx.setFwckbh(fwckbh);
        zdxx.setFwckmc(yztyCjdwsc.getSbsydmc());
        zdxx.setSbxh(yztyCjdwsc.getZlpzsbdwbh().substring(12, 14));
        zdxx.setSbdwdm(yztyReq.getJgdm());
        zdxx.setSbdwmc(yztyReq.getJgdmmc());
        zdxx.setSbdwdz(yztyCjdwsc.getSbsydz());
        zdxx.setMacdz(yztyCjdwsc.getSbmac());
        zdxx.setSfgddw(Constants.YES);
        zdxx.setJd(yztyCjdwsc.getJd());
        zdxx.setWd(yztyCjdwsc.getWd());
        zdxx.setSfyrzs(Constants.YES);
        XtXtcsbDTO xtXtcsb = xtXtcsbService.findByCslbAndDm(CkZzjDictTypeConstants.DM_ZPSBPPXH, yztyCjdwsc.getSblx());
        if (xtXtcsb == null || StringUtils.isBlank(xtXtcsb.getKzbzb())) {
            zdxx.setCjzdlxdm("99");
            zdxx.setCjzdlxmc("其他");
        } else {
            zdxx.setCjzdlxdm(xtXtcsb.getKzbzb());
            zdxx.setCjzdlxmc(xtXtcsb.getKzbzc());
        }
        XtXtcsbDTO xtXtcsbFwck = xtXtcsbService.findByCslbAndDm(CkZzjDictTypeConstants.DM_PZFWCKLBDM, yztyCjdwsc.getSbsydlbdm());
        zdxx.setDwfwcklbdm(xtXtcsbFwck.getKzbzc());
        zdxx.setDwfwcklbmc(xtXtcsbFwck.getKzbzd());
        if (CkZzjConstants.CJZD_CZLX_ZX.equals(czlx)) {
            //sfqy 2 czlx 3 是注销
            zdxx.setSfqy(CkZzjConstants.CJZD_SFQY_QY);
        } else {
            zdxx.setSfqy(CkZzjConstants.CJZD_SFQY_ZY);
        }
        zdxx.setQyrq(ServerTimeUtils.getCurrentDate());
        zdxx.setZcrq(ServerTimeUtils.getCurrentDate());
        zdxx.setDwbdsj(DateTimeUtils.now(DateTimeUtils.CHAR_DATETIME_PATTERN));
        if (CkZzjConstants.CJZD_CZLX_XZ.equals(czlx)) {
            zdxx.setCzlx(CkZzjConstants.CJZD_CZLX_XZ);
        } else {
            zdxx.setCzlx(CkZzjConstants.CJZD_CZLX_XG);
        }
        cjdwscRequestData.setZdxx(zdxx);
        cjdwscRequestData.setSqzdxx(sqzdxx);
        yztyReq.setRequestData(cjdwscRequestData);

        String result;
        try {
            result = postForStr(url, yztyReq);
        } catch (Exception e) {
            log.error("一照通用采集终端上传接口请求失败", e);
            throw new ServiceException(CkZzjErrorCode.YZTY_CJDWSC_API_ERROR);
        }

        log.info("一照通用注册采集点, zlpzsbdwbh={}, 返回信息：{}", yztyCjdwsc.getZlpzsbdwbh(), result);
        return parseCjdwscResult(yztyCjdwsc, result, czlx);
    }

    private String parseCjdwscResult(YztyCjdwsc yztyCjdwsc, String result, String czlx) {
        String cjzdbm = "";
        YztyResponse<YztyDataResponse<YztyCjdwscResponse>> yztyResponse = JsonUtils.parseObject(result,
                new TypeReference<YztyResponse<YztyDataResponse<YztyCjdwscResponse>>>() {});

        //注册设备时返回907接收下来，修改设备时返回907抛出
        if (CkZzjConstants.CJZD_CZLX_XZ.equals(czlx)) {
            if (CODE_SUCCESS_2.equals(yztyResponse.getStatus())) {
                return cjdwcx(yztyCjdwsc);
            }

            if (!CODE_SUCCESS.equals(yztyResponse.getStatus())) {
                log.error("一照通用采集点位新增一级报错， status={}, message={}", yztyResponse.getStatus(), yztyResponse.getMessage());
                throw new ServiceException(CkZzjErrorCode.YZTY_CJDWSC_API_ERROR, yztyResponse.getMessage());
            }
            YztyDataResponse<YztyCjdwscResponse> yztyDataResp = yztyResponse.getData();
            if (!CODE_SUCCESS.equals(yztyDataResp.getStatus())) {
                log.error("一照通用采集点位新增二级报错， status={}, message={}, ywslh={}", yztyDataResp.getStatus(),
                        yztyDataResp.getMessage(), yztyDataResp.getYwslh());
                throw new ServiceException(CkZzjErrorCode.YZTY_CJDWSC_API_ERROR, yztyDataResp.getMessage());
            }
            YztyCjdwscResponse cjdwscResponse = yztyDataResp.getData();
            if (StringUtils.isNotBlank(cjdwscResponse.getCjzdbm())) {
                cjzdbm = cjdwscResponse.getCjzdbm();
            } else {
                log.info("一照通用采集点位新增info报错, code={}, info={}", cjdwscResponse.getCode(), cjdwscResponse.getInfo());
                throw new ServiceException(CkZzjErrorCode.YZTY_CJDWSC_API_ERROR, cjdwscResponse.getInfo());
            }
        } else if (CkZzjConstants.CJZD_CZLX_XG.equals(czlx)) {
            if (!CODE_SUCCESS.equals(yztyResponse.getStatus())) {
                log.error("一照通用采集点位修改失败， status={}, message={}", yztyResponse.getStatus(), yztyResponse.getMessage());
                throw new ServiceException(CkZzjErrorCode.YZTY_CJDWSC_API_ERROR, yztyResponse.getMessage());
            }
        }
        return cjzdbm;
    }

    private String postForStr(String url, Object req) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.toJsonString(req), headers);
        return restTemplate.postForObject(url, entity, String.class);
    }
}
