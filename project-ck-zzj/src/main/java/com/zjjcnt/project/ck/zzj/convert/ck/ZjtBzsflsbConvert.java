package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtBzsflsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtBzsflsbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtBzsflsbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtBzsflsbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtBzsflsbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtBzsflsbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtBzsflsbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtBzsflsbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 办证收费流水表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtBzsflsbConvert {

    ZjtBzsflsbConvert INSTANCE = Mappers.getMapper(ZjtBzsflsbConvert.class);

    ZjtBzsflsbDTO convert(ZjtBzsflsbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtBzsflsbDO convertToDO(ZjtBzsflsbDTO dto);

    ZjtBzsflsbDTO convertToDTO(ZjtBzsflsbPageReq req);

    ZjtBzsflsbDTO convertToDTO(ZjtBzsflsbCreateReq req);

    ZjtBzsflsbDTO convertToDTO(ZjtBzsflsbUpdateReq req);

    @Mapping(ignore = true, target = "id")
    ZjtBzsflsbDTO convertToDTO(ZjtSlxxbDTO zjtSlxxb);

    ZjtBzsflsbPageResp convertToPageResp(ZjtBzsflsbDTO dto);

    ZjtBzsflsbViewResp convertToViewResp(ZjtBzsflsbDTO dto);

    ZjtBzsflsbCreateResp convertToCreateResp(ZjtBzsflsbDTO dto);

}
