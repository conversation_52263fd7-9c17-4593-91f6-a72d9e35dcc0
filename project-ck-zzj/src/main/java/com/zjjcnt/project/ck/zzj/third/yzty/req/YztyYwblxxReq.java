package com.zjjcnt.project.ck.zzj.third.yzty.req;

import lombok.Data;

/**
 * 业务办理信息
 * <AUTHOR>
 */
@Data
public class YztyYwblxxReq {
    /**
     * 业务办理系统名称
     */
    private String ywblxtmc;

    /**
     * 业务办理点名称
     */
    private String ywbldmc;

    /**
     * 业务办理单位代码
     */
    private String ywbldwdm;

    /**
     * 业务办理单位名称
     */
    private String ywbldwmc;

    /**
     * 业务经办人姓名
     */
    private String ywjbrxm;

    /**
     * 业务经办人身份证号
     */
    private String ywjbrsfzh;

    /**
     * 业务流水号 可为空
     */
    private String ywlsh;
}
