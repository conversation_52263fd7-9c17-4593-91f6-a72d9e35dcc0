package com.zjjcnt.project.ck.zzj.manager;

import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjSbpzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYhsbpzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYhsbpzbListResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjSbpzbService;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjYhsbpzbService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户设备配置manager
 *
 * <AUTHOR>
 * @date 2024-07-19 15:12:00
 */
@RequiredArgsConstructor
@Service
public class ZzjYhsbpzbManager {

    private final ZzjSbpzbService zzjSbpzbService;
    private final ZzjYhsbpzbService zzjYhsbpzbService;

    public List<ZzjYhsbpzbListResp> listYhsbpz(Long yhid) {
        ZzjYhsbpzbDTO zzjYhsbpzbDTO = new ZzjYhsbpzbDTO();
        zzjYhsbpzbDTO.setYhid(yhid);
        zzjYhsbpzbDTO.setYxbz(Constants.YES);
        List<ZzjYhsbpzbDTO> yhsbpzbList = zzjYhsbpzbService.list(zzjYhsbpzbDTO);

        Map<Long, String> yhpzMap = yhsbpzbList.stream().collect(Collectors.toMap(ZzjYhsbpzbDTO::getSbpzid,
                ZzjYhsbpzbDTO::getPzz));

        ZzjSbpzbDTO zzjSbpzbDTO = new ZzjSbpzbDTO();
        zzjSbpzbDTO.setYxbz(Constants.YES);
        List<ZzjSbpzbDTO> sbpzbList = zzjSbpzbService.list(zzjSbpzbDTO);

        return sbpzbList.stream().map(zzjSbpzb -> {
            ZzjYhsbpzbListResp zzjYhsbpzbListResp = new ZzjYhsbpzbListResp();
            zzjYhsbpzbListResp.setSbpzid(zzjSbpzb.getId());
            zzjYhsbpzbListResp.setYhid(yhid);
            zzjYhsbpzbListResp.setPzbm(zzjSbpzb.getPzbm());
            zzjYhsbpzbListResp.setPzz(yhpzMap.getOrDefault(zzjSbpzb.getId(), zzjSbpzb.getMrz()));
            zzjYhsbpzbListResp.setMrz(zzjSbpzb.getMrz());
            return zzjYhsbpzbListResp;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<ZzjYhsbpzbDTO> list) {
        list.forEach(zzjYhsbpzbDTO -> {
            ZzjYhsbpzbDTO query = new ZzjYhsbpzbDTO();
            query.setYhid(zzjYhsbpzbDTO.getYhid());
            query.setSbpzid(zzjYhsbpzbDTO.getSbpzid());

            ZzjYhsbpzbDTO exist = zzjYhsbpzbService.find(query);

            if (Objects.isNull(exist)) {
                zzjYhsbpzbService.insert(zzjYhsbpzbDTO);
            } else if (!StringUtils.equals(zzjYhsbpzbDTO.getPzz(), exist.getPzz())){
                zzjYhsbpzbDTO.setId(exist.getId());
                zzjYhsbpzbDTO.setYxbz(Constants.YES);
                zzjYhsbpzbService.update(zzjYhsbpzbDTO);
            }
        });
    }
}
