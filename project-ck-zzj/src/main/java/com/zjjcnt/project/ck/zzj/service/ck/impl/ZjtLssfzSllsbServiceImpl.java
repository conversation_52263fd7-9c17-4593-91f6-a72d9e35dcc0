package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtLssfzSllsbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSllsbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtLssfzSllsbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtLssfzSllsbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtLssfzSllsbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 临时身份证受理历史表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Service
public class ZjtLssfzSllsbServiceImpl extends AbstractBaseServiceImpl<ZjtLssfzSllsbMapper, ZjtLssfzSllsbDO, ZjtLssfzSllsbDTO> implements ZjtLssfzSllsbService {

    ZjtLssfzSllsbConvert convert = ZjtLssfzSllsbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtLssfzSllsbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtLssfzSllsbDO::getLsslid, dto.getLsslid(), StringUtils.isNotEmpty(dto.getLsslid()));
        query.eq(ZjtLssfzSllsbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtLssfzSllsbDO::getBgqkh, dto.getBgqkh(), StringUtils.isNotEmpty(dto.getBgqkh()));
        query.eq(ZjtLssfzSllsbDO::getBghkh, dto.getBghkh(), StringUtils.isNotEmpty(dto.getBghkh()));
        query.eq(ZjtLssfzSllsbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.ge(ZjtLssfzSllsbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtLssfzSllsbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtLssfzSllsbDO::getCzyip, dto.getCzyip(), StringUtils.isNotEmpty(dto.getCzyip()));
        query.eq(ZjtLssfzSllsbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        return query;
    }

    @Override
    public ZjtLssfzSllsbDTO convertToDTO(ZjtLssfzSllsbDO zjtLssfzSllsbDO) {
        return convert.convert(zjtLssfzSllsbDO);
    }

    @Override
    public ZjtLssfzSllsbDO convertToDO(ZjtLssfzSllsbDTO zjtLssfzSllsbDTO) {
        return convert.convertToDO(zjtLssfzSllsbDTO);
    }
}
