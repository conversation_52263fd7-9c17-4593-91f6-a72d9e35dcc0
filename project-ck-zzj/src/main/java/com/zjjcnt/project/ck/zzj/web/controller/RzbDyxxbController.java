package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.RktYwslClysjConvert;
import com.zjjcnt.project.ck.zzj.convert.ck.RzbDyxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.RzbDyxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.RzbDyxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.RzbDyxxbCreateResp;
import com.zjjcnt.project.ck.zzj.service.ck.RzbDyxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 打印信息表前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/

@Tag(name = "打印信息表")
@RestController
@RequestMapping("/rzbDyxxb")
public class RzbDyxxbController extends AbstractCrudController<RzbDyxxbDTO> {

    @Autowired
    private RzbDyxxbService rzbDyxxbService;

    @Override
    protected IBaseService getService() {
        return rzbDyxxbService;
    }

//    @GetMapping("page")
//    @Operation(summary = "查询打印信息表")
//    public CommonResult<PageResult<RzbDyxxbPageResp>> page(RzbDyxxbPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, RzbDyxxbConvert.INSTANCE::convertToDTO, RzbDyxxbConvert.INSTANCE::convertToPageResp);
//    }
//
//    @GetMapping("view")
//    @Operation(summary = "查看打印信息表详情")
//    public CommonResult<RzbDyxxbViewResp> view(String id) {
//        return super.view(id, RzbDyxxbConvert.INSTANCE::convertToViewResp);
//    }

    @PostMapping("create")
    @Operation(summary = "新增打印信息表 F23419")
    public CommonResult<RzbDyxxbCreateResp> create(@RequestBody RzbDyxxbCreateReq req) {
        RzbDyxxbDTO insert = rzbDyxxbService.insert(RzbDyxxbConvert.INSTANCE.convertToDTO(req),
                RktYwslClysjConvert.INSTANCE.convertToDTO(req.getClysjList()));
        return CommonResult.success(RzbDyxxbConvert.INSTANCE.convertToCreateResp(insert));
    }


//    @PostMapping("update")
//    @Operation(summary = "编辑打印信息表")
//    public CommonResult<Boolean> update(@RequestBody RzbDyxxbUpdateReq req) {
//        return super.update(req, RzbDyxxbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除打印信息表")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }

}
