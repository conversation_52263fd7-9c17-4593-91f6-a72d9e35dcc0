package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 证件业务申请信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtSqxxbDTO
 */
@Crypto
@Data
@Table("zjt_sqxxb")
public class ZjtSqxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = -1756251465640384365L;

    /**
     *
     */
    @Id(keyType = KeyType.None)
    private String ywslh;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 申请人_公民身份号码
     */
    @Crypto
    @Column(value = "sqrgmsfhm")
    private String sqrgmsfhm;

    /**
     * 申请人_姓名
     */
    @Crypto
    @Column(value = "sqrxm")
    private String sqrxm;

    /**
     *
     */
    @Crypto
    @Column(value = "sqrlxdh")
    private String sqrlxdh;

    /**
     *
     */
    @Column(value = "sqrq")
    private String sqrq;

    /**
     *
     */
    @Column(value = "sqrzpid")
    private String sqrzpid;

    /**
     *
     */
    @Column(value = "slzt")
    private String slzt;

    /**
     * 收费类型
     */
    @Column(value = "sflx")
    private String sflx;

    /**
     * 收费金额
     */
    @Column(value = "sfje")
    private BigDecimal sfje;

    /**
     * 受理单位_公安机关机构代码
     */
    @Column(value = "sldwgajgjgdm")
    private String sldwgajgjgdm;

    /**
     * 受理单位_公安机关名称
     */
    @Column(value = "sldwgajgmc")
    private String sldwgajgmc;

    /**
     * 受理人姓名
     */
    @Crypto
    @Column(value = "slrxm")
    private String slrxm;

    /**
     * 受理时间
     */
    @Column(value = "slsj")
    private String slsj;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     *
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     *
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     *
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     *
     */
    @Column(value = "sldfjsjgsdwdm")
    private String sldfjsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldfjsjgsdwmc")
    private String sldfjsjgsdwmc;

    /**
     * 收费单据号
     */
    @Column(value = "sfdjh")
    private String sfdjh;

    /**
     *
     */
    @Column(value = "bysllx")
    private String bysllx;

    /**
     *
     */
    @Column(value = "byslsm")
    private String byslsm;

    /**
     *
     */
    @Column(value = "zzlx")
    private String zzlx;

    /**
     * 服务对象
     */
    @Column(value = "fwdx")
    private String fwdx;

    /**
     * 互联网申请id
     */
    @Column(value = "hlwsqid")
    private String hlwsqid;

    /**
     * 第三方用户名称
     */
    @Column(value = "username")
    private String username;

    /**
     * 申请人人像比对时间
     */
    @Column(value = "sqrrxbdsj")
    private String sqrrxbdsj;

    /**
     * 申请人人像比对相似度
     */
    @Column(value = "sqrrxbdxsd")
    private String sqrrxbdxsd;

    /**
     * 申请人人像比对结果
     */
    @Column(value = "sqrrxbdjg")
    private String sqrrxbdjg;


    @Override
    public String getId() {
        return this.ywslh;
    }

    @Override
    public void setId(String id) {
        this.ywslh = id;
    }
}
