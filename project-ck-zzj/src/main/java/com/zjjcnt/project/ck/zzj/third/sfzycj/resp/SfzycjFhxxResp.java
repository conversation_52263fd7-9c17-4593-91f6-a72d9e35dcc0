package com.zjjcnt.project.ck.zzj.third.sfzycj.resp;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * 身份证预采集返回信息
 */
@Data
public class SfzycjFhxxResp {

    private String blrGmsfhm;//办理人公民身份号码
    private String blrXm;//办理人姓名
    private String blrLxdh;//办理人联系电话
    private String blrXjzszqy;//办理人现居住所在区域
    private String blrXjzxxdz;//办理人现居住详细地址
    private String sjrXm;//收件人姓名
    private String sjrLxdh;//收件人联系电话
    private String sjrXjzszqy;//收件人现居住所在区域
    private String sjrXjzxxdz;//收件人现居住详细地址
    private String jjlxrXm;//紧急联系人姓名
    private String jjlxrLxdh;//紧急联系人联系电话
    private String jjlxrYsjrgx;//紧急联系人与收件人关系
    private String createTime;//创建时间

    public void decryptWzxx(String key) {
        SymmetricCrypto sm4 = SmUtil.sm4(key.getBytes(StandardCharsets.UTF_8));
        this.blrGmsfhm = sm4.decryptStr(this.blrGmsfhm);
        this.blrXm = sm4.decryptStr(this.blrXm);
        this.blrLxdh = sm4.decryptStr(this.blrLxdh);
        this.sjrXm = sm4.decryptStr(this.sjrXm);
        this.sjrLxdh = sm4.decryptStr(this.sjrLxdh);
        this.jjlxrXm = sm4.decryptStr(this.jjlxrXm);
        this.jjlxrLxdh = sm4.decryptStr(this.jjlxrLxdh);
    }
}
