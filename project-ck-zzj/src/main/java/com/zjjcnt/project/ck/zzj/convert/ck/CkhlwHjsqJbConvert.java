package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.CkhlwHjsqJbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.CkhlwHjsqJbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface CkhlwHjsqJbConvert {

    CkhlwHjsqJbConvert INSTANCE = Mappers.getMapper(CkhlwHjsqJbConvert.class);

    CkhlwHjsqJbDTO convert(CkhlwHjsqJbDO entity);

    @InheritInverseConfiguration(name="convert")
    CkhlwHjsqJbDO convertToDO(CkhlwHjsqJbDTO dto);

}
