package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.jk.ws.JkZalwcxApiManager;
import com.zjjcnt.project.ck.jk.ws.VoZalwcxqqHjgljbxx;
import com.zjjcnt.project.ck.jk.ws.ZalwcxqqHjgljbxxResultVo;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.XtZzsbxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.XtZzsbxxbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.XtZzsbxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxCzrkjbxxbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbxxbService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 自助设备信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 11:19:21
 */
@RequiredArgsConstructor
@Service
public class XtZzsbxxbServiceImpl extends AbstractBaseServiceImpl<XtZzsbxxbMapper, XtZzsbxxbDO, XtZzsbxxbDTO> implements XtZzsbxxbService {

    XtZzsbxxbConvert convert = XtZzsbxxbConvert.INSTANCE;

    private final HjxxCzrkjbxxbService hjxxCzrkjbxxbService;
    private final JkZalwcxApiManager jkZalwcxApiManager;

    @Override
    protected QueryWrapper genQueryWrapper(XtZzsbxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(XtZzsbxxbDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(XtZzsbxxbDO::getPcsmc, dto.getPcsmc(), StringUtils.isNotEmpty(dto.getPcsmc()));
        query.eq(XtZzsbxxbDO::getAppkey, dto.getAppkey(), StringUtils.isNotEmpty(dto.getAppkey()));
        query.eq(XtZzsbxxbDO::getSbip, dto.getSbip(), StringUtils.isNotEmpty(dto.getSbip()));
        query.eq(XtZzsbxxbDO::getSbmac, dto.getSbmac(), StringUtils.isNotEmpty(dto.getSbmac()));
        query.eq(XtZzsbxxbDO::getSbsfvpn, dto.getSbsfvpn(), StringUtils.isNotEmpty(dto.getSbsfvpn()));
        query.eq(XtZzsbxxbDO::getSbsydz, dto.getSbsydz(), StringUtils.isNotEmpty(dto.getSbsydz()));
        query.eq(XtZzsbxxbDO::getSbxh, dto.getSbxh(), StringUtils.isNotEmpty(dto.getSbxh()));
        query.eq(XtZzsbxxbDO::getSbxhmc, dto.getSbxhmc(), StringUtils.isNotEmpty(dto.getSbxhmc()));
        query.eq(XtZzsbxxbDO::getSblx, dto.getSblx(), StringUtils.isNotEmpty(dto.getSblx()));
        query.eq(XtZzsbxxbDO::getSbzwcjqbh1, dto.getSbzwcjqbh1(), StringUtils.isNotEmpty(dto.getSbzwcjqbh1()));
        query.eq(XtZzsbxxbDO::getSbzwcjqbh2, dto.getSbzwcjqbh2(), StringUtils.isNotEmpty(dto.getSbzwcjqbh2()));
        query.eq(XtZzsbxxbDO::getLicense, dto.getLicense(), StringUtils.isNotEmpty(dto.getLicense()));
        query.ge(XtZzsbxxbDO::getYxqkssj, dto.getYxqkssjStart(), StringUtils.isNotEmpty(dto.getYxqkssjStart()));
        query.le(XtZzsbxxbDO::getYxqkssj, dto.getYxqkssjEnd(), StringUtils.isNotEmpty(dto.getYxqkssjEnd()));
        query.ge(XtZzsbxxbDO::getYxqjssj, dto.getYxqjssjStart(), StringUtils.isNotEmpty(dto.getYxqjssjStart()));
        query.le(XtZzsbxxbDO::getYxqjssj, dto.getYxqjssjEnd(), StringUtils.isNotEmpty(dto.getYxqjssjEnd()));
        query.eq(XtZzsbxxbDO::getQybz, dto.getQybz(), StringUtils.isNotEmpty(dto.getQybz()));
        query.ge(XtZzsbxxbDO::getDjsj, dto.getDjsjStart(), StringUtils.isNotEmpty(dto.getDjsjStart()));
        query.le(XtZzsbxxbDO::getDjsj, dto.getDjsjEnd(), StringUtils.isNotEmpty(dto.getDjsjEnd()));
        query.eq(XtZzsbxxbDO::getZxyy, dto.getZxyy(), StringUtils.isNotEmpty(dto.getZxyy()));
        query.eq(XtZzsbxxbDO::getSfjy, dto.getSfjy(), StringUtils.isNotEmpty(dto.getSfjy()));
        query.eq(XtZzsbxxbDO::getSfjyip, dto.getSfjyip(), StringUtils.isNotEmpty(dto.getSfjyip()));
        query.eq(XtZzsbxxbDO::getSfjylicense, dto.getSfjylicense(), StringUtils.isNotEmpty(dto.getSfjylicense()));
        query.eq(XtZzsbxxbDO::getSfjyyxsj, dto.getSfjyyxsj(), StringUtils.isNotEmpty(dto.getSfjyyxsj()));
        query.eq(XtZzsbxxbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(XtZzsbxxbDO::getSbscgs, dto.getSbscgs(), StringUtils.isNotEmpty(dto.getSbscgs()));
        query.eq(XtZzsbxxbDO::getSbxsgs, dto.getSbxsgs(), StringUtils.isNotEmpty(dto.getSbxsgs()));
        query.eq(XtZzsbxxbDO::getSfyxsczp, dto.getSfyxsczp(), StringUtils.isNotEmpty(dto.getSfyxsczp()));
        query.eq(XtZzsbxxbDO::getSbsydmc, dto.getSbsydmc(), StringUtils.isNotEmpty(dto.getSbsydmc()));
        query.eq(XtZzsbxxbDO::getZddjrgmsfhm, ColumnUtils.encryptColumn(dto.getZddjrgmsfhm()), StringUtils.isNotEmpty(dto.getZddjrgmsfhm()));
        query.eq(XtZzsbxxbDO::getZddjrxm, ColumnUtils.encryptColumn(dto.getZddjrxm()), StringUtils.isNotEmpty(dto.getZddjrxm()));
        query.eq(XtZzsbxxbDO::getZlpzsbdwbh, dto.getZlpzsbdwbh(), StringUtils.isNotEmpty(dto.getZlpzsbdwbh()));
        query.eq(XtZzsbxxbDO::getSbzcgs, dto.getSbzcgs(), StringUtils.isNotEmpty(dto.getSbzcgs()));
        query.eq(XtZzsbxxbDO::getJd, dto.getJd(), StringUtils.isNotEmpty(dto.getJd()));
        query.eq(XtZzsbxxbDO::getWd, dto.getWd(), StringUtils.isNotEmpty(dto.getWd()));
        query.eq(XtZzsbxxbDO::getSbsydlbdm, dto.getSbsydlbdm(), StringUtils.isNotEmpty(dto.getSbsydlbdm()));
        query.eq(XtZzsbxxbDO::getTbxt, dto.getTbxt(), StringUtils.isNotEmpty(dto.getTbxt()));
        query.eq(XtZzsbxxbDO::getWdid, dto.getWdid(), StringUtils.isNotEmpty(dto.getWdid()));
        query.eq(XtZzsbxxbDO::getDwjgdm, dto.getDwjgdm(), StringUtils.isNotEmpty(dto.getDwjgdm()));
        query.eq(XtZzsbxxbDO::getDwjgmc, dto.getDwjgmc(), StringUtils.isNotEmpty(dto.getDwjgmc()));
        query.eq(XtZzsbxxbDO::getCkdwssdw, dto.getCkdwssdw(), StringUtils.isNotEmpty(dto.getCkdwssdw()));
        query.eq(XtZzsbxxbDO::getTsbzb, dto.getTsbzb(), StringUtils.isNotEmpty(dto.getTsbzb()));
        query.eq(XtZzsbxxbDO::getCkyhdlm, dto.getCkyhdlm(), StringUtils.isNotEmpty(dto.getCkyhdlm()));
        query.eq(XtZzsbxxbDO::getCkczyid, dto.getCkczyid(), Objects.nonNull(dto.getCkczyid()));
        query.eq(XtZzsbxxbDO::getSbdwbhold, dto.getSbdwbhold(), StringUtils.isNotEmpty(dto.getSbdwbhold()));
        query.eq(XtZzsbxxbDO::getSbdwbhnew, dto.getSbdwbhnew(), StringUtils.isNotEmpty(dto.getSbdwbhnew()));
        query.eq(XtZzsbxxbDO::getXxdz, dto.getXxdz(), StringUtils.isNotEmpty(dto.getXxdz()));
        query.eq(XtZzsbxxbDO::getSfzlpzsb, dto.getSfzlpzsb(), StringUtils.isNotEmpty(dto.getSfzlpzsb()));
        query.eq(XtZzsbxxbDO::getSfzdxq, dto.getSfzdxq(), StringUtils.isNotEmpty(dto.getSfzdxq()));
        query.eq(XtZzsbxxbDO::getSbyjm, dto.getSbyjm(), StringUtils.isNotEmpty(dto.getSbyjm()));
        query.eq(XtZzsbxxbDO::getSbmc, dto.getSbmc(), StringUtils.isNotEmpty(dto.getSbmc()));
        return query;
    }

    @Override
    public List<XtZzsbxxbDTO> listDzcZlpz(XtZzsbxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(XtZzsbxxbDO::getQybz, "1");
        query.eq(XtZzsbxxbDO::getSfzlpzsb, "1");
        query.isNotNull(XtZzsbxxbDO::getZlpzsbdwbh);
        query.isNull(XtZzsbxxbDO::getCsjcjzdbm);
        query.eq(XtZzsbxxbDO::getTbxt,"00");
        query.like(XtZzsbxxbDO::getZlpzsbdwbh,dto.getZlpzsbdwbh(),StringUtils.isNotEmpty(dto.getZlpzsbdwbh()));
        query.likeLeft(XtZzsbxxbDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));

        List<XtZzsbxxbDO> list = list(query);
        return convertToDTO(list);
    }

    @Override
    public XtZzsbxxbDTO convertToDTO(XtZzsbxxbDO xtZzsbxxbDO) {
        return convert.convert(xtZzsbxxbDO);
    }

    @Override
    public XtZzsbxxbDO convertToDO(XtZzsbxxbDTO xtZzsbxxbDTO) {
        return convert.convertToDO(xtZzsbxxbDTO);
    }

    @Override
    public XtZzsbxxbDTO findEnabledByAppkeyAndSbip(String appkey, String sbip) {
        XtZzsbxxbDTO dto = new XtZzsbxxbDTO();
        dto.setAppkey(appkey);
        dto.setSbip(sbip);
        dto.setQybz(Constants.YES);
        return find(dto);
    }

    @Override
    public XtZzsbxxbDTO findEnabledByAppkeyAndSbipAndSbmac(String appkey, String sbip, String sbmac) {
        XtZzsbxxbDTO dto = new XtZzsbxxbDTO();
        dto.setAppkey(appkey);
        dto.setSbip(sbip);
        dto.setSbmac(sbmac);
        dto.setQybz(Constants.YES);
        return find(dto);
    }

    @Override
    public XtZzsbxxbDTO findEnabledByAppkeyAndZlpzsbdwbh(String appkey, String zlpzsbdwbh) {
        XtZzsbxxbDTO dto = new XtZzsbxxbDTO();
        dto.setAppkey(appkey);
        dto.setZlpzsbdwbh(zlpzsbdwbh);
        dto.setQybz(Constants.YES);
        return find(dto);
    }


    @Override
    public XtZzsbxxbDTO insertZzsb(XtZzsbxxbDTO xtZzsbxxb) {
        XtZzsbxxbDTO exist = findEnabledByAppkeyAndSbip(xtZzsbxxb.getAppkey(), xtZzsbxxb.getSbip());
        if (Objects.nonNull(exist)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_ALREADY_EXIST, "该设备ip:" + xtZzsbxxb.getSbip()
                    + "已存在,请勿重复注册");
        }
        checkZddjr(xtZzsbxxb, null);

        XtZzsbxxbDTO sbyjmExist = findEnabledByAppkeyAndSbmac(CkZzjConstants.APP_KEY_CZRKZDYH, xtZzsbxxb.getSbmac());
        if (Objects.nonNull(sbyjmExist)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_ALREADY_EXIST);
        }

        xtZzsbxxb.setSbscgs(CkZzjConstants.SBGS_HTZN);
        xtZzsbxxb.setSbxsgs(CkZzjConstants.SBGS_HTZN);
        xtZzsbxxb.setSbzcgs(CkZzjConstants.SBGS_HTZN);
        xtZzsbxxb.setSfjyip(Constants.YES);
        xtZzsbxxb.setSfjylicense(Constants.YES);
        xtZzsbxxb.setSfjyyxsj(Constants.NO);
        xtZzsbxxb.setSfyxsczp(Constants.YES);

        String nowTime = ServerTimeUtils.getCurrentTime();
        String nowDate = ServerTimeUtils.getCurrentDate();
        xtZzsbxxb.setYxqkssj(nowDate);
        if (StringUtils.isBlank(xtZzsbxxb.getYxqjssj())) {
            xtZzsbxxb.setYxqjssj(DateTimeUtils.addYears(nowDate, 1));
        }
        xtZzsbxxb.setDjsj(nowTime);
        xtZzsbxxb.setQybz(Constants.YES);

        //sbxhmc为空时填入sbxh
        if (StringUtils.isBlank(xtZzsbxxb.getSbxhmc())) {
            xtZzsbxxb.setSbxhmc(xtZzsbxxb.getSbxh());
        }

        xtZzsbxxb.setCkczyid(SecurityUtils.getUserId());
        if (StringUtils.isBlank(xtZzsbxxb.getCkyhdlm())) {
            xtZzsbxxb.setCkyhdlm(SecurityUtils.getUsername());
        }

        xtZzsbxxb.setLicense(creatLicense(xtZzsbxxb.getSbip(), xtZzsbxxb.getYxqkssj(),
                xtZzsbxxb.getYxqjssj(), xtZzsbxxb.getSbzwcjqbh1(), xtZzsbxxb.getSbxh()));

        xtZzsbxxb.setSfjy(Constants.NO);
        if (!Constants.NO.equals(xtZzsbxxb.getSfjyip()) || !Constants.NO.equals(xtZzsbxxb.getSfjylicense())
                || !Constants.NO.equals(xtZzsbxxb.getSfjyyxsj())) {
            xtZzsbxxb.setSfjy(Constants.YES);
        }

        return insert(xtZzsbxxb);
    }

    @Override
    public XtZzsbxxbDTO updateZzsb(XtZzsbxxbDTO newZzsbxxb) {

        XtZzsbxxbDTO existZzsb = findById(newZzsbxxb.getId());

        if (Objects.isNull(existZzsb)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_NOT_FOUND, "设备id为:" + newZzsbxxb.getId() + "的设备不存在!");
        }

        checkZddjr(newZzsbxxb, existZzsb);

        newZzsbxxb.setCkczyid(SecurityUtils.getUserId());
        newZzsbxxb.setLicense(creatLicense(StringUtils.defaultIfEmpty(newZzsbxxb.getSbip(), existZzsb.getSbip()),
                StringUtils.defaultIfEmpty(newZzsbxxb.getYxqkssj(), existZzsb.getYxqkssj()),
                StringUtils.defaultIfEmpty(newZzsbxxb.getYxqjssj(), existZzsb.getYxqjssj()),
                StringUtils.defaultIfEmpty(newZzsbxxb.getSbzwcjqbh1(), existZzsb.getSbzwcjqbh1()),
                StringUtils.defaultIfEmpty(newZzsbxxb.getSbxh(), existZzsb.getSbxh())));
        this.update(newZzsbxxb);
        return newZzsbxxb;
    }

    @Override
    public XtZzsbxxbDTO findByZlpzsbdwbh(String zlpzsbdwbh) {
        if (StringUtils.isBlank(zlpzsbdwbh)) {
            return null;
        }
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(XtZzsbxxbDO::getQybz, Constants.YES);
        queryWrapper.eq(XtZzsbxxbDO::getZlpzsbdwbh, zlpzsbdwbh);
        return find(queryWrapper);
    }

    @Override
    public XtZzsbxxbDTO findEnabledByAppkeyAndSbmac(String appkey, String sbmac) {
        if (StringUtils.isBlank(sbmac)) {
            return null;
        }
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(XtZzsbxxbDO::getAppkey, appkey);
        queryWrapper.eq(XtZzsbxxbDO::getSbmac, sbmac);
        queryWrapper.eq(XtZzsbxxbDO::getQybz, Constants.YES);
        queryWrapper.orderBy(XtZzsbxxbDO::getDjsj, false);
        queryWrapper.limit(1);
        return find(queryWrapper);
    }

    /**
     * 校验终端登记人姓名和公民身份号码是否匹配
     * @param entity 新的设备信息
     * @param oldEntity 已存在的设备信息
     */
    private void checkZddjr(XtZzsbxxbDTO entity, XtZzsbxxbDTO oldEntity) {
        if (Objects.nonNull(oldEntity) && StringUtils.equals(entity.getZddjrgmsfhm(), oldEntity.getZddjrgmsfhm()) &&
                StringUtils.equals(entity.getZddjrxm(), oldEntity.getZddjrxm())) {
            return;
        }

        if (StringUtils.isEmpty(entity.getZddjrgmsfhm()) && StringUtils.isEmpty(entity.getZddjrxm())) {
            return;
        }
        String gmsfhm = entity.getZddjrgmsfhm();
        HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO = hjxxCzrkjbxxbService.findEnabledByGmsfhm(gmsfhm);

        if (Objects.nonNull(hjxxCzrkjbxxbDTO)) {
            if (!StringUtils.equals(entity.getZddjrxm(), hjxxCzrkjbxxbDTO.getXm())) {
                throw new ServiceException(CkZzjErrorCode.DEVICE_SAVE_ERROR, "公民身份号码或姓名不正确，请重新输入");
            }
        } else {
            VoZalwcxqqHjgljbxx zalwcxqqHjgljbxx = new VoZalwcxqqHjgljbxx();
            zalwcxqqHjgljbxx.setGmsfhm(gmsfhm);
            zalwcxqqHjgljbxx.setZt(SysadminConstants.GAB_RKXX_CX_ZT_QB);
            zalwcxqqHjgljbxx.setInvokeSys(CkZzjConstants.INVOKE_SYS_XC_ZZJ_ZZSBZC);
            ZalwcxqqHjgljbxxResultVo gabRkxx = jkZalwcxApiManager.getGabRkxx(zalwcxqqHjgljbxx);

            if (Objects.isNull(gabRkxx) || !StringUtils.equals(entity.getZddjrxm(), gabRkxx.getXm())) {
                throw new ServiceException(CkZzjErrorCode.DEVICE_SAVE_ERROR, "公民身份号码或姓名不正确，请重新输入");
            }
        }
    }

    private String creatLicense(String sbip, String yxqkssj, String yxqjssj, String sbzwcjqbh1, String sbxh) {
        String strLicense = "";
        try {
            JSONObject json = new JSONObject();
            json.put("ip", sbip);
            json.put("yxqkssj", yxqkssj);
            json.put("yxqjssj", "".equals(yxqjssj) ? null : yxqjssj);
            json.put("zwcjqbh", "".equals(sbzwcjqbh1) ? null : sbzwcjqbh1);
            json.put("sbxh", sbxh);
            strLicense = DigestUtils.md5Hex(json.toJSONString());
        } catch (Exception ignored) {
        }
        return strLicense;

    }

}
