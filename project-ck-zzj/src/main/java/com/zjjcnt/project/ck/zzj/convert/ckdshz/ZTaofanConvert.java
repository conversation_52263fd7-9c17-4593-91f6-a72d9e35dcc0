package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.ZTaofanDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.ZTaofanDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 逃犯Convert
*
* <AUTHOR>
* @date 2024-11-22 10:58:38
*/
@Mapper
public interface ZTaofanConvert {

    ZTaofanConvert INSTANCE = Mappers.getMapper(ZTaofanConvert.class);

    ZTaofanDTO convert(ZTaofanDO entity);

    @InheritInverseConfiguration(name="convert")
    ZTaofanDO convertToDO(ZTaofanDTO dto);

}
