package com.zjjcnt.project.ck.zzj.service.ck;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjbDTO;

/**
 * 指纹采集表Service
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
public interface ZjtZwcjbService extends IBaseService<ZjtZwcjbDTO> {

    /**
     * 新增不校验指纹采集器
     * @param zjtZwcjbDTO
     * @return
     */
    ZjtZwcjbDTO insertWithNotValidateZwcjqid(ZjtZwcjbDTO zjtZwcjbDTO);
}
