package com.zjjcnt.project.ck.zzj.service.ckdshz;

import com.zjjcnt.common.core.service.IBaseService;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbGabjmxxbDTO;

/**
 * Service
 *
 * <AUTHOR>
 * @date 2025-05-21 10:04:10
 */
public interface XtZzsbGabjmxxbService extends IBaseService<XtZzsbGabjmxxbDTO> {

    XtZzsbGabjmxxbDTO getConfig(Long sbid);

    XtZzsbGabjmxxbDTO findBySbid(Long sbid);
}