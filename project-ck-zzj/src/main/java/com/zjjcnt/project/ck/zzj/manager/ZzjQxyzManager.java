package com.zjjcnt.project.ck.zzj.manager;

import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.project.ck.sysadmin.dto.XtYhxxbDTO;
import com.zjjcnt.project.ck.sysadmin.service.XtYhxxbService;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjlsbService;
import com.zjjcnt.project.ck.zzj.web.response.GlyWithZwxxRes;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自助机鉴权生成器.
 *
 */
@Service
public class ZzjQxyzManager {

    @Autowired
    private XtYhxxbService xtYhxxbService;

    @Autowired
    private ZjtZwcjlsbService zjtZwcjlsbService;

    public List<GlyWithZwxxRes> listGlyWithZw(String dwdm){
        List<XtYhxxbDTO> yhxxbList = xtYhxxbService.listZwglyWithDwdm(dwdm);
        if (CollectionUtils.isEmpty(yhxxbList)) {
            throw new ServiceException(CkZzjErrorCode.ERR_ZWGLYWITHDWDM_NULL);
        }

        return yhxxbList.stream().map(xtYhxxbDTO -> {
            String gmsfhm = xtYhxxbDTO.getGmsfhm();
            if (StringUtils.isBlank(gmsfhm)) {
               return null;
            }
            ZjtZwcjlsbDTO zwDto = zjtZwcjlsbService.findNewZwByGmsfhm(gmsfhm);
            if (Objects.isNull(zwDto)){
               return null;
            }
            GlyWithZwxxRes temp = new GlyWithZwxxRes();
            temp.setYhid(xtYhxxbDTO.getYhid().toString());
            temp.setYhxm(zwDto.getXm());
            temp.setGmsfhm(gmsfhm);
            temp.setZwyzw(zwDto.getZwyzw());
            temp.setZwytzsj(Base64.encodeBase64String(zwDto.getZwyzwtzsj()));
            temp.setZwezw(zwDto.getZwezw());
            temp.setZwetzsj(Base64.encodeBase64String(zwDto.getZwezwtzsj()));
            return temp;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
