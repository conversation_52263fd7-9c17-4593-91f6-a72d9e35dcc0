package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxZpytbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZplsbSaveReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZpytbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZpytbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZpytbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZpytbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZpytbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZpytbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxZpytbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 照片原图表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface HjxxZpytbConvert {

    HjxxZpytbConvert INSTANCE = Mappers.getMapper(HjxxZpytbConvert.class);

    HjxxZpytbDTO convert(HjxxZpytbDO entity);

    @InheritInverseConfiguration(name="convert")
    HjxxZpytbDO convertToDO(HjxxZpytbDTO dto);

    HjxxZpytbDTO convertToDTO(HjxxZpytbPageReq req);

    HjxxZpytbDTO convertToDTO(HjxxZpytbCreateReq req);

    HjxxZpytbDTO convertToDTO(HjxxZpytbUpdateReq req);

    HjxxZpytbPageResp convertToPageResp(HjxxZpytbDTO dto);

    HjxxZpytbViewResp convertToViewResp(HjxxZpytbDTO dto);

    HjxxZpytbCreateResp convertToCreateResp(HjxxZpytbDTO dto);

    @Mapping(target = "zpwjlx", source = "zpytwjlx")
    HjxxZpytbDTO convertToDTO(HjxxZplsbSaveReq dto);

}
