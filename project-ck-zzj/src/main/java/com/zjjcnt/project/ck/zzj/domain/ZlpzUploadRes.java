package com.zjjcnt.project.ck.zzj.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 浙里拍照上传结果
 *
 * <AUTHOR>
 * @date 2024-12-11 15:28:00
 */
@Data
public class ZlpzUploadRes {

    @Schema(description = "是否成功")
    private Boolean success;

    @Schema(description = "错误消息")
    private String message;

    public static ZlpzUploadRes success() {
        return success(null);
    }

    public static ZlpzUploadRes success(String message) {
        return new ZlpzUploadRes(true, message);
    }

    public static ZlpzUploadRes fail(String message) {
        return new ZlpzUploadRes(false, message);
    }

    private ZlpzUploadRes(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
}
