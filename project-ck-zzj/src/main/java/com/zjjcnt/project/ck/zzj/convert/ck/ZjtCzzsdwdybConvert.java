package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtCzzsdwdybDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtCzzsdwdybCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtCzzsdwdybPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtCzzsdwdybUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtCzzsdwdybCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtCzzsdwdybPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtCzzsdwdybViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtCzzsdwdybDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 财政执收单位对应表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtCzzsdwdybConvert {

    ZjtCzzsdwdybConvert INSTANCE = Mappers.getMapper(ZjtCzzsdwdybConvert.class);

    ZjtCzzsdwdybDTO convert(ZjtCzzsdwdybDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtCzzsdwdybDO convertToDO(ZjtCzzsdwdybDTO dto);

    ZjtCzzsdwdybDTO convertToDTO(ZjtCzzsdwdybPageReq req);

    ZjtCzzsdwdybDTO convertToDTO(ZjtCzzsdwdybCreateReq req);

    ZjtCzzsdwdybDTO convertToDTO(ZjtCzzsdwdybUpdateReq req);

    ZjtCzzsdwdybPageResp convertToPageResp(ZjtCzzsdwdybDTO dto);

    ZjtCzzsdwdybViewResp convertToViewResp(ZjtCzzsdwdybDTO dto);

    ZjtCzzsdwdybCreateResp convertToCreateResp(ZjtCzzsdwdybDTO dto);

}
