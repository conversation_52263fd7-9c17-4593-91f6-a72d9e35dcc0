package com.zjjcnt.project.ck.zzj.third.crj.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 新全国人口库查询接口请求参数
 * 条件组合说明：
 *  1.身份证号码
 *  2.中文姓名+出生日期
 *
 * <AUTHOR>
 * @date 2024-08-09 15:21:00
 */
@Data
public class CrjQueryPeopleNewReq {

    /**
     * 非空 客户端 IP
     */
    @JsonProperty(value = "client_ip")
    private String clientIp;


    /**
     * 非空 客户端证书编号
     */
    @JsonProperty(value = "client_zsbh")
    private String clientZsbh;

    /**
     * 非空 客户端单位编号
     */
    @JsonProperty(value = "client_dwbh")
    private String clientDwbh;

    /**
     * 非空 客户端单位名称
     */
    @JsonProperty(value = "client_dwmc")
    private String clientDwmc;

    /**
     * 非空 客户端单位名称
     */
    @JsonProperty(value = "client_xm")
    private String clientXm;


    /**
     * 非空 客户端单位名称
     */
    @JsonProperty(value = "client_sfzh")
    private String clientSfzh;


    /**
     * 身份证号码（15/18 位）
     */
    @JsonProperty(value = "SFZH")
    private String sfzh;


    /**
     * 中文姓名
     */
    @JsonProperty(value = "XM")
    private String xm;


    /**
     * 出生日期 （格式"yyyyMMdd"）
     */
    @JsonProperty(value = "CSRQ")
    private String csrq;

}
