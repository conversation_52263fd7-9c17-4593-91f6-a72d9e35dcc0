package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.WwYwjkdzbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwYwjkdzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.WwYwjkdzbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.WwYwjkdzbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.WwYwjkdzbService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 业务接口对照表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@Service
public class WwYwjkdzbServiceImpl extends AbstractBaseServiceImpl<WwYwjkdzbMapper, WwYwjkdzbDO, WwYwjkdzbDTO> implements WwYwjkdzbService {

    WwYwjkdzbConvert convert = WwYwjkdzbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(WwYwjkdzbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(WwYwjkdzbDO::getBdywbh, dto.getBdywbh(), Objects.nonNull(dto.getBdywbh()));
        query.eq(WwYwjkdzbDO::getBdywmc, dto.getBdywmc(), Objects.nonNull(dto.getBdywmc()));
        query.eq(WwYwjkdzbDO::getCsid, dto.getCsid(), Objects.nonNull(dto.getCsid()));
        query.eq(WwYwjkdzbDO::getFhlx, dto.getFhlx(), Objects.nonNull(dto.getFhlx()));
        query.eq(WwYwjkdzbDO::getQybz, dto.getQybz(), Objects.nonNull(dto.getQybz()));
        return query;
    }

    @Override
    public WwYwjkdzbDTO convertToDTO(WwYwjkdzbDO wwYwjkdzbDO) {
        return convert.convert(wwYwjkdzbDO);
    }

    @Override
    public WwYwjkdzbDO convertToDO(WwYwjkdzbDTO wwYwjkdzbDTO) {
        return convert.convertToDO(wwYwjkdzbDTO);
    }
}
