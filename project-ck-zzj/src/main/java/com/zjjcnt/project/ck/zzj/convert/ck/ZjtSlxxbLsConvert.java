package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbLsDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSlxxbLsCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSlxxbLsPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSlxxbLsUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbLsCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbLsPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbLsViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtSlxxbLsDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZjtSlxxbLsExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtSlxxbLsConvert {

    ZjtSlxxbLsConvert INSTANCE = Mappers.getMapper(ZjtSlxxbLsConvert.class);

    ZjtSlxxbLsDTO convert(ZjtSlxxbLsDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtSlxxbLsDO convertToDO(ZjtSlxxbLsDTO dto);

    ZjtSlxxbLsDTO convertToDTO(ZjtSlxxbLsPageReq req);

    ZjtSlxxbLsDTO convertToDTO(ZjtSlxxbLsCreateReq req);

    ZjtSlxxbLsDTO convertToDTO(ZjtSlxxbLsUpdateReq req);

    ZjtSlxxbLsPageResp convertToPageResp(ZjtSlxxbLsDTO dto);

    ZjtSlxxbLsViewResp convertToViewResp(ZjtSlxxbLsDTO dto);

    ZjtSlxxbLsCreateResp convertToCreateResp(ZjtSlxxbLsDTO dto);

    ZjtSlxxbLsExp convertToExp(ZjtSlxxbLsDTO dto);

}
