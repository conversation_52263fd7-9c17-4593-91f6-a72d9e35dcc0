package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-05-07 11:35:02
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.HjywWjSjDTO
 */
@Data
@Table("hjyw_wj_sj")
public class HjywWjSjDO implements IdEntity<String> {
    private static final long serialVersionUID = -7049991134135135132L;

    /**
     *
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String wjsjbh;

    /**
     *
     */
    @Column(value = "wjsj")
    private byte[] wjsj;


    @Override
    public String getId() {
        return this.wjsjbh;
    }

    @Override
    public void setId(String id) {
        this.wjsjbh = id;
    }
}
