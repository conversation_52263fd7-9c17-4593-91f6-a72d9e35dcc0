package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-05-09 13:35:50
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjsbxxbDTO
 */
@Data
@Table("zjt_zwcjsbxxb")
public class ZjtZwcjsbxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = 7901832999282104357L;

    /**
     *
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String sbid;

    /**
     *
     */
    @Column(value = "zwcjqbsh")
    private String zwcjqbsh;

    /**
     *
     */
    @Column(value = "zwqdxtzch")
    private String zwqdxtzch;

    /**
     *
     */
    @Column(value = "sbmc")
    private String sbmc;

    /**
     *
     */
    @Column(value = "ppxh")
    private String ppxh;

    /**
     *
     */
    @Column(value = "djsj")
    private String djsj;

    /**
     *
     */
    @Column(value = "sbdwgajgjgdm")
    private String sbdwgajgjgdm;

    /**
     *
     */
    @Column(value = "sbdwgajgmc")
    private String sbdwgajgmc;

    /**
     *
     */
    @Column(value = "syztdm")
    private String syztdm;

    /**
     *
     */
    @Column(value = "qyrq")
    private String qyrq;

    /**
     *
     */
    @Column(value = "tyrq")
    private String tyrq;

    /**
     *
     */
    @Column(value = "sjgsdwdm")
    private String sjgsdwdm;

    /**
     *
     */
    @Column(value = "sjgsdwmc")
    private String sjgsdwmc;

    /**
     *
     */
    @Column(value = "babz")
    private String babz;

    /**
     *
     */
    @Column(value = "basj")
    private String basj;

    /**
     *
     */
    @Column(value = "fhdm")
    private String fhdm;

    /**
     *
     */
    @Column(value = "fhms")
    private String fhms;

    /**
     *
     */
    @Column(value = "sjbbh")
    private String sjbbh;

    /**
     *
     */
    @Column(value = "rksj")
    private String rksj;

    /**
     *
     */
    @Column(value = "jssjbbh")
    private String jssjbbh;


    @Override
    public String getId() {
        return this.sbid;
    }

    @Override
    public void setId(String id) {
        this.sbid = id;
    }
}
