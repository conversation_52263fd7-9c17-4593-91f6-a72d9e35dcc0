package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZzjBbgxpzDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjBbgxpzCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjBbgxpzPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjBbgxpzUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjBbgxpzCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjBbgxpzLatestVersionResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjBbgxpzPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjBbgxpzViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjBbgxpzDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZzjBbgxpzExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 自助机版本更新配置Convert
*
* <AUTHOR>
* @date 2024-07-30 15:27:57
*/
@Mapper
public interface ZzjBbgxpzConvert {

    ZzjBbgxpzConvert INSTANCE = Mappers.getMapper(ZzjBbgxpzConvert.class);

    ZzjBbgxpzDTO convert(ZzjBbgxpzDO entity);

    @InheritInverseConfiguration(name="convert")
    ZzjBbgxpzDO convertToDO(ZzjBbgxpzDTO dto);

    ZzjBbgxpzDTO convertToDTO(ZzjBbgxpzPageReq req);

    ZzjBbgxpzDTO convertToDTO(ZzjBbgxpzCreateReq req);

    ZzjBbgxpzDTO convertToDTO(ZzjBbgxpzUpdateReq req);

    ZzjBbgxpzPageResp convertToPageResp(ZzjBbgxpzDTO dto);

    ZzjBbgxpzViewResp convertToViewResp(ZzjBbgxpzDTO dto);

    ZzjBbgxpzLatestVersionResp convertToLatestVersionResp(ZzjBbgxpzDTO dto);

    ZzjBbgxpzCreateResp convertToCreateResp(ZzjBbgxpzDTO dto);

    ZzjBbgxpzExp convertToExp(ZzjBbgxpzDTO dto);

}
