package com.zjjcnt.project.ck.zzj.config;

import com.zjjcnt.project.ck.core.file.CkFileManager;
import com.zjjcnt.project.ck.core.file.DefaultFileObjectToOSSService;
import com.zjjcnt.project.ck.core.file.FileObjectService;
import com.zjjcnt.project.ck.core.file.property.AliyunOssProperties;
import com.zjjcnt.project.ck.core.file.property.FileTransportProperties;
import com.zjjcnt.project.ck.sysadmin.file.CkFileMetaServiceImpl;
import com.zjjcnt.project.ck.sysadmin.file.CkFileObjectToDbServiceImpl;
import com.zjjcnt.project.ck.sysadmin.file.RktYwslClysjFileManager;
import com.zjjcnt.project.ck.sysadmin.file.RktYwslClysjFileManagerImpl;
import com.zjjcnt.project.ck.zzj.file.*;
import com.zjjcnt.project.ck.zzj.file.properties.CkZzjFileTransportProperties;
import com.zjjcnt.project.ck.sysadmin.service.RktYwslCldzwjService;
import com.zjjcnt.project.ck.sysadmin.service.impl.RktYwslClysjServiceImpl;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import jakarta.annotation.Resource;

@Configuration
@EnableConfigurationProperties(CkZzjFileTransportProperties.class)
public class FileTransportAutoConfiguration {

    protected CkZzjFileTransportProperties properties;

    @Resource
    protected CkFileMetaServiceImpl ckFileMetaService;
    @Resource
    protected CkFileObjectToDbServiceImpl ckFileObjectToDbService;

    @Lazy
    @Resource
    private RktYwslClysjServiceImpl rktYwslClysjService;

    @Resource
    protected RktYwslCldzwjService rktYwslCldzwjService;
    @Resource
    protected ZpytFileObjectToDbServiceImpl zpytFileObjectToDbService;

    @Resource
    protected ZzjYztyZpFileObjectToDbServiceImpl zzjYztyZpFileObjectToDbService;

    public FileTransportAutoConfiguration(CkZzjFileTransportProperties properties) {
        this.properties = properties;
    }

    @Bean
    public CkFileManager ckFileManager() {
        CkFileManager fileManager = new CkFileManager();

        fileManager.setFileMetaService(ckFileMetaService);
        FileObjectService fileObjectService = getFileObjectService(properties.getCkFile(),
                ckFileObjectToDbService, properties.getOss());
        fileManager.setFileObjectService(fileObjectService);
        return fileManager;
    }

    @Bean(name = "zpytFileObjectService")
    public FileObjectService zpytFileObjectService() {
        return getFileObjectService(properties.getZpyt(), zpytFileObjectToDbService, properties.getOss());
    }

    @Bean(name = "zzjYztyZpFileObjectService")
    public FileObjectService zzjYztyZpFileObjectService() {
        return getFileObjectService(properties.getYztyzp(), zzjYztyZpFileObjectToDbService, properties.getOss());
    }

    @Bean(name = "ryzpxxFileObjectService")
    public FileObjectService ryzpxxFileObjectService() {
        AliyunOssProperties ossProperties = properties.getOss();
        ossProperties.setBucketName(properties.getRyzp().getOssBucketName());
        return new DefaultFileObjectToOSSService(ossProperties);
    }

    @Bean
    public RktYwslClysjFileManager rktYwslClysjFileManager() {
        RktYwslClysjFileManagerImpl fileManager = new RktYwslClysjFileManagerImpl();
        fileManager.setFileMetaService(rktYwslClysjService);

        FileObjectService fileObjectService = getFileObjectService(properties.getYwslcl(),
                rktYwslCldzwjService, properties.getOss());
        fileManager.setFileObjectService(fileObjectService);
        return fileManager;
    }

    private FileObjectService getFileObjectService(FileTransportProperties properties,
                                                   FileObjectService fileObjectToDbService,
                                                   AliyunOssProperties ossProperties) {
        FileObjectService fileObjectService;
        System.out.println("BucketName == > " + properties.getOssBucketName());
        fileObjectService = switch (properties.getType()) {
            case ALIYUN_OSS -> {
                ossProperties.setBucketName(properties.getOssBucketName());
                yield new DefaultFileObjectToOSSService(ossProperties);
            }
            case DATABASE -> fileObjectToDbService;
        };
        return fileObjectService;
    }

}
