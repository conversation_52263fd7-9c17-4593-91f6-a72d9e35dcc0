package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 临时身份证受理历史表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSllsbDTO
 */
@Crypto
@Data
@Table("zjt_lssfz_sllsb")
public class ZjtLssfzSllsbDO implements IdEntity<String> {
    private static final long serialVersionUID = 7804816947985519670L;

    /**
     * 临时受理历史ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String lssllsbid;

    /**
     * 临时受理ID
     */
    @Column(value = "lsslid")
    private String lsslid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 变更前卡号
     */
    @Column(value = "bgqkh")
    private String bgqkh;

    /**
     * 变更后卡号
     */
    @Column(value = "bghkh")
    private String bghkh;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     *
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作员IP
     */
    @Column(value = "czyip")
    private String czyip;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;


    @Override
    public String getId() {
        return this.lssllsbid;
    }

    @Override
    public void setId(String id) {
        this.lssllsbid = id;
    }
}
