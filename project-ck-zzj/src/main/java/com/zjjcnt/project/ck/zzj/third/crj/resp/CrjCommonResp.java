package com.zjjcnt.project.ck.zzj.third.crj.resp;

import lombok.Data;

import java.util.Objects;

/**
 * 返回结果
 *
 * <AUTHOR>
 * @date 2024-08-09 09:45:00
 */
@Data
public class CrjCommonResp<T> {

    /**
     * 是否成功。0-失败，1-成功
     */
    private Integer success;

    /**
     * 状态代码用于区分错误类型。共7位长，格式为“CRJ+4位数字”，详见0业务代码说明
     */
    private String state;


    /**
     * 返回错误信息文本
     */
    private String message;

    /**
     * Object
     */
    private T data;

    public boolean isSuccess() {
        return Objects.equals(1, success);
    }


}

