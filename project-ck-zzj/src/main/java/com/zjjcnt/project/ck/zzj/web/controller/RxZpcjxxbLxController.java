package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.RxZpcjxxbLxConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.RxZpcjxxbLxDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.RxZpcjxxbLxCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.RxZpcjxxbLxCreateResp;
import com.zjjcnt.project.ck.zzj.service.ck.RxZpcjxxbLxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 离线照片采集信息表前端控制器
*
* <AUTHOR>
* @date 2024-06-21 09:30:28
*/

@Tag(name = "离线照片采集信息表")
@RestController
@RequestMapping("/rxZpcjxxbLx")
public class RxZpcjxxbLxController extends AbstractCrudController<RxZpcjxxbLxDTO> {

    @Autowired
    private RxZpcjxxbLxService rxZpcjxxbLxService;

    @Override
    protected IBaseService getService() {
        return rxZpcjxxbLxService;
    }

//    @GetMapping("page")
//    @Operation(summary = "查询离线照片采集信息表")
//    public CommonResult<PageResult<RxZpcjxxbLxPageResp>> page(RxZpcjxxbLxPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, RxZpcjxxbLxConvert.INSTANCE::convertToDTO, RxZpcjxxbLxConvert.INSTANCE::convertToPageResp);
//    }
//
//    @GetMapping("view")
//    @Operation(summary = "查看离线照片采集信息表详情")
//    public CommonResult<RxZpcjxxbLxViewResp> view(Long id) {
//        return super.view(id, RxZpcjxxbLxConvert.INSTANCE::convertToViewResp);
//    }

    @PostMapping("create")
    @Operation(summary = "新增离线照片采集信息表")
    public CommonResult<RxZpcjxxbLxCreateResp> create(@Validated @RequestBody RxZpcjxxbLxCreateReq req) {
        return super.create(req, RxZpcjxxbLxConvert.INSTANCE::convertToDTO, RxZpcjxxbLxConvert.INSTANCE::convertToCreateResp);
    }

//    @PostMapping("update")
//    @Operation(summary = "编辑离线照片采集信息表")
//    public CommonResult<Boolean> update(@RequestBody RxZpcjxxbLxUpdateReq req) {
//        return super.update(req, RxZpcjxxbLxConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除离线照片采集信息表")
//    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
//        return super.delete(id);
//    }
//
}
