package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 费用支付流水表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtFyzflsbDTO
 */
@Crypto
@Data
@Table("zjt_fyzflsb")
public class ZjtFyzflsbDO implements IdEntity<String> {

    @Serial
    private static final long serialVersionUID = -1328901485721225379L;

    /**
     * 收费流水id
     */
    @Id(keyType = KeyType.None)
    private String sflsid;

    /**
     * 业务表名称
     */
    @Column(value = "ywbmc")
    private String ywbmc;

    /**
     * 业务表编号（主键）
     */
    @Column(value = "ywbbh")
    private String ywbbh;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 人员内部id
     */
    @Column(value = "rynbid")
    private String rynbid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 收费金额
     */
    @Column(value = "sfje")
    private BigDecimal sfje;

    /**
     * 数据归属单位代码
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     * 操作时间
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     * 操作员单位代码
     */
    @Column(value = "czydwdm")
    private String czydwdm;

    /**
     * 操作员单位名称
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 是否收费·
     */
    @Column(value = "sfsf")
    private String sfsf;

    /**
     * 收费业务类型
     */
    @Column(value = "sfywlx")
    private String sfywlx;

    /**
     * 缴款单号
     */
    @Column(value = "jkdh")
    private String jkdh;

    /**
     * 缴款人身份号码
     */
    @Column(value = "jkrsfhm")
    private String jkrsfhm;

    /**
     * 缴款人联系电话
     */
    @Crypto
    @Column(value = "jkrlxdh")
    private String jkrlxdh;

    /**
     * 收费业务类型名称
     */
    @Crypto
    @Column(value = "sfywlxmc")
    private String sfywlxmc;

    /**
     * 合并缴款单号
     */
    @Column(value = "hbjkdh")
    private String hbjkdh;

    /**
     * 受理地分局数据归属单位代码
     */
    @Column(value = "sldfjsjgsdwdm")
    private String sldfjsjgsdwdm;

    /**
     * 受理地分局数据归属单位名称
     */
    @Column(value = "sldfjsjgsdwmc")
    private String sldfjsjgsdwmc;

    /**
     * 执收单位编码
     */
    @Column(value = "zsdwdm")
    private String zsdwdm;

    /**
     * 执收单位名称
     */
    @Column(value = "zsdwmc")
    private String zsdwmc;

    /**
     * 行政区划编码
     */
    @Column(value = "xzqhdm")
    private String xzqhdm;

    /**
     * 执收项目编码
     */
    @Crypto
    @Column(value = "zsxmbm")
    private String zsxmbm;

    /**
     * 执收项目名称
     */
    @Crypto
    @Column(value = "zsxmmc")
    private String zsxmmc;

    /**
     * 收费单价
     */
    @Column(value = "sfdj")
    private BigDecimal sfdj;

    /**
     * 收费数量
     */
    @Column(value = "sfsl")
    private Integer sfsl;

    /**
     * 退费人员id
     */
    @Column(value = "tfryid")
    private String tfryid;

    /**
     * 退费人员姓名
     */
    @Crypto
    @Column(value = "tfryxm")
    private String tfryxm;

    /**
     * 退费人员单位代码
     */
    @Column(value = "tfrydwdm")
    private String tfrydwdm;

    /**
     * 退费人员单位名称
     */
    @Column(value = "tfrydwmc")
    private String tfrydwmc;

    /**
     * 退费时间
     */
    @Column(value = "tfsj")
    private String tfsj;

    /**
     * 缴款单来源渠道编号
     */
    @Column(value = "jkdlyqdbh")
    private String jkdlyqdbh;

    /**
     * 服务对象
     */
    @Column(value = "fwdx")
    private String fwdx;

    /**
     * 非现金状态
     */
    @Column(value = "fxjzt")
    private String fxjzt;

    /**
     * 收费信息来源
     */
    @Column(value = "sfxxly")
    private String sfxxly;
    /**
     * 收费信息来源
     */
    @Column(value = "fxjsqid")
    private String fxjsqid;
    /**
     * 非现金申请时间
     */
    @Column(value = "fxjsqsj")
    private String fxjsqsj;
    /**
     * 身份证受理时间
     */
    @Column(value = "sfzslsj")
    private String sfzslsj;
    /**
     * 非现金支付时间
     */
    @Column(value = "fxjzfsj")
    private  String fxjzfsj;

    @Override
    public String getId() {
        return this.sflsid;
    }

    @Override
    public void setId(String id) {
        this.sflsid = id;
    }
}
