package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.CkhlwHjsqJbDTO
 */
@Crypto
@Data
@Table("ckhlw_hjsq_jb")
public class CkhlwHjsqJbDO implements IdEntity<String> {
    private static final long serialVersionUID = -2864384439084926876L;

    /**
     * 互联网申请ID
     */
    @Id(keyType = KeyType.None)
    private String hlwsqid;

    /**
     * 申请类型
     */
    @Column(value = "sqlx")
    private String sqlx;

    /**
     * 业务编码
     */
    @Column(value = "ywbm")
    private String ywbm;

    /**
     * 数据来源
     */
    @Column(value = "sjly")
    private String sjly;

    /**
     * 第三方主键
     */
    @Column(value = "projectid")
    private String projectid;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 申请人公民身份号码
     */
    @Crypto
    @Column(value = "sqrgmsfhm")
    private String sqrgmsfhm;

    /**
     * 申请人姓名
     */
    @Crypto
    @Column(value = "sqrxm")
    private String sqrxm;

    /**
     * 申请人联系电话
     */
    @Crypto
    @Column(value = "sqrlxdh")
    private String sqrlxdh;

    /**
     * 申请人地址
     */
    @Column(value = "sqrdz")
    private String sqrdz;

    /**
     * 办理方式
     */
    @Column(value = "blfs")
    private String blfs;

    /**
     * 收件人姓名
     */
    @Crypto
    @Column(value = "sjrxm")
    private String sjrxm;

    /**
     * 收件人联系电话
     */
    @Crypto
    @Column(value = "sjrlxdh")
    private String sjrlxdh;

    /**
     * 收件省市县区
     */
    @Column(value = "sjssxq")
    private String sjssxq;

    /**
     * 收件地址
     */
    @Column(value = "sjdz")
    private String sjdz;

    /**
     * 紧急联系人姓名
     */
    @Crypto
    @Column(value = "jjlxrxm")
    private String jjlxrxm;

    /**
     * 紧急联系人公民身份号码
     */
    @Crypto
    @Column(value = "jjlxrgmsfhm")
    private String jjlxrgmsfhm;

    /**
     * 紧急联系人联系电话
     */
    @Crypto
    @Column(value = "jjlxrlxdh")
    private String jjlxrlxdh;

    /**
     * 随迁人数量
     */
    @Column(value = "sqrsl")
    private Integer sqrsl;

    /**
     * 申请状态
     */
    @Column(value = "sqzt")
    private String sqzt;

    /**
     * 审核标志
     */
    @Column(value = "shbz")
    private String shbz;

    /**
     * 审核结果
     */
    @Column(value = "shjg")
    private String shjg;

    /**
     * 审核人ID
     */
    @Column(value = "shrid")
    private String shrid;

    /**
     * 审核人姓名
     */
    @Crypto
    @Column(value = "shrxm")
    private String shrxm;

    /**
     * 审核人IP
     */
    @Column(value = "shrip")
    private String shrip;

    /**
     *
     */
    @Column(value = "shsj")
    private String shsj;

    /**
     * 有效标志
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     * 创建人
     */
    @Column(value = "cjr")
    private String cjr;

    /**
     * 创建人IP
     */
    @Column(value = "cjrip")
    private String cjrip;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 修改人
     */
    @Column(value = "xgr")
    private String xgr;

    /**
     * 修改人IP
     */
    @Column(value = "xgrip")
    private String xgrip;

    /**
     * 修改时间
     */
    @Column(value = "xgsj")
    private String xgsj;

    /**
     *
     */
    @Column(value = "ywtable")
    private String ywtable;

    /**
     *
     */
    @Column(value = "bustype")
    private String bustype;

    /**
     *
     */
    @Column(value = "relbusid")
    private String relbusid;

    /**
     *
     */
    @Column(value = "applyfrom")
    private String applyfrom;

    /**
     * 确认结果
     */
    @Column(value = "qrjg")
    private String qrjg;

    /**
     * 确认描述
     */
    @Column(value = "qrms")
    private String qrms;

    /**
     * 审批业务受理号
     */
    @Column(value = "spywslh")
    private String spywslh;

    /**
     * 第三方用户名称
     */
    @Column(value = "username")
    private String username;

    /**
     * 预约办理时间
     */
    @Column(value = "yyblsj")
    private String yyblsj;

    /**
     * 政务网大厅编号
     */
    @Column(value = "hallid")
    private String hallid;

    /**
     * 政务网大厅名称
     */
    @Column(value = "hallname")
    private String hallname;

    /**
     *
     */
    @Column(value = "jkdh")
    private String jkdh;

    /**
     *
     */
    @Column(value = "tbcxbz")
    private String tbcxbz;

    /**
     * 政务大厅用户编号
     */
    @Column(value = "halluserid")
    private String halluserid;

    /**
     * 政务大厅用户名称
     */
    @Column(value = "hallusername")
    private String hallusername;

    /**
     * 申请时间
     */
    @Column(value = "sqsj")
    private String sqsj;

    /**
     * 受理时间
     */
    @Column(value = "slsj")
    private String slsj;

    /**
     * 特别程序开始时间
     */
    @Column(value = "tbcxkssj")
    private String tbcxkssj;

    /**
     * 特别程序结束时间
     */
    @Column(value = "tbcxjssj")
    private String tbcxjssj;

    /**
     * 办结时间
     */
    @Column(value = "bjsj")
    private String bjsj;


    @Override
    public String getId() {
        return this.hlwsqid;
    }

    @Override
    public void setId(String id) {
        this.hlwsqid = id;
    }
}
