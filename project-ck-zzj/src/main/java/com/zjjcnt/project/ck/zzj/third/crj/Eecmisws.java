
package com.zjjcnt.project.ck.zzj.third.crj;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 *
 */
@WebServiceClient(name = "eecmisws", targetNamespace = "http://e3.org/eecmisws/soapws", wsdlLocation = "http://10.118.8.4/eecmisws/services/eecmisws?wsdl")
//@WebServiceClient(name = "eecmisws", targetNamespace = "http://e3.org/eecmisws/soapws", wsdlLocation = "http://10.118.8.144/eecmisws/services/eecmisws?wsdl")
public class Eecmisws extends Service {

    private final static URL EECMISWS_WSDL_LOCATION;
    private final static WebServiceException EECMISWS_EXCEPTION;
    private final static QName EECMISWS_QNAME = new QName("http://e3.org/eecmisws/soapws", "eecmisws");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
//            url = new URL("http://10.118.8.144/eecmisws/services/eecmisws?wsdl");
            url = new URL("http://10.118.8.4/eecmisws/services/eecmisws?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        EECMISWS_WSDL_LOCATION = url;
        EECMISWS_EXCEPTION = e;
    }

    public Eecmisws() {
        super(__getWsdlLocation(), EECMISWS_QNAME);
    }

    public Eecmisws(WebServiceFeature... features) {
        super(__getWsdlLocation(), EECMISWS_QNAME, features);
    }

    public Eecmisws(URL wsdlLocation) {
        super(wsdlLocation, EECMISWS_QNAME);
    }

    public Eecmisws(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, EECMISWS_QNAME, features);
    }

    public Eecmisws(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public Eecmisws(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     *
     * @return
     *     returns EecmiswsPortType
     */
    @WebEndpoint(name = "eecmiswsHttpSoap11Endpoint")
    public EecmiswsPortType getEecmiswsHttpSoap11Endpoint() {
        return super.getPort(new QName("http://e3.org/eecmisws/soapws", "eecmiswsHttpSoap11Endpoint"), EecmiswsPortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns EecmiswsPortType
     */
    @WebEndpoint(name = "eecmiswsHttpSoap11Endpoint")
    public EecmiswsPortType getEecmiswsHttpSoap11Endpoint(WebServiceFeature... features) {
        return super.getPort(new QName("http://e3.org/eecmisws/soapws", "eecmiswsHttpSoap11Endpoint"), EecmiswsPortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (EECMISWS_EXCEPTION!= null) {
            throw EECMISWS_EXCEPTION;
        }
        return EECMISWS_WSDL_LOCATION;
    }

}
