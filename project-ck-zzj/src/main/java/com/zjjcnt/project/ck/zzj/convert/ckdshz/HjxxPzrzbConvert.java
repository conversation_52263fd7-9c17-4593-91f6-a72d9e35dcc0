package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxPzrzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxPzrzbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxPzrzbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxPzrzbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxPzrzbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxPzrzbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxPzrzbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxPzrzbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 拍照日志信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface HjxxPzrzbConvert {

    HjxxPzrzbConvert INSTANCE = Mappers.getMapper(HjxxPzrzbConvert.class);

    HjxxPzrzbDTO convert(HjxxPzrzbDO entity);

    @InheritInverseConfiguration(name="convert")
    HjxxPzrzbDO convertToDO(HjxxPzrzbDTO dto);

    HjxxPzrzbDTO convertToDTO(HjxxPzrzbPageReq req);

    HjxxPzrzbDTO convertToDTO(HjxxPzrzbCreateReq req);

    HjxxPzrzbDTO convertToDTO(HjxxPzrzbUpdateReq req);

    HjxxPzrzbPageResp convertToPageResp(HjxxPzrzbDTO dto);

    HjxxPzrzbViewResp convertToViewResp(HjxxPzrzbDTO dto);

    HjxxPzrzbCreateResp convertToCreateResp(HjxxPzrzbDTO dto);

}
