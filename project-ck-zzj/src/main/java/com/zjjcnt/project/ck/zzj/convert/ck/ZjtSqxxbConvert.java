package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSqxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSqxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSqxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSqxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSqxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSqxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSqxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtSqxxbDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZjtSqxxbExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 证件业务申请信息表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtSqxxbConvert {

    ZjtSqxxbConvert INSTANCE = Mappers.getMapper(ZjtSqxxbConvert.class);

    ZjtSqxxbDTO convert(ZjtSqxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtSqxxbDO convertToDO(ZjtSqxxbDTO dto);

    ZjtSqxxbDTO convertToDTO(ZjtSqxxbPageReq req);

    ZjtSqxxbDTO convertToDTO(ZjtSqxxbCreateReq req);

    ZjtSqxxbDTO convertToDTO(ZjtSqxxbUpdateReq req);

    ZjtSqxxbPageResp convertToPageResp(ZjtSqxxbDTO dto);

    ZjtSqxxbViewResp convertToViewResp(ZjtSqxxbDTO dto);

    ZjtSqxxbCreateResp convertToCreateResp(ZjtSqxxbDTO dto);

    ZjtSqxxbExp convertToExp(ZjtSqxxbDTO dto);

}
