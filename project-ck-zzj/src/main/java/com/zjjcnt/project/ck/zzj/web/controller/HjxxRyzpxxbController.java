package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.core.file.FileObjectService;
import com.zjjcnt.project.ck.core.file.domain.DefaultFileMeta;
import com.zjjcnt.project.ck.sysadmin.utils.CkCommonUtils;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxRyzpxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxRyzpxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxRyzpxxbViewResp;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxRyzpxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人员照片信息表前端控制器
 *
 * <AUTHOR>
 * @date 2024-05-07 11:35:02
 */

@Tag(name = "人员照片信息表")
@RestController
@RequestMapping("/hjxxRyzpxxb")
public class HjxxRyzpxxbController extends AbstractCrudController<HjxxRyzpxxbDTO> {

    @Autowired
    private HjxxRyzpxxbService hjxxRyzpxxbService;

    @Autowired
    @Qualifier("ryzpxxFileObjectService")
    private FileObjectService fileObjectService;

    @Override
    protected IBaseService getService() {
        return hjxxRyzpxxbService;
    }
//
//    @GetMapping("page")
//    @Operation(summary = "查询人员照片信息表")
//    public CommonResult<PageResult<HjxxRyzpxxbPageResp>> page(HjxxRyzpxxbPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, HjxxRyzpxxbConvert.INSTANCE::convertToDTO, HjxxRyzpxxbConvert.INSTANCE::convertToPageResp);
//    }

    @GetMapping("view")
    @Operation(summary = "查看人员照片信息表详情 F1002")
    public CommonResult<HjxxRyzpxxbViewResp> view(Long id) {
        HjxxRyzpxxbViewResp ryzpxxbViewResp = doView(id, HjxxRyzpxxbConvert.INSTANCE::convertToViewResp);
        if (StringUtils.isBlank(ryzpxxbViewResp.getZp()) && StringUtils.isNotBlank(ryzpxxbViewResp.getZpsjdz())) {
            DefaultFileMeta fileMeta = new DefaultFileMeta();
            fileMeta.setFileLocation(ryzpxxbViewResp.getZpsjdz());
            ryzpxxbViewResp.setZp(CkCommonUtils.encodeBase64(fileObjectService.getData(fileMeta)));
        }
        return CommonResult.success(ryzpxxbViewResp);
    }

//    @PostMapping("create")
//    @Operation(summary = "新增人员照片信息表")
//    public CommonResult<HjxxRyzpxxbCreateResp> create(@RequestBody HjxxRyzpxxbCreateReq req) {
//        return super.create(req, HjxxRyzpxxbConvert.INSTANCE::convertToDTO, HjxxRyzpxxbConvert.INSTANCE::convertToCreateResp);
//    }
//
//    @PostMapping("update")
//    @Operation(summary = "编辑人员照片信息表")
//    public CommonResult<Boolean> update(@RequestBody HjxxRyzpxxbUpdateReq req) {
//        return super.update(req, HjxxRyzpxxbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除人员照片信息表")
//    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
//        return super.delete(id);
//    }

}
