package com.zjjcnt.project.ck.zzj.convert;

import com.zjjcnt.project.ck.zzj.third.rop.req.*;
import com.zjjcnt.project.ck.zzj.third.rop.resp.KssfzBzzgscRopResp;
import com.zjjcnt.project.ck.zzj.third.rop.resp.KssfzJfjghqRopResp;
import com.zjjcnt.project.ck.zzj.third.rop.resp.KssfzSlxxUploadRopResp;
import com.zjjcnt.project.ck.zzj.third.rop.resp.KssfzWcnscbzzgscRopResp;
import com.zjjcnt.project.ck.zzj.web.request.*;
import com.zjjcnt.project.ck.zzj.web.response.KssfzBzzgscResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzJfjghqResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzSlxxUploadResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzWcnscbzzgscResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 跨省身份证Convert
 *
 * <AUTHOR>
 * @date 2024-10-16 09:42:00
 */
@Mapper
public interface KssfzConvert {

    KssfzConvert INSTANCE = Mappers.getMapper(KssfzConvert.class);

    KssfzBzzgscRopReq convertBzzgscRopReq(KssfzBzzgscReq kssfzBzzgscReq);

    KssfzWcnscbzzgscRopReq convertWcnscbzzgscRopReq(KssfzWcnscbzzgscReq kssfzWcnscbzzgscReq);

    KssfzBzzgscResp convertBzzgscResp(KssfzBzzgscRopResp kssfzBzzgscRopResp);

    KssfzWcnscbzzgscResp convertWcnscbzzgscResp(KssfzWcnscbzzgscRopResp kssfzWcnscbzzgscRopResp);

    KssfzSlxxUploadRopReq convertSlxxUploadReq(KssfzSlxxUploadReq kssfzSlxxUploadReq);

    KssfzClxxUploadRopReq convertClxxUploadReq(KssfzClxxUploadReq kssfzClxxUploadReq);

    List<KssfzClxxUploadRopReq> convertClxxUploadReq(List<KssfzClxxUploadReq> kssfzClxxUploadList);

    KssfzSlxxUploadResp convertSlxxUploadResp(KssfzSlxxUploadRopResp kssfzSlxxUploadRopResp);

    KssfzJfjghqRopReq convertJfjghqRopReq(KssfzJfjghqReq kssfzBzzgscReq);

    KssfzJfjghqResp convertJfjghqResp(KssfzJfjghqRopResp kssfzJfjghqRopResp);

    KssfzSljstjRopReq convertSljstjRopReq(KssfzSljstjReq kssfzSljstjReq);

}
