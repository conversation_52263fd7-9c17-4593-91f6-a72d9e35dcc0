package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjYhsbpzbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYhsbpzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYhsbpzbBatchSaveReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYhsbpzbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYhsbpzbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYhsbpzbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYhsbpzbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYhsbpzbListResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYhsbpzbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYhsbpzbViewResp;
import com.zjjcnt.project.ck.zzj.manager.ZzjYhsbpzbManager;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjYhsbpzbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
* 自助用户设备配置表前端控制器
*
* <AUTHOR>
* @date 2024-07-19 14:06:40
*/
@RequiredArgsConstructor
@Tag(name = "自助用户设备配置表")
@RestController
@RequestMapping("/zzjYhsbpzb")
public class ZzjYhsbpzbController extends AbstractCrudController<ZzjYhsbpzbDTO> {

    private final ZzjYhsbpzbService zzjYhsbpzbService;
    private final ZzjYhsbpzbManager zzjYhsbpzbManager;

    @Override
    protected IBaseService getService() {
        return zzjYhsbpzbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询自助用户设备配置表")
    public CommonResult<PageResult<ZzjYhsbpzbPageResp>> page(ZzjYhsbpzbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZzjYhsbpzbConvert.INSTANCE::convertToDTO, ZzjYhsbpzbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看自助用户设备配置表详情")
    public CommonResult<ZzjYhsbpzbViewResp> view(Long id) {
        return super.view(id, ZzjYhsbpzbConvert.INSTANCE::convertToViewResp);
    }

    @GetMapping("listYhsbpz")
    @Operation(summary = "查询用户设备配置列表")
    public CommonResult<List<ZzjYhsbpzbListResp>> listYhsbpz(Long yhid) {
        if (Objects.isNull(yhid)) {
            yhid = SecurityUtils.getUserId();
        }
        return CommonResult.success(zzjYhsbpzbManager.listYhsbpz(yhid));
    }

    @PostMapping("batchSave")
    @Operation(summary = "用户设备配置批量保存")
    public CommonResult<Boolean> batchSave(@Validated @RequestBody ZzjYhsbpzbBatchSaveReq zzjYhsbpzbBatchSaveReq) {
        zzjYhsbpzbManager.batchSave(ZzjYhsbpzbConvert.INSTANCE.convertToDTO(zzjYhsbpzbBatchSaveReq.getYhsbpzbList()));
        return CommonResult.success(true);
    }

    @PostMapping("create")
    @Operation(summary = "新增自助用户设备配置表")
    public CommonResult<ZzjYhsbpzbCreateResp> create(@Validated @RequestBody ZzjYhsbpzbCreateReq req) {
        return super.create(req, ZzjYhsbpzbConvert.INSTANCE::convertToDTO, ZzjYhsbpzbConvert.INSTANCE::convertToCreateResp);
    }

    @PostMapping("update")
    @Operation(summary = "编辑自助用户设备配置表")
    public CommonResult<Boolean> update(@Validated @RequestBody ZzjYhsbpzbUpdateReq req) {
        return super.update(req, ZzjYhsbpzbConvert.INSTANCE::convertToDTO);
    }

    @DeleteMapping(value = "delete")
    @Operation(summary = "删除自助用户设备配置表")
    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
        return super.delete(id);
    }

//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(ZzjYhsbpzbExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出自助用户设备配置表")
//    public void export(ZzjYhsbpzbPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "自助用户设备配置表" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, ZzjYhsbpzbExp.class,
//                    ZzjYhsbpzbConvert.INSTANCE::convertToDTO, ZzjYhsbpzbConvert.INSTANCE::convertToExp, response);
//    }

}
