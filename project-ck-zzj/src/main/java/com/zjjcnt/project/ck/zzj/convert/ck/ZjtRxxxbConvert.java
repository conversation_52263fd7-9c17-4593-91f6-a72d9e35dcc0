package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtRxxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtRxxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtRxxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 人像信息表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtRxxxbConvert {

    ZjtRxxxbConvert INSTANCE = Mappers.getMapper(ZjtRxxxbConvert.class);

    ZjtRxxxbDTO convert(ZjtRxxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtRxxxbDO convertToDO(ZjtRxxxbDTO dto);

//    ZjtRxxxbDTO convertToDTO(ZjtRxxxbPageReq req);
//
//    ZjtRxxxbDTO convertToDTO(ZjtRxxxbCreateReq req);
//
//    ZjtRxxxbDTO convertToDTO(ZjtRxxxbUpdateReq req);
//
//    ZjtRxxxbPageResp convertToPageResp(ZjtRxxxbDTO dto);

    ZjtRxxxbViewResp convertToViewResp(ZjtRxxxbDTO dto);
//
//    ZjtRxxxbCreateResp convertToCreateResp(ZjtRxxxbDTO dto);

}
