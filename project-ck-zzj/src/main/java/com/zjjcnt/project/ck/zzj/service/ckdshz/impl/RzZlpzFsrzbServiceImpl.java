package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.RzZlpzFsrzbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.RzZlpzFsrzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RzZlpzFsrzbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.RzZlpzFsrzbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RzZlpzFsrzbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-09-23 16:21:37
 */
@Service
public class RzZlpzFsrzbServiceImpl extends AbstractBaseServiceImpl<RzZlpzFsrzbMapper, RzZlpzFsrzbDO, RzZlpzFsrzbDTO> implements RzZlpzFsrzbService {

    RzZlpzFsrzbConvert convert = RzZlpzFsrzbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(RzZlpzFsrzbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(RzZlpzFsrzbDO::getZpxxid, dto.getZpxxid(), Objects.nonNull(dto.getZpxxid()));
        query.eq(RzZlpzFsrzbDO::getSbid, dto.getSbid(), Objects.nonNull(dto.getSbid()));
        query.ge(RzZlpzFsrzbDO::getFssj, dto.getFssjStart(), StringUtils.isNotEmpty(dto.getFssjStart()));
        query.le(RzZlpzFsrzbDO::getFssj, dto.getFssjEnd(), StringUtils.isNotEmpty(dto.getFssjEnd()));
        query.eq(RzZlpzFsrzbDO::getFhdm, dto.getFhdm(), StringUtils.isNotEmpty(dto.getFhdm()));
        query.eq(RzZlpzFsrzbDO::getDtbxt, dto.getDtbxt(), StringUtils.isNotEmpty(dto.getDtbxt()));
        query.eq(RzZlpzFsrzbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(RzZlpzFsrzbDO::getCzyid, dto.getCzyid(), Objects.nonNull(dto.getCzyid()));
        query.eq(RzZlpzFsrzbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        return query;
    }

    @Override
    public RzZlpzFsrzbDTO convertToDTO(RzZlpzFsrzbDO rzZlpzFsrzbDO) {
        return convert.convert(rzZlpzFsrzbDO);
    }

    @Override
    public RzZlpzFsrzbDO convertToDO(RzZlpzFsrzbDTO rzZlpzFsrzbDTO) {
        return convert.convertToDO(rzZlpzFsrzbDTO);
    }
}
