package com.zjjcnt.project.ck.zzj.third.yzty.resp;

import lombok.Data;

@Data
public class YztyMpzjResp {

    private Boolean success;

    private Boolean error;

    private String message;

    public static YztyMpzjResp success(String message) {
        return new YztyMpzjResp(true, message, false);
    }

    public static YztyMpzjResp fail(String message) {
        return new YztyMpzjResp(false, message, false);
    }

    public static YztyMpzjResp error(String message) {
        return new YztyMpzjResp(false, message, true);
    }

    private YztyMpzjResp(Boolean success, String message, boolean error) {
        this.success = success;
        this.message = message;
        this.error = error;
    }

}
