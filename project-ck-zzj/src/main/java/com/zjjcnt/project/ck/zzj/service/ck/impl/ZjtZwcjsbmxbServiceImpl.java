package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtZwcjsbmxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjsbmxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtZwcjsbmxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtZwcjsbmxbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjsbmxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 13:35:50
 */
@Service
public class ZjtZwcjsbmxbServiceImpl extends AbstractBaseServiceImpl<ZjtZwcjsbmxbMapper, ZjtZwcjsbmxbDO, ZjtZwcjsbmxbDTO> implements ZjtZwcjsbmxbService {

    ZjtZwcjsbmxbConvert convert = ZjtZwcjsbmxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtZwcjsbmxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtZwcjsbmxbDO::getTxzlpdmx, dto.getTxzlpdmx(), StringUtils.isNotEmpty(dto.getTxzlpdmx()));
        query.eq(ZjtZwcjsbmxbDO::getBdzlpdmx, dto.getBdzlpdmx(), StringUtils.isNotEmpty(dto.getBdzlpdmx()));
        query.eq(ZjtZwcjsbmxbDO::getYszlpdmx, dto.getYszlpdmx(), StringUtils.isNotEmpty(dto.getYszlpdmx()));
        query.eq(ZjtZwcjsbmxbDO::getDzzdcjcs, dto.getDzzdcjcs(), StringUtils.isNotEmpty(dto.getDzzdcjcs()));
        query.eq(ZjtZwcjsbmxbDO::getBlcs1, dto.getBlcs1(), Objects.nonNull(dto.getBlcs1()));
        query.eq(ZjtZwcjsbmxbDO::getBlcs2, dto.getBlcs2(), Objects.nonNull(dto.getBlcs2()));
        query.eq(ZjtZwcjsbmxbDO::getBlcs3, dto.getBlcs3(), Objects.nonNull(dto.getBlcs3()));
        query.eq(ZjtZwcjsbmxbDO::getBlcs4, dto.getBlcs4(), Objects.nonNull(dto.getBlcs4()));
        query.eq(ZjtZwcjsbmxbDO::getBlcs5, dto.getBlcs5(), Objects.nonNull(dto.getBlcs5()));
        return query;
    }

    @Override
    public ZjtZwcjsbmxbDTO convertToDTO(ZjtZwcjsbmxbDO zjtZwcjsbmxbDO) {
        return convert.convert(zjtZwcjsbmxbDO);
    }

    @Override
    public ZjtZwcjsbmxbDO convertToDO(ZjtZwcjsbmxbDTO zjtZwcjsbmxbDTO) {
        return convert.convertToDO(zjtZwcjsbmxbDTO);
    }
}
