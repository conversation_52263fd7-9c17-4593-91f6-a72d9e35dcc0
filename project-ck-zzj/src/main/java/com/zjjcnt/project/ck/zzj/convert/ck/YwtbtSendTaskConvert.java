package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.YwtbtSendTaskDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.YwtbtSendTaskDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-06-21 15:36:54
*/
@Mapper
public interface YwtbtSendTaskConvert {

    YwtbtSendTaskConvert INSTANCE = Mappers.getMapper(YwtbtSendTaskConvert.class);

    YwtbtSendTaskDTO convert(YwtbtSendTaskDO entity);

    @InheritInverseConfiguration(name="convert")
    YwtbtSendTaskDO convertToDO(YwtbtSendTaskDTO dto);

}
