package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 临时身份证受理信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSlxxbDTO
 */
@Crypto
@Data
@Table("zjt_lssfz_slxxb")
public class ZjtLssfzSlxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = -5515564693207522997L;

    /**
     * 临时受理ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String lsslid;

    /**
     * 人员内部ID
     */
    @Column(value = "rynbid")
    private String rynbid;

    /**
     * 照片ID
     */
    @Column(value = "zpid")
    private String zpid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 签发机关
     */
    @Column(value = "qfjg")
    private String qfjg;

    /**
     * 有效期限起始日期
     */
    @Column(value = "yxqxqsrq")
    private String yxqxqsrq;

    /**
     * 有效期限截止日期
     */
    @Column(value = "yxqxjzrq")
    private String yxqxjzrq;

    /**
     * 临时居民身份证卡号
     */
    @Column(value = "lsjmsfzkh")
    private String lsjmsfzkh;

    /**
     * 住址
     */
    @Column(value = "zz")
    private String zz;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 内部身份证ID
     */
    @Column(value = "nbsfzid")
    private String nbsfzid;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 民族
     */
    @Column(value = "mz")
    private String mz;

    /**
     * 出生日期
     */
    @Column(value = "csrq")
    private String csrq;

    /**
     * 出生地省市县（区）
     */
    @Column(value = "csdssxq")
    private String csdssxq;

    /**
     * 联系电话
     */
    @Crypto
    @Column(value = "lxdh")
    private String lxdh;

    /**
     *
     */
    @Column(value = "mlpnbid")
    private String mlpnbid;

    /**
     * 省市县（区）
     */
    @Column(value = "ssxq")
    private String ssxq;

    /**
     * 街路巷
     */
    @Column(value = "jlx")
    private String jlx;

    /**
     * 门（楼）牌号
     */
    @Column(value = "mlph")
    private String mlph;

    /**
     * 门（楼）详址
     */
    @Column(value = "mlxz")
    private String mlxz;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 责任区
     */
    @Column(value = "zrq")
    private String zrq;

    /**
     * 乡镇（街道）
     */
    @Column(value = "xzjd")
    private String xzjd;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 排序号
     */
    @Column(value = "pxh")
    private String pxh;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     *
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作员IP
     */
    @Column(value = "czyip")
    private String czyip;

    /**
     * 打印标志
     */
    @Column(value = "dybz")
    private String dybz;

    /**
     *
     */
    @Column(value = "dyrid")
    private String dyrid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "dyrxm")
    private String dyrxm;

    /**
     *
     */
    @Column(value = "dysj")
    private String dysj;

    /**
     *
     */
    @Column(value = "dyrip")
    private String dyrip;

    /**
     * 审核结果
     */
    @Column(value = "shjg")
    private String shjg;

    /**
     *
     */
    @Column(value = "shrid")
    private String shrid;

    /**
     * 审核人IP
     */
    @Column(value = "shrip")
    private String shrip;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "shrxm")
    private String shrxm;

    /**
     * 审核时间
     */
    @Column(value = "shsj")
    private String shsj;

    /**
     * 公安部的受理地数据归属单位代码
     */
    @Column(value = "sldsjgsdwdmgab")
    private String sldsjgsdwdmgab;

    /**
     *
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     *
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     *
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     *
     */
    @Column(value = "zzlx")
    private String zzlx;

    /**
     *
     */
    @Column(value = "nbslid")
    private String nbslid;

    /**
     * 居民身份证受理号
     */
    @Column(value = "jmsfzslh")
    private String jmsfzslh;

    /**
     * 受理地分局数据归属单位代码
     */
    @Column(value = "sldfjsjgsdwdm")
    private String sldfjsjgsdwdm;

    /**
     * 受理地分局数据归属单位名称
     */
    @Column(value = "sldfjsjgsdwmc")
    private String sldfjsjgsdwmc;

    /**
     * 互联网申请id
     */
    @Column(value = "hlwsqid")
    private String hlwsqid;

    /**
     * 第三方用户名称
     */
    @Column(value = "username")
    private String username;

    /**
     * 评价结果
     */
    @Column(value = "pjjg")
    private String pjjg;

    /**
     * 跑了几次评价
     */
    @Column(value = "pjpljc")
    private String pjpljc;

    /**
     * 评价时间
     */
    @Column(value = "pjsj")
    private String pjsj;

    /**
     * 领证方式
     */
    @Column(value = "lqfs")
    private String lqfs;

    /**
     * 收件人姓名
     */
    @Crypto
    @Column(value = "sjrxm")
    private String sjrxm;

    /**
     * 收件人联系电话
     */
    @Crypto
    @Column(value = "sjrlxdh")
    private String sjrlxdh;

    /**
     * 收件人邮编
     */
    @Column(value = "sjryb")
    private String sjryb;

    /**
     * 收件人省市县区
     */
    @Column(value = "sjrssxq")
    private String sjrssxq;

    /**
     * 收件人详址
     */
    @Column(value = "sjrxz")
    private String sjrxz;

    /**
     * 收件人通讯地址
     */
    @Column(value = "sjrtxdz")
    private String sjrtxdz;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 户籍地分局审核耗时(分)
     */
    @Column(value = "fjshhs")
    private Integer fjshhs;

    /**
     * 采集方式
     */
    @Column(value = "cjfs")
    private String cjfs;

    /**
     * 非现金收费状态
     */
    @Column(value = "fxjsfzt")
    private String fxjsfzt;

    /**
     * 非现金收费时间
     */
    @Column(value = "fxjsfsj")
    private String fxjsfsj;

    /**
     * 服务对象
     */
    @Column(value = "fwdx")
    private String fwdx;

    /**
     * 收费类型
     */
    @Column(value = "sflx")
    private String sflx;

    /**
     * 收费金额
     */
    @Column(value = "sfje")
    private BigDecimal sfje;

    @Override
    public String getId() {
        return this.lsslid;
    }

    @Override
    public void setId(String id) {
        this.lsslid = id;
    }
}
