package com.zjjcnt.project.ck.zzj.manager;

import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.exception.UserErrorCode;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.IdentityUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.DwUtils;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.dto.XtXtkzcsbDTO;
import com.zjjcnt.project.ck.sysadmin.service.XtXtkzcsbService;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ck.ZlpzConvert;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.RxZpcjxxbConvert;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.XtZzsbxxbConvert;
import com.zjjcnt.project.ck.zzj.domain.VoPzyjsSxRegisterPointResponse;
import com.zjjcnt.project.ck.zzj.domain.ZlpzUploadRes;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.RxZpcjxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.RzZlpzZjrzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RxZpcjxxbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RxZpwjbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RzZlpzZjrzbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbxxbService;
import com.zjjcnt.project.ck.zzj.third.cjrzj.CrjZjClient;
import com.zjjcnt.project.ck.zzj.third.cjrzj.CrjZjResp;
import com.zjjcnt.project.ck.zzj.third.rop.YztyCrjRopClient;
import com.zjjcnt.project.ck.zzj.third.rop.req.YztyCrjRopReq;
import com.zjjcnt.project.ck.zzj.third.rop.req.YztyJjRopReq;
import com.zjjcnt.project.ck.zzj.third.yzty.YztyClient;
import com.zjjcnt.project.ck.zzj.third.yzty.req.YztyCjdwsc;
import com.zjjcnt.project.ck.zzj.third.yzty.req.YztyMpzjReq;
import com.zjjcnt.project.ck.zzj.third.yzty.resp.YztyMpzjResp;
import com.zjjcnt.project.ck.zzj.web.request.CrjZpUploadReq;
import com.zjjcnt.project.ck.zzj.web.request.JjZpUploadReq;
import com.zjjcnt.project.ck.zzj.web.request.ZlpzCrjZjReq;
import com.zjjcnt.project.ck.zzj.web.request.ZlpzZpxxSaveReq;
import com.zjjcnt.project.ck.zzj.web.response.ZlpzCrjZjResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.Objects;

/**
 * Created by hubin on 2023/02/15.
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ZlpzManager {

    private final RxZpcjxxbService rkxtRxZpcjxxbService;
    private final RxZpwjbService rkxtRxZpwjbService;
    private final XtZzsbxxbService xtZzsbxxbService;
    private final PzyjsSxManager pzyjsSxManager;
    private final CrjZjClient crjZjClient;
    private final YztyCrjRopClient yztyCrjRopClient;
    private final YztyClient yztyClient;
    private final XtXtkzcsbService xtXtkzcsbService;
    private final RzZlpzZjrzbService rzZlpzZjrzbService;

    /**
     * 照片保存接口
     *
     * @param zpxxSaveReq
     */
    @Transactional(rollbackFor = Exception.class)
    public RxZpcjxxbDTO zlpzZpxxSave(ZlpzZpxxSaveReq zpxxSaveReq) {

        // 获取设备信息
        XtZzsbxxbDTO zzsbxxb = xtZzsbxxbService.findById(zpxxSaveReq.getSbid());
        if (zzsbxxb == null) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "查无设备信息:" + zpxxSaveReq.getSbid());
        } else if (Constants.NO.equals(zzsbxxb.getQybz())) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "该设备未启用:" + zpxxSaveReq.getSbid());
        }
        // 不是浙里拍照设备的不允许保存照片
        if (Constants.NO.equals(zzsbxxb.getSfzlpzsb())) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "该设备不是浙里拍照设备,不允许保存照片!");
        }
        //校验设备信息
        //组织照片采集信息
        RxZpcjxxbDTO zpcjxx = RxZpcjxxbConvert.INSTANCE.convertToDTO(zpxxSaveReq);

        zpcjxx.setCzsj(ServerTimeUtils.getCurrentTime());
        zpcjxx.setCjsj(ServerTimeUtils.getCurrentTime());
        zpcjxx.setTbbz(CkZzjConstants.TBBZ_BTB);
        zpcjxx.setDtbxt(CkZzjConstants.TBXT_CSJ);

        //操作员信息
        CustomUserDetails user = SecurityUtils.getCurrentUser();
        zpcjxx.setCzyid(user.getUserId());
        zpcjxx.setCzyxm(user.getName());
        zpcjxx.setPcs(user.getDeptCode());
        zpcjxx.setPcsmc(user.getDeptName());

        boolean sfsfz = IdentityUtils.validateID(user.getIdCard());

        if (sfsfz) {
            zpcjxx.setCzygmsfhm(user.getIdCard());
        } else {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, user.getUsername() + "账号的注册身份证号码有误,请联系管理员修改,错误身份证为:" + user.getIdCard());
        }

        zpcjxx.setCzyip(user.getRemoteAddress());

        //设备点位编号
        zpcjxx.setZlpzsbdwbh(zzsbxxb.getZlpzsbdwbh());

        zpcjxx.setZjzl(StringUtils.defaultIfBlank(zpcjxx.getZjzl(), CkZzjConstants.ZJZL_SFZ));
        zpcjxx.setGjdm(StringUtils.defaultIfBlank(zpcjxx.getGjdm(), CkZzjConstants.GJDM_ZG));
        zpcjxx.setGjmc(StringUtils.defaultIfBlank(zpcjxx.getGjmc(), CkZzjConstants.GJMC_ZG));
        zpcjxx.setCjyt(StringUtils.defaultIfBlank(zpcjxx.getCjyt(), CkZzjConstants.CJYT_BZSY));

        //校验必填项
        rkxtRxZpcjxxbService.validateZpcjxx(zpcjxx);

        String wjhz = "jpg";
        //保存各照片
        if (zpxxSaveReq.getBase64Ytzp() != null) {
            zpcjxx.setYtzpid(rkxtRxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_YT, zpxxSaveReq.getZjhm(), zpxxSaveReq.getXm(), zpxxSaveReq.getBase64Ytzp(), wjhz));
        }
        if (zpxxSaveReq.getBase64Sfzzp() != null) {
            zpcjxx.setSfzzpid(rkxtRxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_SFZ, zpxxSaveReq.getZjhm(), zpxxSaveReq.getXm(), zpxxSaveReq.getBase64Sfzzp(), wjhz));
        }
        if (zpxxSaveReq.getBase64Crjzp() != null) {
            zpcjxx.setCrjzpid(rkxtRxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_CRJ, zpxxSaveReq.getZjhm(), zpxxSaveReq.getXm(), zpxxSaveReq.getBase64Crjzp(), wjhz));
        }
        if (zpxxSaveReq.getBase64Jszzp() != null) {
            zpcjxx.setJszzpid(rkxtRxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_JSZ, zpxxSaveReq.getZjhm(), zpxxSaveReq.getXm(), zpxxSaveReq.getBase64Jszzp(), wjhz));
        }
//        if (vozpxx.getBase64Qtzp() != null) {
//            zpcjxx.setQtzpid(rkxtRxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_QT, vozpxx.getZjhm(), vozpxx.getXm(), vozpxx.getBase64Qtzp(), wjhz));
//        }

        //是否发送任务
        if (Constants.YES.equals(zpxxSaveReq.getSffsrw())) {
            zpcjxx.setTbbz(CkZzjConstants.TBBZ_DTB);
        }

        return rkxtRxZpcjxxbService.insert(zpcjxx);
    }


    /**
     * 拍照设备注册接口
     * 同步设备信息到照片库
     */
    @Transactional(rollbackFor = Exception.class)
    public XtZzsbxxbDTO yztyZdsbxxSaveAndUpload(XtZzsbxxbDTO zzsbxxbDTO) {
        boolean isSfzplzsb = Constants.YES.equals(zzsbxxbDTO.getSfzlpzsb());

        if (isSfzplzsb && CkZzjConstants.TBXT_CSJ.equals(zzsbxxbDTO.getTbxt()) && zzsbxxbDTO.getZlpzsbdwbh().length() != 16) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "sbdwbh长度不正确:" + zzsbxxbDTO.getZlpzsbdwbh());
        }
        XtZzsbxxbDTO result;
        YztyCjdwsc yztyCjdwsc = XtZzsbxxbConvert.INSTANCE.convertToYztyCjdwsc(zzsbxxbDTO);

        if (Objects.isNull(zzsbxxbDTO.getId())) {
            //新增
            XtZzsbxxbDTO sbxxbExist = xtZzsbxxbService.findEnabledByAppkeyAndSbipAndSbmac(zzsbxxbDTO.getAppkey(),
                    zzsbxxbDTO.getSbip(), zzsbxxbDTO.getSbmac());
            checkZlpzsbdwbh(zzsbxxbDTO, sbxxbExist);
            if (isSfzplzsb) {
                String csjcjzdbm;
                if (Objects.isNull(sbxxbExist) || StringUtils.isBlank(sbxxbExist.getCsjcjzdbm())) {
                    csjcjzdbm = yztyClient.cjdwsc(yztyCjdwsc, CkZzjConstants.CJZD_CZLX_XZ);
                } else {
                    csjcjzdbm = yztyClient.cjdwsc(yztyCjdwsc, CkZzjConstants.CJZD_CZLX_XG);
                }
                zzsbxxbDTO.setCsjcjzdbm(csjcjzdbm);
            }
            if (Objects.isNull(sbxxbExist)) {
                result = xtZzsbxxbService.insertZzsb(zzsbxxbDTO);
            } else {
                zzsbxxbDTO.setId(sbxxbExist.getId());
                result = xtZzsbxxbService.updateZzsb(zzsbxxbDTO);
            }
        } else {
            XtZzsbxxbDTO sbxxbExist = xtZzsbxxbService.findById(zzsbxxbDTO.getId());
            checkZlpzsbdwbh(zzsbxxbDTO, sbxxbExist);
            //修改
            if (isSfzplzsb) {
                String csjcjzdbm;
                if (StringUtils.isBlank(sbxxbExist.getCsjcjzdbm())) {
                    csjcjzdbm = yztyClient.cjdwsc(yztyCjdwsc, CkZzjConstants.CJZD_CZLX_XZ);
                } else if (!StringUtils.equals(sbxxbExist.getZlpzsbdwbh(), zzsbxxbDTO.getZlpzsbdwbh())){
                    YztyCjdwsc yztyCjdwzx = XtZzsbxxbConvert.INSTANCE.convertToYztyCjdwsc(sbxxbExist);
                    yztyClient.cjdwsc(yztyCjdwzx, CkZzjConstants.CJZD_CZLX_ZX);
                    csjcjzdbm = yztyClient.cjdwsc(yztyCjdwsc, CkZzjConstants.CJZD_CZLX_XZ);
                } else {
                    csjcjzdbm = yztyClient.cjdwsc(yztyCjdwsc, CkZzjConstants.CJZD_CZLX_XG);
                }
                if (StringUtils.isNotBlank(csjcjzdbm)) {
                    zzsbxxbDTO.setCsjcjzdbm(csjcjzdbm);
                }
            }
            result = xtZzsbxxbService.updateZzsb(zzsbxxbDTO);
        }

        return result;
    }

    /**
     * 拍照设备注册接口
     * 同步设备信息到照片库
     */
    @Transactional(rollbackFor = Exception.class)
    public XtZzsbxxbDTO yztyZdsbxxSaveAndUpload(XtZzsbxxbDTO zzsbxxbDTO, String czlx) {
        XtZzsbxxbDTO zzsbxxb = new XtZzsbxxbDTO();

        if (CkZzjConstants.TBXT_CSJ.equals(zzsbxxbDTO.getTbxt()) && zzsbxxbDTO.getZlpzsbdwbh().length() != 16) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "sbdwbh长度不正确:" + zzsbxxbDTO.getZlpzsbdwbh());
        }

        YztyCjdwsc yztyCjdwsc = XtZzsbxxbConvert.INSTANCE.convertToYztyCjdwsc(zzsbxxbDTO);

        if (CkZzjConstants.CJZD_CZLX_XZ.equals(czlx)) {
            if (CkZzjConstants.TBXT_CSJ.equals(zzsbxxbDTO.getTbxt())) {
                //长三角照片库 注册设备
                String csjcjzdbm = yztyClient.cjdwsc(yztyCjdwsc, czlx);

                XtZzsbxxbDTO xtZzsbxxbDTO = xtZzsbxxbService.findEnabledByAppkeyAndSbip(zzsbxxbDTO.getAppkey(), zzsbxxbDTO.getSbip());
                if (Objects.isNull(xtZzsbxxbDTO)) {
                    XtZzsbxxbDTO xtZzsbxxbByZlpzsbdwbh = xtZzsbxxbService.findEnabledByAppkeyAndZlpzsbdwbh(zzsbxxbDTO.getAppkey(), zzsbxxbDTO.getZlpzsbdwbh());

                    if (Objects.isNull(xtZzsbxxbByZlpzsbdwbh)) {
                        zzsbxxbDTO.setCsjcjzdbm(csjcjzdbm);
                        zzsbxxb = xtZzsbxxbService.insertZzsb(zzsbxxbDTO);
                    } else {
                        throw new ServiceException(CkZzjErrorCode.DEVICE_ALREADY_EXIST,
                                "该设备点位编号:" + zzsbxxbDTO.getZlpzsbdwbh() + "已使用,请勿重复注册");
                    }
                } else if (StringUtils.isBlank(xtZzsbxxbDTO.getCsjcjzdbm()) && StringUtils.isNotBlank(csjcjzdbm)) {
                    xtZzsbxxbDTO.setCsjcjzdbm(csjcjzdbm);
                    if (StringUtils.isBlank(xtZzsbxxbDTO.getZddjrlxdh()) && StringUtils.isNotBlank(zzsbxxbDTO.getZddjrlxdh())) {
                        xtZzsbxxbDTO.setZddjrlxdh(zzsbxxbDTO.getZddjrlxdh());
                    }
                    xtZzsbxxbService.update(xtZzsbxxbDTO);
                } else {
                    return xtZzsbxxbDTO;
                }

            } else if (CkZzjConstants.TBXT_SX.equals(zzsbxxbDTO.getTbxt())) {
                //绍兴照片库 注册设备
                VoPzyjsSxRegisterPointResponse response = pzyjsSxManager.registerPoint(zzsbxxbDTO, czlx);

                XtZzsbxxbDTO xtZzsbxxbDTO = xtZzsbxxbService.findEnabledByAppkeyAndSbip(zzsbxxbDTO.getAppkey(), zzsbxxbDTO.getSbip());
                if (Objects.nonNull(xtZzsbxxbDTO)) {
                    return xtZzsbxxbDTO;
                } else {
                    zzsbxxbDTO.setZlpzsbdwbh(response.getCjdbh());
                    zzsbxxb = xtZzsbxxbService.insertZzsb(zzsbxxbDTO);
                }
            }
        } else if (CkZzjConstants.CJZD_CZLX_XG.equals(czlx)) {

            if (CkZzjConstants.TBXT_CSJ.equals(zzsbxxbDTO.getTbxt())) {
                //长三角照片库 修改设备
                yztyClient.cjdwsc(yztyCjdwsc, czlx);
                zzsbxxb = xtZzsbxxbService.updateZzsb(zzsbxxbDTO);

            } else if (CkZzjConstants.TBXT_SX.equals(zzsbxxbDTO.getTbxt())) {
                VoPzyjsSxRegisterPointResponse response = pzyjsSxManager.registerPoint(zzsbxxbDTO, czlx);
                zzsbxxbDTO.setZlpzsbdwbh(response.getCjdbh());
                zzsbxxb = xtZzsbxxbService.updateZzsb(zzsbxxbDTO);
            }
        }
        return zzsbxxb;
    }

    public ZlpzUploadRes uploadCrjzp(CrjZpUploadReq crjZjReq) {
        YztyCrjRopReq yztyCrjRopReq = ZlpzConvert.INSTANCE.convertRopReq(crjZjReq);
        yztyCrjRopReq.setPsrq(StringUtils.defaultIfBlank(yztyCrjRopReq.getPsrq(),
                ServerTimeUtils.getCurrentDate()));
        yztyCrjRopReq.setXtmc(StringUtils.defaultIfBlank(yztyCrjRopReq.getXtmc(),
                CkZzjConstants.ZLPZ_CRJ_XTMC_DEFAULT));

        CustomUserDetails user = SecurityUtils.getCurrentUser();
        yztyCrjRopReq.setCzyxm(user.getName());
        yztyCrjRopReq.setCzyid(String.valueOf(user.getUserId()));

        try {
            yztyCrjRopClient.processYztyCrj(yztyCrjRopReq);
        } catch (ServiceException e) {
            return ZlpzUploadRes.fail(e.getMessage());
        }
        return ZlpzUploadRes.success();
    }

    public ZlpzUploadRes uploadJjzp(JjZpUploadReq jjZpUploadReq) {
        YztyJjRopReq yztyCrjRopReq = ZlpzConvert.INSTANCE.convertRopReq(jjZpUploadReq);

        CustomUserDetails user = SecurityUtils.getCurrentUser();
        yztyCrjRopReq.setCzyxm(user.getName());
        yztyCrjRopReq.setCzygmsfhm(user.getIdCard());
        yztyCrjRopReq.setCzyid(String.valueOf(user.getUserId()));
        yztyCrjRopReq.setCzyip(user.getRemoteAddress());
        yztyCrjRopReq.setPcs(user.getDeptCode());
        yztyCrjRopReq.setPcsmc(user.getDeptName());

        try {
            yztyCrjRopClient.processYztyJj(yztyCrjRopReq);
        } catch (ServiceException e) {
            return ZlpzUploadRes.fail(e.getMessage());
        }
        return ZlpzUploadRes.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public ZlpzCrjZjResp crjZjYzty(ZlpzCrjZjReq crjZjReq) {

        // 一照通用前缀及信息
        XtXtkzcsbDTO xtXtkzcsb = xtXtkzcsbService.findByKzlb("1647");

        String url;
        SecureRandom random = new SecureRandom();
        int choice = random.nextInt(2);
        if (choice > 0 && xtXtkzcsb.getKzz().contains("9010")) {
            url = xtXtkzcsb.getKzz();
        } else {
            url = xtXtkzcsb.getBz();
        }

        CrjZjResp crjZjResp = crjZjClient.crjZjYzty(crjZjReq, url);

        // 添加质检日志
        if (CkZzjConstants.CRJZJ_TYPE_ZJ.equals(crjZjReq.getType()) && Constants.YES.equals(xtXtkzcsb.getMrz())) {
            try {
                RzZlpzZjrzbDTO rzZlpzZjrzb = new RzZlpzZjrzbDTO();
                rzZlpzZjrzb.setZjlx(CkZzjConstants.ZJLX_CRJ);
                rzZlpzZjrzb.setZjjg(crjZjResp.getOk() ? Constants.YES : Constants.NO);
                rzZlpzZjrzb.setZjms(crjZjResp.getMessage());
                rzZlpzZjrzb.setCzsj(ServerTimeUtils.getCurrentTime());
                rzZlpzZjrzb.setGmsfhm(crjZjReq.getZjhm());
                rzZlpzZjrzb.setCzyid(String.valueOf(SecurityUtils.getUserId()));
                rzZlpzZjrzbService.insert(rzZlpzZjrzb);
            } catch (Exception e) {
                log.info("出入境质检日志保存失败", e);
            }
        }
        return ZlpzConvert.INSTANCE.convert(crjZjResp);
    }

    @Transactional(rollbackFor = Exception.class)
    public YztyMpzjResp yztyMpzj(YztyMpzjReq yztyMpzjReq) {
        CustomUserDetails user = SecurityUtils.getCurrentUser();

        yztyMpzjReq.setXm(user.getUsername());
        yztyMpzjReq.setGmsfhm(user.getIdCard());
        yztyMpzjReq.setJgdm(DwUtils.expandSjgsdw(user.getDeptCode()));
        yztyMpzjReq.setJgdmmc(user.getDeptName());
        yztyMpzjReq.setZdbs(user.getRemoteAddress());

        YztyMpzjResp mpzjResp = yztyClient.mpzj(yztyMpzjReq);

        // 质检日志
        try {
            XtXtkzcsbDTO xtXtkzcsbDTO = xtXtkzcsbService.findByKzlb("1647");
            if (Constants.YES.equals(xtXtkzcsbDTO.getMrz())) {
                RzZlpzZjrzbDTO rzZlpzZjrzb = new RzZlpzZjrzbDTO();
                rzZlpzZjrzb.setZjlx(CkZzjConstants.ZJLX_MP);
                rzZlpzZjrzb.setZjjg(mpzjResp.getSuccess() ? Constants.YES : Constants.NO);
                rzZlpzZjrzb.setZjms(mpzjResp.getMessage());
                rzZlpzZjrzb.setCzsj(ServerTimeUtils.getCurrentTime());
                rzZlpzZjrzb.setGmsfhm(yztyMpzjReq.getBlrzjhm());
                rzZlpzZjrzb.setCzyid(String.valueOf(user.getUserId()));
                rzZlpzZjrzbService.insert(rzZlpzZjrzb);
            }
        } catch (Exception e) {
            log.error("母片质检日志保存失败", e);
        }

        return mpzjResp;
    }

    private void checkZlpzsbdwbh(XtZzsbxxbDTO zzsbxxbDTO, XtZzsbxxbDTO sbxxbExist) {
        if (StringUtils.isEmpty(zzsbxxbDTO.getZlpzsbdwbh())) {
            return;
        }
        XtZzsbxxbDTO xtZzsbxxbByZlpzsbdwbh = xtZzsbxxbService.findEnabledByAppkeyAndZlpzsbdwbh(zzsbxxbDTO.getAppkey(), zzsbxxbDTO.getZlpzsbdwbh());
        if (Objects.nonNull(xtZzsbxxbByZlpzsbdwbh)) {
            if (Objects.isNull(sbxxbExist) || !Objects.equals(xtZzsbxxbByZlpzsbdwbh.getId(), sbxxbExist.getId())) {
                throw new ServiceException(CkZzjErrorCode.DEVICE_ALREADY_EXIST,
                        "该设备点位编号:" + zzsbxxbDTO.getZlpzsbdwbh() + "已使用,请勿重复注册");
            }
        }
    }
}
