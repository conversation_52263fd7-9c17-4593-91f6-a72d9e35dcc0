package com.zjjcnt.project.ck.zzj.domain;

import com.google.common.collect.Lists;
import com.zjjcnt.project.ck.zzj.util.EncodeUtils;

import jakarta.xml.bind.annotation.XmlAnyElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.List;

@XmlSeeAlso(CkdshzJjzpFieldDto.class)
public class CkdshzJjzpDataDto {

    private List<CkdshzJjzpFieldDto> ckdshzJjzpFieldDtoList = Lists.newArrayList();

    @XmlJavaTypeAdapter(CkdshzFieldAdapter.class)
    @XmlAnyElement
    public List<CkdshzJjzpFieldDto> getFieldList() {
        return ckdshzJjzpFieldDtoList;
    }

    public void setFieldList(List<CkdshzJjzpFieldDto> ckdshzJjzpFieldDtoList) {
        this.ckdshzJjzpFieldDtoList = ckdshzJjzpFieldDtoList;
    }

    public void addField(String key, String value) {
        ckdshzJjzpFieldDtoList.add(new CkdshzJjzpFieldDto(key, value));
    }

    public void addFieldUTF8(String key, String value) {
        ckdshzJjzpFieldDtoList.add(new CkdshzJjzpFieldDto(key, EncodeUtils.encode(value)));
    }

}
