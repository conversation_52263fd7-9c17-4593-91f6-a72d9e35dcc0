package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtZwcjbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjbViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtSlxxbService;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Base64;
import java.util.Objects;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2024-05-09 11:39:12
 */
@Deprecated
@RequiredArgsConstructor
@Tag(name = "指纹采集表")
@RestController
@RequestMapping("/zjtZwcjb")
public class ZjtZwcjbController extends AbstractCrudController<ZjtZwcjbDTO> {

    private final ZjtZwcjbService zjtZwcjbService;
    private final ZjtSlxxbService zjtSlxxbService;

    @Override
    protected IBaseService getService() {
        return zjtZwcjbService;
    }

    @GetMapping("viewNormal")
    @Operation(summary = "查看正常指纹")
    public CommonResult<ZjtZwcjbViewResp> viewNormal(@RequestParam(value = "gmsfhm") String gmsfhm) {
        ZjtSlxxbDTO zjtSlxxbDTO = zjtSlxxbService.findNewestNormal(gmsfhm);
        if (Objects.isNull(zjtSlxxbDTO)) {
            return CommonResult.success(null);
        }

        ZjtZwcjbDTO zjtZwcjbDTO = zjtZwcjbService.findById(zjtSlxxbDTO.getZwtxid());

        if (!StringUtils.equalsAny(zjtZwcjbDTO.getZwcjjgdm(), ZjConstant.ZW_ZWCJJGDM_1, ZjConstant.ZW_ZWCJJGDM_2,
                ZjConstant.ZW_ZWCJJGDM_3)) {
            return CommonResult.success(null);
        }

        ZjtZwcjbViewResp zjtZwcjbViewResp = ZjtZwcjbConvert.INSTANCE.convertToViewResp(zjtZwcjbDTO);

        if (CkZzjConstants.ZWZCJGDM_ZCCG.equals(zjtZwcjbViewResp.getZwyzcjg())
                && zjtZwcjbViewResp.getZwytxzlz().compareTo(BigDecimal.valueOf(55)) >= 0) {
            zjtZwcjbViewResp.setZwyzwtzsjBase64(Base64.getEncoder().encodeToString(zjtZwcjbDTO.getZwyzwtzsj()));
        }
        if (CkZzjConstants.ZWZCJGDM_ZCCG.equals(zjtZwcjbViewResp.getZwezcjg())
                && zjtZwcjbViewResp.getZwetxzlz().compareTo(BigDecimal.valueOf(55)) >= 0) {
            zjtZwcjbViewResp.setZwezwtzsjBase64(Base64.getEncoder().encodeToString(zjtZwcjbDTO.getZwezwtzsj()));
        }

        return CommonResult.success(zjtZwcjbViewResp);
    }

}
