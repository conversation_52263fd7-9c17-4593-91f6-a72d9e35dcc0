package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtRxxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtRxxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtRxxxbViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtRxxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
* 人像信息表前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/

@Tag(name = "人像信息表")
@RestController
@RequestMapping("/zjtRxxxb")
public class ZjtRxxxbController extends AbstractCrudController<ZjtRxxxbDTO> {

    @Autowired
    private ZjtRxxxbService zjtRxxxbService;

    @Override
    protected IBaseService getService() {
        return zjtRxxxbService;
    }

//    @GetMapping("page")
//    @Operation(summary = "查询人像信息表")
//    public CommonResult<PageResult<ZjtRxxxbPageResp>> page(ZjtRxxxbPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, ZjtRxxxbConvert.INSTANCE::convertToDTO, ZjtRxxxbConvert.INSTANCE::convertToPageResp);
//    }

    @GetMapping("view")
    @Operation(summary = "查看人像信息表详情 N23004")
    public CommonResult<ZjtRxxxbViewResp> view(String id) {
        ZjtRxxxbViewResp zjtRxxxbViewResp = doView(id, ZjtRxxxbConvert.INSTANCE::convertToViewResp);
        if (Objects.nonNull(zjtRxxxbViewResp)) {
            zjtRxxxbViewResp.setBase64zp(zjtRxxxbService.getBase64Zp(zjtRxxxbViewResp.getZpwjbh()));
        }
        return CommonResult.success(zjtRxxxbViewResp);
    }

//    @PostMapping("create")
//    @Operation(summary = "新增人像信息表")
//    public CommonResult<ZjtRxxxbCreateResp> create(@RequestBody ZjtRxxxbCreateReq req) {
//        return super.create(req, ZjtRxxxbConvert.INSTANCE::convertToDTO, ZjtRxxxbConvert.INSTANCE::convertToCreateResp);
//    }
//
//    @PostMapping("update")
//    @Operation(summary = "编辑人像信息表")
//    public CommonResult<Boolean> update(@RequestBody ZjtRxxxbUpdateReq req) {
//        return super.update(req, ZjtRxxxbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除人像信息表")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }
//
//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(ZjtRxxxbExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出人像信息表")
//    public void export(ZjtRxxxbPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "人像信息表" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, ZjtRxxxbExp.class,
//                    ZjtRxxxbConvert.INSTANCE::convertToDTO, ZjtRxxxbConvert.INSTANCE::convertToExp, response);
//    }

}
