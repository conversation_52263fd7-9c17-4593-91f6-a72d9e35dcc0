package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjywWjSjDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjywWjSjCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjywWjSjPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjywWjSjUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjywWjSjCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjywWjSjPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjywWjSjViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjywWjSjDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-07 11:35:02
*/
@Mapper
public interface HjywWjSjConvert {

    HjywWjSjConvert INSTANCE = Mappers.getMapper(HjywWjSjConvert.class);

    HjywWjSjDTO convert(HjywWjSjDO entity);

    @InheritInverseConfiguration(name="convert")
    HjywWjSjDO convertToDO(HjywWjSjDTO dto);

    HjywWjSjDTO convertToDTO(HjywWjSjPageReq req);

    HjywWjSjDTO convertToDTO(HjywWjSjCreateReq req);

    HjywWjSjDTO convertToDTO(HjywWjSjUpdateReq req);

    HjywWjSjPageResp convertToPageResp(HjywWjSjDTO dto);

    HjywWjSjViewResp convertToViewResp(HjywWjSjDTO dto);

    HjywWjSjCreateResp convertToCreateResp(HjywWjSjDTO dto);

}
