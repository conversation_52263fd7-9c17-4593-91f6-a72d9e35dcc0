package com.zjjcnt.project.ck.zzj.third.rop.req;

import lombok.Data;

/**
 * 未成年首申办证资格审查req
 *
 * <AUTHOR>
 * @date 2025年4月9日 14点19分
 */
@Data
public class KssfzWcnscbzzgscRopReq implements IZdbzsbKssfzRopReq {

    /**
     * 公民身份号码不能为空
     */
    private String gmsfhm;

    /**
     * 密文
     */
    private String mw;

    /**
     * 信封
     */
    private String xf;

    /**
     * 申请信息key
     */
    private String key;

    /**
     * 设备id
     */
    private String deviceid;

    /**
     * 用户登录名
     */
    private String yhdlm;

    /**
     * 设备IP
     */
    private String sbip;

    /**
     * 设备秘钥
     */
    private String license;

    /**
     * 受理地数据归属单位代码
     */
    private String sldSjgsdwdm;

    /**
     * 受理地数据归属单位名称
     */
    private String sldSjgsdwmc;
}
