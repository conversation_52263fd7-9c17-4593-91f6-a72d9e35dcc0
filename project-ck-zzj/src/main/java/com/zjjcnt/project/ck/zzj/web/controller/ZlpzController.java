package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.project.ck.zzj.convert.ck.ZlpzConvert;
import com.zjjcnt.project.ck.zzj.domain.ZlpzUploadRes;
import com.zjjcnt.project.ck.zzj.manager.ZlpzManager;
import com.zjjcnt.project.ck.zzj.third.yzty.req.YztyMpzjReq;
import com.zjjcnt.project.ck.zzj.third.yzty.resp.YztyMpzjResp;
import com.zjjcnt.project.ck.zzj.web.request.*;
import com.zjjcnt.project.ck.zzj.web.response.ZlpzCrjZjResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Tag(name = "浙里拍照")
@RestController
@RequestMapping("/zlpz")
public class ZlpzController {

    private final ZlpzManager zlpzManager;

    @Operation(summary = "出入境照片上传 F51002")
    @PostMapping("uploadCrjzp")
    public CommonResult<ZlpzUploadRes> uploadCrjzp(@Validated @RequestBody CrjZpUploadReq req) {
        return CommonResult.success(zlpzManager.uploadCrjzp(req));
    }

    @Operation(summary = "交警照片上传 F51003")
    @PostMapping("uploadJjzp")
    public CommonResult<ZlpzUploadRes> uploadJjzp(@Validated @RequestBody JjZpUploadReq req) {
        return CommonResult.success(zlpzManager.uploadJjzp(req));
    }

    @Operation(summary = "照片上传 F60002")
    @PostMapping("uploadZp")
    public CommonResult<Boolean> uploadZp(@Validated @RequestBody ZlpzZpxxSaveReq req) {
        zlpzManager.zlpzZpxxSave(req);
        return CommonResult.success(true);
    }

    @Operation(summary = "出入境质检 F60006")
    @PostMapping("crjZj")
    public CommonResult<ZlpzCrjZjResp> crjZj(@Validated @RequestBody ZlpzCrjZjReq req) {
        return CommonResult.success(zlpzManager.crjZjYzty(req));
    }

    @Operation(summary = "一照通用母片质检 F60008")
    @PostMapping("yztyMpzj")
    public CommonResult<YztyMpzjResp> yztyMpzj(@Validated @RequestBody ZlpzMpzjReq req) {
        YztyMpzjReq csjMpzjReq = ZlpzConvert.INSTANCE.convert(req);
        return CommonResult.success(zlpzManager.yztyMpzj(csjMpzjReq));
    }

}
