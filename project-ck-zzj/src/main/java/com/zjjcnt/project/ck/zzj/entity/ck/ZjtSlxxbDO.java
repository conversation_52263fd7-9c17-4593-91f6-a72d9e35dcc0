package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 居民身份证受理信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO
 */
@Crypto
@Data
@Table("zjt_slxxb")
public class ZjtSlxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = -2393263877796378527L;

    /**
     * 内部受理ID
     */
    @Id(keyType = KeyType.None)
    private String nbslid;

    /**
     *
     */
    @Column(value = "ywslh")
    private String ywslh;

    /**
     * 内部身份证ID
     */
    @Column(value = "nbsfzid")
    private String nbsfzid;

    /**
     * 照片ID
     */
    @Column(value = "zpid")
    private String zpid;

    /**
     * 受理号
     */
    @Column(value = "slh")
    private String slh;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     *
     */
    @Column(value = "rynbid")
    private String rynbid;

    /**
     * 签发机关
     */
    @Column(value = "qfjg")
    private String qfjg;

    /**
     * 有效期限起始日期
     */
    @Column(value = "yxqxqsrq")
    private String yxqxqsrq;

    /**
     * 有效期限截止日期
     */
    @Column(value = "yxqxjzrq")
    private String yxqxjzrq;

    /**
     * 住址
     */
    @Column(value = "zz")
    private String zz;

    /**
     * 户籍地址区划内详细地址
     */
    @Column(value = "hjdzqhnxxdz")
    private String hjdzqhnxxdz;

    /**
     * 申领原因
     */
    @Column(value = "slyy")
    private String slyy;

    /**
     * 制证类型
     */
    @Column(value = "zzlx")
    private String zzlx;

    /**
     * 领证方式
     */
    @Column(value = "lqfs")
    private String lqfs;

    /**
     * 收费类型
     */
    @Column(value = "sflx")
    private String sflx;

    /**
     * 收费金额
     */
    @Column(value = "sfje")
    private BigDecimal sfje;

    /**
     * 数据包流水号
     */
    @Column(value = "sjblsh")
    private String sjblsh;

    /**
     * 受理状态
     */
    @Column(value = "slzt")
    private String slzt;

    /**
     * 同步标志
     */
    @Column(value = "tbbz")
    private Integer tbbz;

    /**
     * 区域范围代码
     */
    @Column(value = "qyfwdm")
    private String qyfwdm;

    /**
     * 双语证标志
     */
    @Column(value = "syzbz")
    private String syzbz;

    /**
     * 电子整档标志
     */
    @Column(value = "dzzdbz")
    private String dzzdbz;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 民族
     */
    @Column(value = "mz")
    private String mz;

    /**
     * 民族附加项代码
     */
    @Column(value = "mzfjxdm")
    private String mzfjxdm;

    /**
     * 出生日期
     */
    @Column(value = "csrq")
    private String csrq;

    /**
     * 出生地省市县（区）
     */
    @Column(value = "csdssxq")
    private String csdssxq;

    /**
     *
     */
    @Column(value = "mlpnbid")
    private String mlpnbid;

    /**
     * 省市县（区）
     */
    @Column(value = "ssxq")
    private String ssxq;

    /**
     * 街路巷
     */
    @Column(value = "jlx")
    private String jlx;

    /**
     * 门（楼）牌号
     */
    @Column(value = "mlph")
    private String mlph;

    /**
     * 门（楼）详址
     */
    @Column(value = "mlxz")
    private String mlxz;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 责任区
     */
    @Column(value = "zrq")
    private String zrq;

    /**
     * 乡镇（街道）
     */
    @Column(value = "xzjd")
    private String xzjd;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 排序号
     */
    @Column(value = "pxh")
    private String pxh;

    /**
     * 业务标志
     */
    @Column(value = "ywbz")
    private String ywbz;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     *
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 单位代码
     */
    @Column(value = "dwdm")
    private String dwdm;

    /**
     * 收件人姓名
     */
    @Crypto
    @Column(value = "sjrxm")
    private String sjrxm;

    /**
     * 收件人联系电话
     */
    @Crypto
    @Column(value = "sjrlxdh")
    private String sjrlxdh;

    /**
     * 收件人邮编
     */
    @Column(value = "sjryb")
    private String sjryb;

    /**
     *
     */
    @Column(value = "sjrssxq")
    private String sjrssxq;

    /**
     *
     */
    @Column(value = "sjrxz")
    private String sjrxz;

    /**
     * 收件人通讯地址
     */
    @Column(value = "sjrtxdz")
    private String sjrtxdz;

    /**
     * 制证信息错误类别
     */
    @Column(value = "zzxxcwlb")
    private String zzxxcwlb;

    /**
     * 错误描述
     */
    @Column(value = "cwms")
    private String cwms;

    /**
     * 检验单位
     */
    @Column(value = "jydw")
    private String jydw;

    /**
     * 检验人姓名
     */
    @Crypto
    @Column(value = "jyrxm")
    private String jyrxm;

    /**
     * 检验日期
     */
    @Column(value = "jyrq")
    private String jyrq;

    /**
     * 处理单位
     */
    @Column(value = "cldw")
    private String cldw;

    /**
     * 处理情况
     */
    @Column(value = "clqk")
    private String clqk;

    /**
     * 处理日期
     */
    @Column(value = "clrq")
    private String clrq;

    /**
     * 质量回馈状态
     */
    @Column(value = "zlhkzt")
    private String zlhkzt;

    /**
     * 回馈时间
     */
    @Column(value = "hksj")
    private String hksj;

    /**
     *
     */
    @Column(value = "bwbha")
    private String bwbha;

    /**
     *
     */
    @Column(value = "bwbhb")
    private String bwbhb;

    /**
     * 审核日期
     */
    @Column(value = "shrq")
    private String shrq;

    /**
     * 省厅接收时间
     */
    @Column(value = "stjssj")
    private String stjssj;

    /**
     *
     */
    @Column(value = "bwbhc")
    private String bwbhc;

    /**
     * 分拣批次号
     */
    @Column(value = "fjpch")
    private String fjpch;

    /**
     *
     */
    @Column(value = "rlbdid")
    private String rlbdid;

    /**
     * 人脸比对ID
     */
    @Column(value = "rlbdbz")
    private String rlbdbz;

    /**
     * 人脸比对标志
     */
    @Column(value = "rlbdsj")
    private String rlbdsj;

    /**
     * 指纹一指位
     */
    @Column(value = "zwyzw")
    private String zwyzw;

    /**
     * 指纹一注册结果
     */
    @Column(value = "zwyzcjg")
    private String zwyzcjg;

    /**
     * 指纹二指位
     */
    @Column(value = "zwezw")
    private String zwezw;

    /**
     * 指纹二注册结果
     */
    @Column(value = "zwezcjg")
    private String zwezcjg;

    /**
     * 指纹采集结果代码
     */
    @Column(value = "zwcjjgdm")
    private String zwcjjgdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "szyczkdm")
    private String szyczkdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "sfzwzj")
    private String sfzwzj;

    /**
     *
     */
    @Column(value = "dztbbz")
    private BigDecimal dztbbz;

    /**
     * 对账同步标志
     */
    @Column(value = "dztbsj")
    private String dztbsj;

    /**
     * 对账同步时间
     */
    @Column(value = "dzsjbbh")
    private String dzsjbbh;

    /**
     *
     */
    @Column(value = "slfs")
    private String slfs;

    /**
     * 受理方式新
     */
    @Column(value = "slfsx")
    private String slfsx;

    /**
     * 指纹图像临时ID
     */
    @Column(value = "zwtxid")
    private String zwtxid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     *
     */
    @Column(value = "hjddzbm")
    private String hjddzbm;

    /**
     *
     */
    @Column(value = "lssfzslbz")
    private String lssfzslbz;

    /**
     *
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     *
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     *
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     *
     */
    @Column(value = "zjzdssxq")
    private String zjzdssxq;

    /**
     *
     */
    @Column(value = "zjzdxz")
    private String zjzdxz;

    /**
     * 领取日期
     */
    @Column(value = "lqrq")
    private String lqrq;

    /**
     * 领取人姓名
     */
    @Crypto
    @Column(value = "lqrxm")
    private String lqrxm;

    /**
     * 领取人身份号码
     */
    @Column(value = "lqrsfhm")
    private String lqrsfhm;

    /**
     * 领取人照片ID
     */
    @Column(value = "lqrzpid")
    private String lqrzpid;

    /**
     * 证件到达日期
     */
    @Column(value = "zjddrq")
    private String zjddrq;

    /**
     *
     */
    @Column(value = "lzczrid")
    private String lzczrid;

    /**
     *
     */
    @Crypto
    @Column(value = "lzczrxm")
    private String lzczrxm;

    /**
     * 领证操作人单位代码
     */
    @Column(value = "lzczrdwdm")
    private String lzczrdwdm;

    /**
     * 领证操作人单位名称
     */
    @Column(value = "lzczrdwmc")
    private String lzczrdwmc;

    /**
     * 审核人姓名
     */
    @Crypto
    @Column(value = "shrxm")
    private String shrxm;

    /**
     * 审核单位
     */
    @Column(value = "shdw")
    private String shdw;

    /**
     * 审核情况
     */
    @Column(value = "shqk")
    private String shqk;

    /**
     * 签发日期
     */
    @Column(value = "qfrq")
    private String qfrq;

    /**
     * 签发人ID
     */
    @Column(value = "qfrid")
    private String qfrid;

    /**
     * 审核人姓名
     */
    @Crypto
    @Column(value = "qfrxm")
    private String qfrxm;

    /**
     * 签发单位机构代码
     */
    @Column(value = "qfdwjgdm")
    private String qfdwjgdm;

    /**
     * 签发单位
     */
    @Column(value = "qfdw")
    private String qfdw;

    /**
     * 审核日期
     */
    @Column(value = "dsshrq")
    private String dsshrq;

    /**
     * 审核人姓名
     */
    @Crypto
    @Column(value = "dsshrxm")
    private String dsshrxm;

    /**
     * 审核单位
     */
    @Column(value = "dsshdw")
    private String dsshdw;

    /**
     * 审核情况
     */
    @Column(value = "dsshqk")
    private String dsshqk;

    /**
     *
     */
    @Column(value = "rwid")
    private String rwid;

    /**
     *
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     *
     */
    @Column(value = "sldfjsjgsdwdm")
    private String sldfjsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldfjsjgsdwmc")
    private String sldfjsjgsdwmc;

    /**
     *
     */
    @Column(value = "shdwdm")
    private String shdwdm;

    /**
     *
     */
    @Column(value = "cldwdm")
    private String cldwdm;

    /**
     *
     */
    @Column(value = "dsshdwdm")
    private String dsshdwdm;

    /**
     *
     */
    @Column(value = "shrid")
    private String shrid;

    /**
     *
     */
    @Column(value = "dsshrid")
    private String dsshrid;

    /**
     * 收费单据号
     */
    @Column(value = "sfdjh")
    private String sfdjh;

    /**
     * 任务执行日志编号
     */
    @Column(value = "rwzxrzbh")
    private String rwzxrzbh;

    /**
     * 任务调度时间
     */
    @Column(value = "rwddsj")
    private String rwddsj;

    /**
     *
     */
    @Column(value = "slyckrxsfbd")
    private String slyckrxsfbd;

    /**
     *
     */
    @Column(value = "rxbdkssj")
    private String rxbdkssj;

    /**
     *
     */
    @Column(value = "rxbdhs")
    private String rxbdhs;

    /**
     *
     */
    @Column(value = "rxbdxsd")
    private String rxbdxsd;

    /**
     *
     */
    @Column(value = "rxbdkbh")
    private String rxbdkbh;

    /**
     *
     */
    @Column(value = "rxbdjg")
    private String rxbdjg;

    /**
     *
     */
    @Column(value = "slylszwsfbd")
    private String slylszwsfbd;

    /**
     *
     */
    @Column(value = "zwybdjg")
    private String zwybdjg;

    /**
     *
     */
    @Column(value = "zwybdxsd")
    private String zwybdxsd;

    /**
     *
     */
    @Column(value = "zwebdjg")
    private String zwebdjg;

    /**
     *
     */
    @Column(value = "zwebdxsd")
    private String zwebdxsd;

    /**
     *
     */
    @Column(value = "lzszwsfhy")
    private String lzszwsfhy;

    /**
     *
     */
    @Column(value = "lzszwyhyjg")
    private String lzszwyhyjg;

    /**
     *
     */
    @Column(value = "lzszwyhyxsd")
    private String lzszwyhyxsd;

    /**
     *
     */
    @Column(value = "lzszwehyjg")
    private String lzszwehyjg;

    /**
     *
     */
    @Column(value = "lzszwehyxsd")
    private String lzszwehyxsd;

    /**
     *
     */
    @Column(value = "lzssfjhjz")
    private String lzssfjhjz;

    /**
     *
     */
    @Column(value = "slylszwbdsm")
    private String slylszwbdsm;

    /**
     *
     */
    @Column(value = "lzszwbdsm")
    private String lzszwbdsm;

    /**
     * 申请人_公民身份号码
     */
    @Crypto
    @Column(value = "sqrgmsfhm")
    private String sqrgmsfhm;

    /**
     * 申请人_姓名
     */
    @Crypto
    @Column(value = "sqrxm")
    private String sqrxm;

    /**
     *
     */
    @Crypto
    @Column(value = "sqrlxdh")
    private String sqrlxdh;

    /**
     * 旧证起始日期
     */
    @Column(value = "jzqsrq")
    private String jzqsrq;

    /**
     * 操作员联系电话
     */
    @Crypto
    @Column(value = "czylxdh")
    private String czylxdh;

    /**
     * 区县审核联系电话
     */
    @Crypto
    @Column(value = "shrlxdh")
    private String shrlxdh;

    /**
     * 地市审核联系电话
     */
    @Crypto
    @Column(value = "dsshrlxdh")
    private String dsshrlxdh;

    /**
     * 总分成金额
     */
    @Column(value = "zfcje")
    private BigDecimal zfcje;

    /**
     * 区县分成金额
     */
    @Column(value = "qxfcje")
    private BigDecimal qxfcje;

    /**
     * 地市分成金额
     */
    @Column(value = "dsfcje")
    private BigDecimal dsfcje;

    /**
     * 中心分成金额
     */
    @Column(value = "zxfcje")
    private BigDecimal zxfcje;

    /**
     * 邮政快递单号
     */
    @Column(value = "yzkddh")
    private String yzkddh;

    /**
     *
     */
    @Column(value = "spdz1")
    private String spdz1;

    /**
     * 是否满十八岁无照片
     */
    @Column(value = "sfmsbswzp")
    private String sfmsbswzp;

    /**
     * 互联网申请id
     */
    @Column(value = "hlwsqid")
    private String hlwsqid;

    /**
     * 第三方用户名称
     */
    @Column(value = "username")
    private String username;

    /**
     * 评价结果
     */
    @Column(value = "pjjg")
    private String pjjg;

    /**
     * 跑了几次评价
     */
    @Column(value = "pjpljc")
    private String pjpljc;

    /**
     * 评价时间
     */
    @Column(value = "pjsj")
    private String pjsj;

    /**
     * 服务对象
     */
    @Column(value = "fwdx")
    private String fwdx;

    /**
     * 是否待挂失证件0否1是
     */
    @Column(value = "sfdgszj")
    private String sfdgszj;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     *
     */
    @Column(value = "slshjdz")
    private String slshjdz;

    /**
     *
     */
    @Column(value = "ksywlsh")
    private String ksywlsh;

    /**
     *
     */
    @Column(value = "ksfsbz")
    private String ksfsbz;

    /**
     *
     */
    @Column(value = "ksfssj")
    private String ksfssj;

    /**
     *
     */
//    @Column(value = "base64zp")
//    private byte[] base64zp;

    /**
     * 临时照片表ID
     */
    @Column(value = "zplsid")
    private String zplsid;

    /**
     * 照片采集类型
     */
    @Column(value = "zpcjlx")
    private String zpcjlx;

    /**
     * 照片设备标识号
     */
    @Column(value = "zpsbbsh")
    private String zpsbbsh;

    /**
     * 照片设备品牌型号编码
     */
    @Column(value = "zpsbppxhdm")
    private String zpsbppxhdm;

    /**
     * 照片设备品牌型号
     */
    @Column(value = "zpsbppxh")
    private String zpsbppxh;

    /**
     *
     */
    @Column(value = "zpytbid")
    private Long zpytbid;

    /**
     * 是否保存照片原图
     */
    @Column(value = "sfbczpyt")
    private String sfbczpyt;

    /**
     * 离线采集时间
     */
    @Column(value = "lxcjsj")
    private String lxcjsj;

    /**
     * 离线操作员名称
     */
    @Column(value = "lxczymc")
    private String lxczymc;

    /**
     * 离线受理单位
     */
    @Column(value = "lxsldw")
    private String lxsldw;

    /**
     * 照片设备编号
     */
    @Column(value = "zpsbbh")
    private String zpsbbh;

    /**
     * 照片设备注册单位
     */
    @Column(value = "zpsbzcdw")
    private String zpsbzcdw;

    /**
     * 照片设备生产公司
     */
    @Column(value = "zpsbscgs")
    private String zpsbscgs;

    /**
     * 照片设备销售公司
     */
    @Column(value = "zpsbxsgs")
    private String zpsbxsgs;

    /**
     * 办证设备编号
     */
    @Column(value = "bzsbbh")
    private String bzsbbh;

    /**
     * 办证设备注册单位
     */
    @Column(value = "bzsbzcdw")
    private String bzsbzcdw;

    /**
     * 办证设备品牌型号编码
     */
    @Column(value = "bzsbppxhdm")
    private String bzsbppxhdm;

    /**
     * 办证设备品牌型号
     */
    @Column(value = "bzsbppxh")
    private String bzsbppxh;

    /**
     * 办证设备生产公司
     */
    @Column(value = "bzsbscgs")
    private String bzsbscgs;

    /**
     * 办证设备销售公司
     */
    @Column(value = "bzsbxsgs")
    private String bzsbxsgs;

    /**
     * 紧急联系人姓名
     */
    @Crypto
    @Column(value = "jjlxrxm")
    private String jjlxrxm;

    /**
     * 紧急联系人电话
     */
    @Column(value = "jjlxrdh")
    private String jjlxrdh;

    /**
     * 紧急联系人与受理人关系
     */
    @Column(value = "jjlxryslrgx")
    private String jjlxryslrgx;

    /**
     * 治安管理业务协同编号
     */
    @Column(value = "zaglywxtbh")
    private String zaglywxtbh;

    /**
     * 治安管理政务服务事项编码
     */
    @Column(value = "zaglzwfwsxbm")
    private String zaglzwfwsxbm;

    /**
     * 治安管理业务类别代码
     */
    @Column(value = "zaglywlbdm")
    private String zaglywlbdm;

    /**
     * 姓名（民族文字）
     */
    @Crypto
    @Column(value = "xmmzwz")
    private String xmmzwz;

    /**
     * 性别（民族文字）
     */
    @Column(value = "xbmzwz")
    private String xbmzwz;

    /**
     * 民族（民族文字）
     */
    @Column(value = "mzmzwz")
    private String mzmzwz;

    /**
     * 民族附加项（民族文字）
     */
    @Crypto
    @Column(value = "mzfjxmzwz")
    private String mzfjxmzwz;

    /**
     * 住址行一（民族文字）
     */
    @Column(value = "zzhymzwz")
    private String zzhymzwz;

    /**
     * 住址行二（民族文字）
     */
    @Column(value = "zzhemzwz")
    private String zzhemzwz;

    /**
     * 住址行三（民族文字）
     */
    @Column(value = "zzhsmzwz")
    private String zzhsmzwz;

    /**
     * 签发机关（民族文字）
     */
    @Column(value = "qfjgmzwz")
    private String qfjgmzwz;

    /**
     * 操作员ip
     */
    @Column(value = "czyip")
    private String czyip;

    /**
     * 指纹采集器编号
     */
    @Column(value = "zwcjqid")
    private String zwcjqid;

    /**
     * 是否申请绿通证
     */
    @Column(value = "sfsqltz")
    private String sfsqltz;

    /**
     * 绿通证审核结果
     */
    @Column(value = "ltzshjg")
    private String ltzshjg;

    /**
     * 绿通证审批时间
     */
    @Column(value = "ltzspsj")
    private String ltzspsj;

    /**
     * 绿通证审批人编号
     */
    @Column(value = "ltzsprid")
    private String ltzsprid;

    /**
     * 绿通证审批人姓名
     */
    @Crypto
    @Column(value = "ltzsprxm")
    private String ltzsprxm;

    /**
     * 绿通证申请事由
     */
    @Column(value = "ltzsqsy")
    private String ltzsqsy;

    /**
     * 绿通证申请原因
     */
    @Column(value = "ltzsqyy")
    private String ltzsqyy;

    /**
     * 绿通证申请时间
     */
    @Column(value = "ltzsqsj")
    private String ltzsqsj;

    /**
     * 辖区类型
     */
    @Column(value = "xqlx")
    private String xqlx;

    /**
     * 办证设备名称
     */
    @Column(value = "bzsbmc")
    private String bzsbmc;


    @Override
    public String getId() {
        return this.nbslid;
    }

    @Override
    public void setId(String id) {
        this.nbslid = id;
    }
}
