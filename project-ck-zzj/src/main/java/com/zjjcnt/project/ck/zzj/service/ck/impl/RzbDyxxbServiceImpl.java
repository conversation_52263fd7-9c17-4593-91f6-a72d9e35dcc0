package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.sysadmin.constant.CkConstants;
import com.zjjcnt.project.ck.zzj.convert.ck.RzbDyxxbConvert;
import com.zjjcnt.project.ck.sysadmin.dto.GmsfhmAndXmEntity;
import com.zjjcnt.project.ck.sysadmin.dto.RktYwslClysjDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.RzbDyxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.RzbDyxxbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.RzbDyxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.RzbDyxxbService;
import com.zjjcnt.project.ck.sysadmin.file.RktYwslClysjFileManager;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 打印信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@RequiredArgsConstructor
@Service
public class RzbDyxxbServiceImpl extends ExBaseServiceImpl<RzbDyxxbMapper, RzbDyxxbDO, RzbDyxxbDTO> implements RzbDyxxbService {

    RzbDyxxbConvert convert = RzbDyxxbConvert.INSTANCE;

    private final RktYwslClysjFileManager fileManager;

    @Override
    protected QueryWrapper genQueryWrapper(RzbDyxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(RzbDyxxbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(RzbDyxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(RzbDyxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(RzbDyxxbDO::getDylb, dto.getDylb(), StringUtils.isNotEmpty(dto.getDylb()));
        query.eq(RzbDyxxbDO::getZjbh, dto.getZjbh(), StringUtils.isNotEmpty(dto.getZjbh()));
        query.eq(RzbDyxxbDO::getYznf, dto.getYznf(), StringUtils.isNotEmpty(dto.getYznf()));
        query.ge(RzbDyxxbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(RzbDyxxbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(RzbDyxxbDO::getCzrid, dto.getCzrid(), StringUtils.isNotEmpty(dto.getCzrid()));
        query.eq(RzbDyxxbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(RzbDyxxbDO::getCzydwdm, dto.getCzydwdm(), StringUtils.isNotEmpty(dto.getCzydwdm()));
        query.eq(RzbDyxxbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.eq(RzbDyxxbDO::getCzip, dto.getCzip(), StringUtils.isNotEmpty(dto.getCzip()));
        query.eq(RzbDyxxbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(RzbDyxxbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(RzbDyxxbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(RzbDyxxbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(RzbDyxxbDO::getYwslh, dto.getYwslh(), StringUtils.isNotEmpty(dto.getYwslh()));
        query.eq(RzbDyxxbDO::getSlclbh, dto.getSlclbh(), StringUtils.isNotEmpty(dto.getSlclbh()));
        query.eq(RzbDyxxbDO::getClmc, dto.getClmc(), StringUtils.isNotEmpty(dto.getClmc()));
        query.eq(RzbDyxxbDO::getLcywlx, dto.getLcywlx(), StringUtils.isNotEmpty(dto.getLcywlx()));
        return query;
    }

    @Override
    public RzbDyxxbDTO convertToDTO(RzbDyxxbDO rzbDyxxbDO) {
        return convert.convert(rzbDyxxbDO);
    }

    @Override
    public RzbDyxxbDO convertToDO(RzbDyxxbDTO rzbDyxxbDTO) {
        return convert.convertToDO(rzbDyxxbDTO);
    }

    @Override
    protected void beforeInsert(RzbDyxxbDTO dto) {
        super.beforeInsert(dto);
        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        dto.setCzsj(ServerTimeUtils.getCurrentTime());
        dto.setCzrid(currentUser.getUserId() + "");
        dto.setCzyxm(currentUser.getName());
        dto.setCzydwdm(currentUser.getDeptCode());
        dto.setCzydwmc(currentUser.getDeptName());
        dto.setCzip(currentUser.getRemoteAddress());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RzbDyxxbDTO insert(RzbDyxxbDTO rzbDyxxb, List<RktYwslClysjDTO> clysjList) throws ServiceException {
        RzbDyxxbDTO result = super.insert(rzbDyxxb);

        if (!CollectionUtils.isEmpty(clysjList)) {
            clysjList.forEach(clysj -> {

                //文件类型为空的进行补全
                if (StringUtils.isEmpty(clysj.getWjlx())) {
                    clysj.setWjlx(MediaType.IMAGE_JPEG_VALUE);
                }

                clysj.setYwslh(result.getDyid()); //将DYID设置为YWSLH
                //流程业务类型设置为打印信息
                if (StringUtils.isEmpty(clysj.getCllylx())) {
                    clysj.setCllylx(CkConstants.CKCLLYLX_DYWD);
                }
                if (StringUtils.isEmpty(clysj.getLcywlx())) {
                    throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "材料员数据的LCYWLX不能为空.");
                }
                //前台传入
                //clysj.setLcywlx(CkConstants.LCYWLX_RZB_DYXX);
            });
            fileManager.save(clysjList);
        }
        return result;
    }

    @Override
    public String[] getLcywlx() {
        return new String[]{CkConstants.LCYWLX_RZB_DYXX};
    }

    @Override
    public List<? extends GmsfhmAndXmEntity> findValidByYwslh(String ywslh) {
        RzbDyxxbDTO rzbDyxxbDTO = findById(ywslh);
        if (Objects.isNull(rzbDyxxbDTO)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(rzbDyxxbDTO);
    }
}
