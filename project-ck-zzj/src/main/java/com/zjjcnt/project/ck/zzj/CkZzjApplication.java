package com.zjjcnt.project.ck.zzj;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动类
 *
 * <AUTHOR>
 * @date 2024-04-01 11:19:00
 */
@EnableScheduling
@ComponentScan(basePackages = "com.zjjcnt.project.ck.**")
@MapperScan("com.zjjcnt.project.**.mapper")
@SpringBootApplication
public class CkZzjApplication {

    public static void main(String[] args) {
        SpringApplication.run(CkZzjApplication.class, args);
    }
}
