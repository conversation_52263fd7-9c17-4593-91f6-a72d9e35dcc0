package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 人员照片信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 11:35:02
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxRyzpxxbDTO
 */
@Crypto
@Data
@Table("hjxx_ryzpxxb")
public class HjxxRyzpxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -2025511206967433620L;

    /**
     * 照片ID
     */
    @Id(keyType = KeyType.Auto)
    private Long zpid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private Long ryid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 录入时间
     */
    @Column(value = "zp")
    private byte[] zp;

    /**
     * 照片
     */
    @Column(value = "lrrq")
    private String lrrq;

    /**
     *
     */
    @Column(value = "docfrom")
    private String docfrom;

    /**
     *
     */
    @Column(value = "zpdx")
    private Long zpdx;

    /**
     *
     */
    @Column(value = "zphash")
    private String zphash;

    /**
     *
     */
    @Column(value = "zpsjdzlx")
    private String zpsjdzlx;

    /**
     *
     */
    @Column(value = "zpsjdz")
    private String zpsjdz;

    /**
     *
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     *
     */
    @Column(value = "sjgsdwdm")
    private String sjgsdwdm;

    /**
     *
     */
    @Column(value = "scsj")
    private String scsj;

    /**
     *
     */
    @Column(value = "scrid")
    private Long scrid;

    /**
     *
     */
    @Column(value = "scrip")
    private String scrip;

    /**
     *
     */
    @Column(value = "scrdwdm")
    private String scrdwdm;


    @Override
    public Long getId() {
        return this.zpid;
    }

    @Override
    public void setId(Long id) {
        this.zpid = id;
    }
}
