package com.zjjcnt.project.ck.zzj.service.ck;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.zzj.constant.ZjtBzsflsbType;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtBzsflsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;

/**
 * 办证收费流水表Service
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
public interface ZjtBzsflsbService extends IBaseService<ZjtBzsflsbDTO> {

    ZjtBzsflsbDTO insert(ZjtSlxxbDTO zjtSlxxb, ZjtBzsflsbType type);
}
