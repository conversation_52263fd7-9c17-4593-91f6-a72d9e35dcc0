package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.zaxxer.hikari.HikariDataSource;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxCzrkjbxxbService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.mybatisflex.core.query.QueryWrapper;

import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.service.ckdshz.WwTfbdjkxxbService;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.WwTfbdjkxxbDO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwTfbdjkxxbDTO;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.WwTfbdjkxxbConvert;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.WwTfbdjkxxbMapper;

import javax.sql.DataSource;
import java.util.*;

/**
 * 逃犯比对接口信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@RequiredArgsConstructor
@Service
public class WwTfbdjkxxbServiceImpl extends AbstractBaseServiceImpl<WwTfbdjkxxbMapper, WwTfbdjkxxbDO, WwTfbdjkxxbDTO> implements WwTfbdjkxxbService {

    private final HjxxCzrkjbxxbService hjxxCzrkjbxxbService;

    WwTfbdjkxxbConvert convert = WwTfbdjkxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(WwTfbdjkxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(WwTfbdjkxxbDO::getJkjndi, dto.getJkjndi(), Objects.nonNull(dto.getJkjndi()));
        query.eq(WwTfbdjkxxbDO::getCxyj, dto.getCxyj(), Objects.nonNull(dto.getCxyj()));
        query.eq(WwTfbdjkxxbDO::getQybz, dto.getQybz(), Objects.nonNull(dto.getQybz()));
        query.eq(WwTfbdjkxxbDO::getBz, dto.getBz(), Objects.nonNull(dto.getBz()));
        query.eq(WwTfbdjkxxbDO::getFhts, dto.getFhts(), Objects.nonNull(dto.getFhts()));
        return query;
    }

    @Override
    public WwTfbdjkxxbDTO convertToDTO(WwTfbdjkxxbDO wwTfbdjkxxbDO) {
        return convert.convert(wwTfbdjkxxbDO);
    }

    @Override
    public WwTfbdjkxxbDO convertToDO(WwTfbdjkxxbDTO wwTfbdjkxxbDTO) {
        return convert.convertToDO(wwTfbdjkxxbDTO);
    }

    public void tfbd(String gmsfhm) {
        HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO = new HjxxCzrkjbxxbDTO();
        hjxxCzrkjbxxbDTO.setRyzt("0");
        hjxxCzrkjbxxbDTO.setJlbz("1");
        hjxxCzrkjbxxbDTO.setCxbz("0");
        hjxxCzrkjbxxbDTO.setGmsfhm(gmsfhm);
        HjxxCzrkjbxxbDTO findDTO = hjxxCzrkjbxxbService.find(hjxxCzrkjbxxbDTO);
        if (findDTO != null) {
            // 比对逃犯库
        }
    }

    private void bdTfk(HjxxCzrkjbxxbDTO czrkjbxxbDTO){
        // 查询第三方库比对信息
        WwTfbdjkxxbDTO tfbdjkxxbDTO = new WwTfbdjkxxbDTO();
        tfbdjkxxbDTO.setQybz("1");
        QueryWrapper query = QueryWrapper.create();
        query.eq(WwTfbdjkxxbDO::getQybz, "1");
        query.orderBy(WwTfbdjkxxbDO::getJkid, true);
        List<WwTfbdjkxxbDTO> tfbdjkxxbDTOList = convertToDTO(list(query));

        if (tfbdjkxxbDTOList != null){
            for (WwTfbdjkxxbDTO wwTfbdjkxxbDTO : tfbdjkxxbDTOList) {
                DataSource dataSource1 = new HikariDataSource();
//                dataSource1.setus
//                MybatisFlexBootstrap.getInstance().
//                DataSourceKey.

            }
        }
    }
}
