package com.zjjcnt.project.ck.zzj.util;

import com.zjjcnt.project.ck.jk.ws.util.JkJAXBUtils;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

public class SerializeUtils {


    private static Map<Class<?>, JAXBContext> contextMap = new HashMap<>();
    /**
     * JAXBContext为线程安全，且很消耗资源，所以利用单例模式
     * Marshaller 非线程安全，所以不能用单例模式
     */
    public static JAXBContext getJAXBContextInstance(Class<?> clazz) {
        /**缓存JAXBContext todo:利用apache-pool进行缓存池控制*/
        if (contextMap.get(clazz) == null) {
            synchronized (clazz) {
                if (contextMap.get(clazz) == null) {
                    try {
                        contextMap.put(clazz, JAXBContext.newInstance(clazz));
                    } catch (JAXBException e) {
                        throw new JkJAXBUtils.JAXBConvertException(e.getMessage(), e);
                    }
                }
            }
        }
        return contextMap.get(clazz);

    }

    public static String toXML(Object obj,String encoding, boolean formatted) {
        try {

            Marshaller marshaller = null;
            marshaller = getJAXBContextInstance(obj.getClass()).createMarshaller();

            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, formatted);
            marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);

            StringWriter sw = new StringWriter();
            marshaller.marshal(obj, sw);

            return sw.toString();
        } catch (JAXBException e) {
            throw new JkJAXBUtils.JAXBConvertException(e.getMessage(), e);
        }
    }

    public static <T> T parseXML(String xml, Class<T> clazz) {
        try {
            Unmarshaller unmarshaller = getJAXBContextInstance(clazz).createUnmarshaller();
            return (T) unmarshaller.unmarshal(new StringReader(xml));
        } catch (JAXBException e) {
            throw new JkJAXBUtils.JAXBConvertException(e.getMessage(), e);
        }
    }
}
