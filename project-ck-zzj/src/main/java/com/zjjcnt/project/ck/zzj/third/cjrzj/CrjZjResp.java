package com.zjjcnt.project.ck.zzj.third.cjrzj;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 返回结果
 *
 * <AUTHOR>
 * @date 2024-09-20 10:31:00
 */
@Data
public class CrjZjResp {

    private Boolean ok;
    private Integer code;
    private String message;
    private Double duration;

    @JsonProperty("image_base64")
    private String imageBase64;

    @JsonProperty("module_name")
    private String moduleName;

    @JsonProperty("crop_ok")
    private Boolean cropOk;

    @JsonProperty("crop_message")
    private String cropMessage;

    @JsonProperty("crop_error")
    private String cropError;

    private String warning;

    @JsonProperty("meas_ret")
    private String measRet;

    private String details;
}