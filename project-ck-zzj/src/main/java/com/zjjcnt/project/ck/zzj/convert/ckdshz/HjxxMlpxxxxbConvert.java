package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxMlpxxxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxMlpxxxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxMlpxxxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxMlpxxxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxMlpxxxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxMlpxxxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxMlpxxxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxMlpxxxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 门（楼）牌详细信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface HjxxMlpxxxxbConvert {

    HjxxMlpxxxxbConvert INSTANCE = Mappers.getMapper(HjxxMlpxxxxbConvert.class);

    HjxxMlpxxxxbDTO convert(HjxxMlpxxxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    HjxxMlpxxxxbDO convertToDO(HjxxMlpxxxxbDTO dto);

    HjxxMlpxxxxbDTO convertToDTO(HjxxMlpxxxxbPageReq req);

    HjxxMlpxxxxbDTO convertToDTO(HjxxMlpxxxxbCreateReq req);

    HjxxMlpxxxxbDTO convertToDTO(HjxxMlpxxxxbUpdateReq req);

    HjxxMlpxxxxbPageResp convertToPageResp(HjxxMlpxxxxbDTO dto);

    HjxxMlpxxxxbViewResp convertToViewResp(HjxxMlpxxxxbDTO dto);

    HjxxMlpxxxxbCreateResp convertToCreateResp(HjxxMlpxxxxbDTO dto);

}
