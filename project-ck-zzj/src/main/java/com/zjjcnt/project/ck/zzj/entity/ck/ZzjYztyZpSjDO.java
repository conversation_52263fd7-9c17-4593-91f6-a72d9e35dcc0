package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 自助机一照通用照片数据DO
 *
 * <AUTHOR>
 * @date 2025-01-14 16:13:45
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZzjYztyZpSjDTO
 */
@Data
@Table("zzj_yzty_zp_sj")
public class ZzjYztyZpSjDO implements IdEntity<Long> {
    private static final long serialVersionUID = 228106074628087417L;

    /**
     * id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 照片数据
     */
    @Column(value = "zpsj")
    private byte[] zpsj;


    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
