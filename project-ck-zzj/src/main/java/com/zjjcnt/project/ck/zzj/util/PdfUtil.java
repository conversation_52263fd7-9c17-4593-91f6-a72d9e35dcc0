package com.zjjcnt.project.ck.zzj.util;

import cn.hutool.core.codec.Base64Decoder;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Element;
import com.itextpdf.text.pdf.*;

import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Base64;

/**
 * PDF工具类
 * 增加水印功能
 */

public class PdfUtil {

    public static String addWaterMark(String base64Input, String[] waterContennts) {
        try {
            return addWaterMark(base64Input, waterContennts, 6, BaseColor.DARK_GRAY);
        } catch (Exception e) {
            e.printStackTrace();
            return base64Input;
        }
    }

    public static String addWaterMark(String base64Input, String[] waterContennts, int numberOfPage, BaseColor baseColor) throws Exception {
        PdfReader pdfReader = new PdfReader(base64ToInputStream(base64Input));
//        PdfReader pdfReader = new PdfReader(srcPath);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        BufferedOutputStream bos = new BufferedOutputStream(baos);
        PdfStamper pdfStamper = new PdfStamper(pdfReader, bos);
        PdfGState pdfGState = new PdfGState();

        // 设置字体
        BaseFont font = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

        // 设置透明度
        pdfGState.setFillOpacity(0.2f);

        int total = pdfReader.getNumberOfPages() + 1;
        PdfContentByte content;
        for (int i = 1; i < total; i++) {
            content = pdfStamper.getOverContent(i);
//            content = pdfStamper.getUnderContent(i);
            content.beginText();
            content.setGState(pdfGState);
            // 水印颜色
            content.setColorFill(baseColor);
            // 水印字体样式和大小
            content.setFontAndSize(font, 20);
            // 插入水印 循环每页插入条数
            int x, y = 0;
            for (int j = 0; j < numberOfPage; j++) {
                boolean temp = j % 2 == 0;
//                if (temp) {
//                    x = 150;
//                    y = y + 130;
//                } else {
//                    x = 450;
//                    y = y + 220;
//                }
                if (temp) {
                    x = 150;
                    y = y + 80;
                } else {
                    x = 450;
                    y = y + 170;
                }
//                x = 300;
//                y = 200*(j + 1);
                for (int i1 = 0; i1 < waterContennts.length; i1++) {
                    if (i1 == 0) {
                        content.showTextAligned(Element.ALIGN_CENTER, waterContennts[i1], x, y - 30 * i1, 20);
                    } else {
                        content.showTextAligned(Element.ALIGN_CENTER, waterContennts[i1], x + 10, y - 30 * i1, 20);
                    }
//                    content.showTextAligned(Element.ALIGN_CENTER, waterContennts[i1], x, y-30*i1, 20);
                }

            }
            content.endText();
        }

        pdfStamper.close();
        byte[] bytes = baos.toByteArray();
        baos.close();
        bos.close();
        pdfReader.close();

        return Base64.getEncoder().encodeToString(bytes);
    }


    public static InputStream base64ToInputStream(String base64String) {
        byte[] bytes = Base64Decoder.decode(base64String);
        ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
        return bais;
    }
}
