package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjYhsbpzbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYhsbpzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjYhsbpzbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZzjYhsbpzbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjYhsbpzbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 自助用户设备配置表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-07-19 14:06:40
 */
@Service
public class ZzjYhsbpzbServiceImpl extends ExBaseServiceImpl<ZzjYhsbpzbMapper, ZzjYhsbpzbDO, ZzjYhsbpzbDTO> implements ZzjYhsbpzbService {

    ZzjYhsbpzbConvert convert = ZzjYhsbpzbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZzjYhsbpzbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZzjYhsbpzbDO::getYhid, dto.getYhid(), Objects.nonNull(dto.getYhid()));
        query.eq(ZzjYhsbpzbDO::getSbpzid, dto.getSbpzid(), Objects.nonNull(dto.getSbpzid()));
        query.eq(ZzjYhsbpzbDO::getPzz, dto.getPzz(), StringUtils.isNotEmpty(dto.getPzz()));
        query.eq(ZzjYhsbpzbDO::getYxbz, dto.getYxbz(), StringUtils.isNotEmpty(dto.getYxbz()));
        return query;
    }

    @Override
    public ZzjYhsbpzbDTO convertToDTO(ZzjYhsbpzbDO zzjYhsbpzbDO) {
        return convert.convert(zzjYhsbpzbDO);
    }

    @Override
    public ZzjYhsbpzbDO convertToDO(ZzjYhsbpzbDTO zzjYhsbpzbDTO) {
        return convert.convertToDO(zzjYhsbpzbDTO);
    }

}
