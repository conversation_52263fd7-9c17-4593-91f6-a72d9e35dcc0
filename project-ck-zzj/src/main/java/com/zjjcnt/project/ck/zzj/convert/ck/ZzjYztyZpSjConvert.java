package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYztyZpSjDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjYztyZpSjDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 自助机一照通用照片数据Convert
*
* <AUTHOR>
* @date 2025-01-14 16:13:45
*/
@Mapper
public interface ZzjYztyZpSjConvert {

    ZzjYztyZpSjConvert INSTANCE = Mappers.getMapper(ZzjYztyZpSjConvert.class);

    ZzjYztyZpSjDTO convert(ZzjYztyZpSjDO entity);

    @InheritInverseConfiguration(name="convert")
    ZzjYztyZpSjDO convertToDO(ZzjYztyZpSjDTO dto);

}
