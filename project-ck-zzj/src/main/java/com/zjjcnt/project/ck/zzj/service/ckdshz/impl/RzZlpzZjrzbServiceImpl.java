package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.RzZlpzZjrzbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.RzZlpzZjrzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RzZlpzZjrzbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.RzZlpzZjrzbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RzZlpzZjrzbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 浙里拍照质检日志表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-12-12 11:04:00
 */
@Service
public class RzZlpzZjrzbServiceImpl extends AbstractBaseServiceImpl<RzZlpzZjrzbMapper, RzZlpzZjrzbDO, RzZlpzZjrzbDTO> implements RzZlpzZjrzbService {

    private final RzZlpzZjrzbConvert convert = RzZlpzZjrzbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(RzZlpzZjrzbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(RzZlpzZjrzbDO::getZjlx, dto.getZjlx(), StringUtils.isNotEmpty(dto.getZjlx()));
        return query;
    }

    @Override
    public RzZlpzZjrzbDTO convertToDTO(RzZlpzZjrzbDO entity) {
        return convert.convert(entity);
    }

    @Override
    public RzZlpzZjrzbDO convertToDO(RzZlpzZjrzbDTO dto) {
        return convert.convertToDO(dto);
    }
}