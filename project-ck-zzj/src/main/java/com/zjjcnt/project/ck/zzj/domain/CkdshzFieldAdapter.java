package com.zjjcnt.project.ck.zzj.domain;


import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import org.w3c.dom.Element;

import javax.xml.namespace.QName;

/**
 * 应该来说接口应该为XmlAdapter<Element, RowColumn>,但为了marshal方法可以利用JAXBElement特性
 * 即marshal方法：
 * JAXBElement jaxbElement = new JAXBElement(rootElement, type, value);
 * Document document = getDocumentBuilder().newDocument();
 * Marshaller marshaller = getJAXBContext(type).createMarshaller();
 * marshaller.marshal(jaxbElement, document);
 * Element element = document.getDocumentElement();
 * Create on 2017-02-13.
 *
 * <AUTHOR>
 */
public class CkdshzFieldAdapter extends XmlAdapter<Object, CkdshzJjzpFieldDto> {


    @Override
    public CkdshzJjzpFieldDto unmarshal(Object element) throws Exception {
        if (element == null) return null;
        Element domElement = (Element) element;
        String name = domElement.getTagName();
        String value = domElement.getTextContent();
        return new CkdshzJjzpFieldDto(name, value);
    }

    @Override
    public JAXBElement<CkdshzJjzpFieldDto> marshal(CkdshzJjzpFieldDto ckdshzJjzpFieldDto) throws Exception {
        if (ckdshzJjzpFieldDto == null) return null;
        /*

        与jackson xml处理行为保持一致，如果是null，则不序列化，参考


        DataJacksonSerializer
        for (Field field : record.getFieldList()) {
            if (field.getValue() == null) {
//                jgen.writeNullField(field.getName());
                //如果是null，不序列化
                continue;
            } else {
                jgen.writeStringField(field.getName(), StringUtils.defaultIfEmpty(field.getValue(),""));
            }
        }
         */
        if(ckdshzJjzpFieldDto.getValue() == null)return null;//


        //        如果是value = null，则会生成类似 <XM2 xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"/>
//        return new JAXBElement(new QName(column.getName()), String.class, column.getValue());
        return new JAXBElement(new QName(ckdshzJjzpFieldDto.getName()), CkdshzJjzpFieldDto.class, ckdshzJjzpFieldDto);
    }
}
