package com.zjjcnt.project.ck.zzj.manager;

import com.alibaba.fastjson2.JSON;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.project.ck.sysadmin.dto.XtDwxxbDTO;
import com.zjjcnt.project.ck.sysadmin.dto.XtXtkzcsbDTO;
import com.zjjcnt.project.ck.sysadmin.service.XtDwxxbService;
import com.zjjcnt.project.ck.sysadmin.service.XtXtkzcsbService;
import com.zjjcnt.project.ck.zzj.domain.VoPzyjsSxRegisterPointResponse;
import com.zjjcnt.project.ck.zzj.domain.VoPzyjsSxResponse;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.util.CkHttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 拍照一件事绍兴
 */
@Service
public class PzyjsSxManager {

    private Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private XtDwxxbService dwxxbService;

    @Autowired
    private XtXtkzcsbService xtXtkzcsbService;


    /**
     * 注册采集点
     * @param voZdsbxx
     * @param czlx
     * @return
     */
    public VoPzyjsSxRegisterPointResponse registerPoint(XtZzsbxxbDTO voZdsbxx, String czlx) {

        XtXtkzcsbDTO byKzlb = xtXtkzcsbService.findByKzlb("1627");
        String url = byKzlb.getBz() + "/api/third/v1/registerPoint";
        String appKey = byKzlb.getKzz();

        String sign = null;
        String requestTime = null;

        VoPzyjsSxResponse matterCode = getMatterCode();
        if ("true".equals(matterCode.getIsSuccess())) {

            String data = matterCode.getData();
            requestTime = data.substring(0, 10);

            sign = StringUtils.substringAfterLast(data, "*");

        } else if ("false".equals(matterCode.getIsSuccess())) {
            throw new ServiceException(500, "获取时间戳接口调用失败");
        }

        //组织参数
        Map<String, String> map = new HashMap<>();
        map.put("appKey", appKey);
        map.put("sign", sign);
        map.put("requestTime", requestTime);

        map.put("qqrXm", voZdsbxx.getZddjrxm());
        map.put("qqrSfzh", voZdsbxx.getZddjrgmsfhm());
        map.put("ip", voZdsbxx.getSbip());
        XtDwxxbDTO xtDwxxb = dwxxbService.findById(voZdsbxx.getPcs());

        map.put("scsjqx", xtDwxxb.getQhdm());
        map.put("dwjgdm", voZdsbxx.getDwjgdm());
        map.put("dwjgmc", xtDwxxb.getMc());
        map.put("sjfl", "2");
        map.put("cjdwmc", voZdsbxx.getSbsydmc());
        map.put("cjd", voZdsbxx.getSbsydz());
        map.put("macdz", voZdsbxx.getSbmac());
        map.put("is_fixed", "1");
        map.put("longitude", voZdsbxx.getJd());
        map.put("latitude", voZdsbxx.getWd());
        map.put("enable", "1");
        String now = DateTimeUtils.now("yyyy-MM-dd HH:mm:ss");
        map.put("dwbdsj", now);
        map.put("xzqhdm", xtDwxxb.getQhdm());
        map.put("xzqhmc", xtDwxxb.getBz());
        map.put("czlx", czlx);
        map.put("cjdwbh", "0");//采集点位编号
        map.put("sspcsdm", xtDwxxb.getDm());
        map.put("sspcsmc", xtDwxxb.getMc());
        map.put("sblx", voZdsbxx.getSblx());

        String fwckbh = voZdsbxx.getZlpzsbdwbh().substring(0, 12);
        map.put("fwckmc", voZdsbxx.getSbsydmc());//服务窗口名称  前端传递
        map.put("fwckbh", fwckbh);//服务窗口编号
        map.put("dwfwcklbdm", voZdsbxx.getSbsydlbdm());//服务窗口类别代码
        map.put("pointId", voZdsbxx.getWdid());//网点id
        // 构建请求参数
        StringBuilder data = new StringBuilder();

        for (Map.Entry<String, String> entry : map.entrySet()) {
            data.append(entry.getKey());
            data.append("=");
            data.append(entry.getValue());
            data.append("&");
        }

        String result = CkHttpUtils.sendPost1(url, data.toString(), "application/x-www-form-urlencoded", null);

        VoPzyjsSxResponse voPzyjsSxResponse = JSON.parseObject(result, VoPzyjsSxResponse.class);

        log.info("绍兴照片一件事注册采集点返回信息：" + result);

        if ("0".equals(voPzyjsSxResponse.getErrorCode())) {
            return JSON.parseObject(voPzyjsSxResponse.getData(), VoPzyjsSxRegisterPointResponse.class);
        } else {
            throw new ServiceException(CkZzjErrorCode.PZYJS_SX_ERROR, voPzyjsSxResponse.getMessage());
        }
    }


    /**
     * 获取时间戳和sign
     * @return
     * @throws ServiceException
     */
    private VoPzyjsSxResponse getMatterCode() throws ServiceException {
        VoPzyjsSxResponse voPzyjsSxResponse = new VoPzyjsSxResponse();
        try {
            XtXtkzcsbDTO byKzlb = xtXtkzcsbService.findByKzlb("1627");
            String url = byKzlb.getBz() + "/api/live";

            String result = CkHttpUtils.sendGet(url, "");

            if (result != null) {
                voPzyjsSxResponse = JSON.parseObject(result, VoPzyjsSxResponse.class);
            }

            //   log.info("获取时间戳和sign返回信息：" + voPzyjsSxResponse.getData());
        } catch (Exception e) {
            throw new ServiceException(CkZzjErrorCode.PZYJS_SX_ERROR, e.getMessage());
        }

        return voPzyjsSxResponse;
    }


    public String getfwcklbdmmc(String sbsydlbdm) {

        String mc = "";

        if ("42".equals(sbsydlbdm)) {
            mc = "行政服务中心综合窗口户籍+出入境";
        } else if ("43".equals(sbsydlbdm)) {
            mc = "行政服务中心综合窗口户籍+交管";
        } else if ("44".equals(sbsydlbdm)) {
            mc = "行政服务中心综合窗口户籍+出入境+交管";
        } else if ("51".equals(sbsydlbdm)) {
            mc = "派出所窗口户籍";
        } else if ("52".equals(sbsydlbdm)) {
            mc = "派出所窗口户籍+出入境延伸点";
        } else if ("61".equals(sbsydlbdm)) {
            mc = "便民服务点收件窗口";
        } else if ("62".equals(sbsydlbdm)) {
            mc = "便民服务点延伸服务点";
        } else if ("63".equals(sbsydlbdm)) {
            mc = "便民服务点其他";
        } else if ("11".equals(sbsydlbdm)) {
            mc = "出入境窗口";
        } else if ("21".equals(sbsydlbdm)) {
            mc = "户籍窗口";
        } else if ("31".equals(sbsydlbdm)) {
            mc = "交管窗口";
        }
        return mc;
    }


}
