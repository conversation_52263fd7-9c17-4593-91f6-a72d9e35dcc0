package com.zjjcnt.project.ck.zzj.config;

import com.zjjcnt.common.security.component.CustomUserDetailsService;
import com.zjjcnt.common.security.config.BaseAuthorizationServerConfig;
import com.zjjcnt.common.security.util.AuthConstants;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.sysadmin.oauth2.password.CkOAuth2PasswordAuthenticationConverter;
import com.zjjcnt.project.ck.sysadmin.oauth2.password.CkOAuth2PasswordAuthenticationProvider;
import com.zjjcnt.project.ck.sysadmin.oauth2.zzry.FwtZzRyAuthenticationConverter;
import com.zjjcnt.project.ck.sysadmin.oauth2.zzry.FwtZzRyAuthenticationProvider;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.core.OAuth2Token;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.jose.jws.SignatureAlgorithm;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.client.InMemoryRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.OAuth2TokenFormat;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenGenerator;
import org.springframework.security.web.authentication.AuthenticationConverter;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-22 10:12:00
 */
@Configuration
@EnableWebSecurity
public class AuthorizationServerConfig extends BaseAuthorizationServerConfig {

    @Resource(name = AuthConstants.BEAN_NAME_USER_DETAILS_SERVICE)
    private CustomUserDetailsService userDetailsService;

    @Override
    public RegisteredClientRepository registeredClientRepository() {
        RegisteredClient zzjRegisteredClient = RegisteredClient.withId("8ed34699bad843f6b0ca9546f4078191")
                .clientId("8ed34699bad843f6b0ca9546f4078191")
                .clientSecret("d6a5148d7f0f44eabdc6824eb5219fde")
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.PASSWORD)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                .authorizationGrantType(new AuthorizationGrantType(SysadminConstants.AUTHORIZATION_GRANT_TYPE_ZZRY))
                // 回调地址
                .redirectUri("")
                // scope自定义的客户端范围
                .scope(OidcScopes.OPENID)
                // client请求访问时需要授权同意
                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(true)
                        .setting(AuthConstants.CLIENT_SETTING_ENCRYPT_PASSWORD_PUBLIC_KEY,
                                "045e5a3797a7450abb73bd4963f61b99ba2eabdc74185aaad725f43faf42321eeb60db95dcefc479d0d0f902a875648570268bebcdfa24f2ac2dd6ef4fccb51d23")
                        .setting(AuthConstants.CLIENT_SETTING_ENCRYPT_PASSWORD_PRIVATE_KEY,
                                "d6b4c036d00e7267975f6aa613a33b9ba4a824bb033a1a77e9294fe78c6eef17")
                        .build())
                // token配置项信息
                .tokenSettings(TokenSettings.builder()
                        // token有效期30分钟
                        .accessTokenTimeToLive(Duration.ofHours(24L))
                        // 使用默认JWT相关格式
                        .accessTokenFormat(OAuth2TokenFormat.SELF_CONTAINED)
                        // 开启刷新token
                        .reuseRefreshTokens(true)
                        // refreshToken有效期120分钟
                        .refreshTokenTimeToLive(Duration.ofHours(144L))
                        .idTokenSignatureAlgorithm(SignatureAlgorithm.RS256).build()
                )
                .build();


        RegisteredClient webRegisteredClient = RegisteredClient.withId("bldz3lu28kzy4stqsi1kwr0rbojma9e6")
                .clientId("bldz3lu28kzy4stqsi1kwr0rbojma9e6")
                .clientSecret("f2yctyrsws3jkuwxn3rekp2oo8h1g8pg")
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.PASSWORD)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                // 回调地址
                .redirectUri("")
                // scope自定义的客户端范围
                .scope(OidcScopes.OPENID)
                // client请求访问时需要授权同意
                .clientSettings(ClientSettings.builder().requireAuthorizationConsent(true)
                        .build())
                // token配置项信息
                .tokenSettings(TokenSettings.builder()
                        // token有效期60分钟
                        .accessTokenTimeToLive(Duration.ofMinutes(60L))
                        // 使用默认JWT相关格式
                        .accessTokenFormat(OAuth2TokenFormat.SELF_CONTAINED)
                        // 开启刷新token
                        .reuseRefreshTokens(true)
                        // refreshToken有效期240分钟
                        .refreshTokenTimeToLive(Duration.ofMinutes(240L))
                        .idTokenSignatureAlgorithm(SignatureAlgorithm.RS256).build()
                )
                .build();
        return new InMemoryRegisteredClientRepository(zzjRegisteredClient, webRegisteredClient);
    }

    @Override
    public PasswordEncoder passwordEncoder() {
        return NoOpPasswordEncoder.getInstance();
    }

    @Override
    protected List<AuthenticationConverter> getAuthenticationConverterList() {
        List<AuthenticationConverter> authenticationConverterList = new ArrayList<>();
        authenticationConverterList.add(new CkOAuth2PasswordAuthenticationConverter());
        authenticationConverterList.add(new FwtZzRyAuthenticationConverter());
        return authenticationConverterList;
    }

    @Override
    protected List<AuthenticationProvider> getAuthenticationProviderList(HttpSecurity http, AuthenticationManager authenticationManager) {
        List<AuthenticationProvider> authenticationProviderList = new ArrayList<>();

        OAuth2AuthorizationService authorizationService = http.getSharedObject(OAuth2AuthorizationService.class);
        OAuth2TokenGenerator<? extends OAuth2Token> tokenGenerator = http.getSharedObject(OAuth2TokenGenerator.class);

        // 密码认证
        CkOAuth2PasswordAuthenticationProvider passwordAuthenticationProvider =
                new CkOAuth2PasswordAuthenticationProvider(userDetailsService, authenticationManager, authorizationService, tokenGenerator);
        authenticationProviderList.add(passwordAuthenticationProvider);

        FwtZzRyAuthenticationProvider fwtZzRyAuthenticationProvider =
                new FwtZzRyAuthenticationProvider(authenticationManager, authorizationService, tokenGenerator);
        authenticationProviderList.add(fwtZzRyAuthenticationProvider);
        return authenticationProviderList;
    }
}
