package com.zjjcnt.project.ck.zzj.third.rop.resp;

import lombok.Data;

/**
 * 跨省身份证办理返回结果
 *
 * <AUTHOR>
 * @date 2024-10-14 11:33:00
 */
@Data
public class KssfzCommonResp {

    /**
     * 返回代码
     * 1 调用成功
     * 3 接口调用失败
     * 9 其他错误
     */
    private String fhjg;

    /**
     * 返回结果
     */
    private String fhms;

    /**
     *  返回数据 base64编码的json数据
     */
    private String retJson;
}
