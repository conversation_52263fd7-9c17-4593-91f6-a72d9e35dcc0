package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.DwUtils;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.constant.CkConstants;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtLssfzSlxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtLssfzSlxxbDO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtSlxxbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtLssfzSlxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtFyzflsbService;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtLssfzSlxxbService;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtSlxxbService;
import com.zjjcnt.project.ck.zzj.web.request.ZjtLssfzReq;
import com.zjjcnt.project.ck.zzj.web.request.ZjtLssfzShReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 临时身份证受理信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ZjtLssfzSlxxbServiceImpl extends AbstractBaseServiceImpl<ZjtLssfzSlxxbMapper, ZjtLssfzSlxxbDO, ZjtLssfzSlxxbDTO> implements ZjtLssfzSlxxbService {

    ZjtLssfzSlxxbConvert convert = ZjtLssfzSlxxbConvert.INSTANCE;

    private final ZjtSlxxbService zjtSlxxbService;
    private final ZjtFyzflsbService zjtFyzflsbService;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtLssfzSlxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtLssfzSlxxbDO::getRynbid, dto.getRynbid(), StringUtils.isNotEmpty(dto.getRynbid()));
        query.eq(ZjtLssfzSlxxbDO::getZpid, dto.getZpid(), StringUtils.isNotEmpty(dto.getZpid()));
        query.eq(ZjtLssfzSlxxbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtLssfzSlxxbDO::getQfjg, dto.getQfjg(), StringUtils.isNotEmpty(dto.getQfjg()));
        query.ge(ZjtLssfzSlxxbDO::getYxqxqsrq, dto.getYxqxqsrqStart(), StringUtils.isNotEmpty(dto.getYxqxqsrqStart()));
        query.le(ZjtLssfzSlxxbDO::getYxqxqsrq, dto.getYxqxqsrqEnd(), StringUtils.isNotEmpty(dto.getYxqxqsrqEnd()));
        query.ge(ZjtLssfzSlxxbDO::getYxqxjzrq, dto.getYxqxjzrqStart(), StringUtils.isNotEmpty(dto.getYxqxjzrqStart()));
        query.le(ZjtLssfzSlxxbDO::getYxqxjzrq, dto.getYxqxjzrqEnd(), StringUtils.isNotEmpty(dto.getYxqxjzrqEnd()));
        query.eq(ZjtLssfzSlxxbDO::getLsjmsfzkh, dto.getLsjmsfzkh(), StringUtils.isNotEmpty(dto.getLsjmsfzkh()));
        query.eq(ZjtLssfzSlxxbDO::getZz, dto.getZz(), StringUtils.isNotEmpty(dto.getZz()));
        query.eq(ZjtLssfzSlxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtLssfzSlxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtLssfzSlxxbDO::getNbsfzid, dto.getNbsfzid(), StringUtils.isNotEmpty(dto.getNbsfzid()));
        query.eq(ZjtLssfzSlxxbDO::getXb, dto.getXb(), StringUtils.isNotEmpty(dto.getXb()));
        query.eq(ZjtLssfzSlxxbDO::getMz, dto.getMz(), StringUtils.isNotEmpty(dto.getMz()));
        query.ge(ZjtLssfzSlxxbDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(ZjtLssfzSlxxbDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.likeLeft(ZjtLssfzSlxxbDO::getCsdssxq, dto.getCsdssxq(), StringUtils.isNotEmpty(dto.getCsdssxq()));
        query.eq(ZjtLssfzSlxxbDO::getLxdh, ColumnUtils.encryptColumn(dto.getLxdh()), StringUtils.isNotEmpty(dto.getLxdh()));
        query.eq(ZjtLssfzSlxxbDO::getMlpnbid, dto.getMlpnbid(), StringUtils.isNotEmpty(dto.getMlpnbid()));
        query.likeLeft(ZjtLssfzSlxxbDO::getSsxq, dto.getSsxq(), StringUtils.isNotEmpty(dto.getSsxq()));
        query.eq(ZjtLssfzSlxxbDO::getJlx, dto.getJlx(), StringUtils.isNotEmpty(dto.getJlx()));
        query.eq(ZjtLssfzSlxxbDO::getMlph, dto.getMlph(), StringUtils.isNotEmpty(dto.getMlph()));
        query.eq(ZjtLssfzSlxxbDO::getMlxz, dto.getMlxz(), StringUtils.isNotEmpty(dto.getMlxz()));
        query.eq(ZjtLssfzSlxxbDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(ZjtLssfzSlxxbDO::getZrq, dto.getZrq(), StringUtils.isNotEmpty(dto.getZrq()));
        query.eq(ZjtLssfzSlxxbDO::getXzjd, dto.getXzjd(), StringUtils.isNotEmpty(dto.getXzjd()));
        query.eq(ZjtLssfzSlxxbDO::getJcwh, dto.getJcwh(), StringUtils.isNotEmpty(dto.getJcwh()));
        query.eq(ZjtLssfzSlxxbDO::getPxh, dto.getPxh(), StringUtils.isNotEmpty(dto.getPxh()));
        query.eq(ZjtLssfzSlxxbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.eq(ZjtLssfzSlxxbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.ge(ZjtLssfzSlxxbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtLssfzSlxxbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtLssfzSlxxbDO::getCzyip, dto.getCzyip(), StringUtils.isNotEmpty(dto.getCzyip()));
        query.eq(ZjtLssfzSlxxbDO::getDybz, dto.getDybz(), StringUtils.isNotEmpty(dto.getDybz()));
        query.eq(ZjtLssfzSlxxbDO::getDyrid, dto.getDyrid(), StringUtils.isNotEmpty(dto.getDyrid()));
        query.eq(ZjtLssfzSlxxbDO::getDyrxm, ColumnUtils.encryptColumn(dto.getDyrxm()), StringUtils.isNotEmpty(dto.getDyrxm()));
        query.ge(ZjtLssfzSlxxbDO::getDysj, dto.getDysjStart(), StringUtils.isNotEmpty(dto.getDysjStart()));
        query.le(ZjtLssfzSlxxbDO::getDysj, dto.getDysjEnd(), StringUtils.isNotEmpty(dto.getDysjEnd()));
        query.eq(ZjtLssfzSlxxbDO::getDyrip, dto.getDyrip(), StringUtils.isNotEmpty(dto.getDyrip()));
        query.eq(ZjtLssfzSlxxbDO::getShjg, dto.getShjg(), StringUtils.isNotEmpty(dto.getShjg()));
        query.eq(ZjtLssfzSlxxbDO::getShrid, dto.getShrid(), StringUtils.isNotEmpty(dto.getShrid()));
        query.eq(ZjtLssfzSlxxbDO::getShrip, dto.getShrip(), StringUtils.isNotEmpty(dto.getShrip()));
        query.eq(ZjtLssfzSlxxbDO::getShrxm, ColumnUtils.encryptColumn(dto.getShrxm()), StringUtils.isNotEmpty(dto.getShrxm()));
        query.ge(ZjtLssfzSlxxbDO::getShsj, dto.getShsjStart(), StringUtils.isNotEmpty(dto.getShsjStart()));
        query.le(ZjtLssfzSlxxbDO::getShsj, dto.getShsjEnd(), StringUtils.isNotEmpty(dto.getShsjEnd()));
        query.eq(ZjtLssfzSlxxbDO::getSldsjgsdwdmgab, dto.getSldsjgsdwdmgab(), StringUtils.isNotEmpty(dto.getSldsjgsdwdmgab()));
        query.eq(ZjtLssfzSlxxbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtLssfzSlxxbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtLssfzSlxxbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtLssfzSlxxbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(ZjtLssfzSlxxbDO::getZzlx, dto.getZzlx(), StringUtils.isNotEmpty(dto.getZzlx()));
        query.eq(ZjtLssfzSlxxbDO::getNbslid, dto.getNbslid(), StringUtils.isNotEmpty(dto.getNbslid()));
        query.eq(ZjtLssfzSlxxbDO::getJmsfzslh, dto.getJmsfzslh(), StringUtils.isNotEmpty(dto.getJmsfzslh()));
        query.eq(ZjtLssfzSlxxbDO::getSldfjsjgsdwdm, dto.getSldfjsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwdm()));
        query.eq(ZjtLssfzSlxxbDO::getSldfjsjgsdwmc, dto.getSldfjsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwmc()));
        query.eq(ZjtLssfzSlxxbDO::getHlwsqid, dto.getHlwsqid(), StringUtils.isNotEmpty(dto.getHlwsqid()));
        query.eq(ZjtLssfzSlxxbDO::getUsername, dto.getUsername(), StringUtils.isNotEmpty(dto.getUsername()));
        query.eq(ZjtLssfzSlxxbDO::getPjjg, dto.getPjjg(), StringUtils.isNotEmpty(dto.getPjjg()));
        query.eq(ZjtLssfzSlxxbDO::getPjpljc, dto.getPjpljc(), StringUtils.isNotEmpty(dto.getPjpljc()));
        query.ge(ZjtLssfzSlxxbDO::getPjsj, dto.getPjsjStart(), StringUtils.isNotEmpty(dto.getPjsjStart()));
        query.le(ZjtLssfzSlxxbDO::getPjsj, dto.getPjsjEnd(), StringUtils.isNotEmpty(dto.getPjsjEnd()));
        query.eq(ZjtLssfzSlxxbDO::getLqfs, dto.getLqfs(), StringUtils.isNotEmpty(dto.getLqfs()));
        query.eq(ZjtLssfzSlxxbDO::getSjrxm, ColumnUtils.encryptColumn(dto.getSjrxm()), StringUtils.isNotEmpty(dto.getSjrxm()));
        query.eq(ZjtLssfzSlxxbDO::getSjrlxdh, ColumnUtils.encryptColumn(dto.getSjrlxdh()), StringUtils.isNotEmpty(dto.getSjrlxdh()));
        query.eq(ZjtLssfzSlxxbDO::getSjryb, dto.getSjryb(), StringUtils.isNotEmpty(dto.getSjryb()));
        query.likeLeft(ZjtLssfzSlxxbDO::getSjrssxq, dto.getSjrssxq(), StringUtils.isNotEmpty(dto.getSjrssxq()));
        query.eq(ZjtLssfzSlxxbDO::getSjrxz, dto.getSjrxz(), StringUtils.isNotEmpty(dto.getSjrxz()));
        query.eq(ZjtLssfzSlxxbDO::getSjrtxdz, dto.getSjrtxdz(), StringUtils.isNotEmpty(dto.getSjrtxdz()));
        query.eq(ZjtLssfzSlxxbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(ZjtLssfzSlxxbDO::getFjshhs, dto.getFjshhs(), Objects.nonNull(dto.getFjshhs()));
        query.eq(ZjtLssfzSlxxbDO::getCjfs, dto.getCjfs(), StringUtils.isNotEmpty(dto.getCjfs()));
        query.eq(ZjtLssfzSlxxbDO::getFxjsfzt, dto.getFxjsfzt(), StringUtils.isNotEmpty(dto.getFxjsfzt()));
        query.ge(ZjtLssfzSlxxbDO::getFxjsfsj, dto.getFxjsfsjStart(), StringUtils.isNotEmpty(dto.getFxjsfsjStart()));
        query.le(ZjtLssfzSlxxbDO::getFxjsfsj, dto.getFxjsfsjEnd(), StringUtils.isNotEmpty(dto.getFxjsfsjEnd()));
        query.eq(ZjtLssfzSlxxbDO::getFwdx, dto.getFwdx(), StringUtils.isNotEmpty(dto.getFwdx()));
        return query;
    }

    @Override
    public ZjtLssfzSlxxbDTO convertToDTO(ZjtLssfzSlxxbDO zjtLssfzSlxxbDO) {
        return convert.convert(zjtLssfzSlxxbDO);
    }

    @Override
    public ZjtLssfzSlxxbDO convertToDO(ZjtLssfzSlxxbDTO zjtLssfzSlxxbDTO) {
        return convert.convertToDO(zjtLssfzSlxxbDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ZjtLssfzSlxxbDTO lssfzSl(ZjtLssfzReq req, String sldsjgsdwdm, String sldsjgsdwmc, String sldfjsjgsdwdm,
                                    String sldfjsjgsdwmc) {

        Validate.notEmpty(req.getGmsfhm(), "临时身份证受理信息GMSFHM不能为空！");
        //Validate.notEmpty(voLssfzslxx.getQfjg(),"临时身份证受理信息QFJG不能为空！");

        //只有存在未领证的二代证受理信息才能申领临时身份证
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ZjtSlxxbDO::getGmsfhm, req.getGmsfhm())
                .lt(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YLZ)
                .notIn(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF,//作废
                        ZjConstant.ZJ_BLBZ_2ID_CH,//重号
                        ZjConstant.ZJ_BLBZ_2ID_QXSHBTG,//区县审核不过
                        ZjConstant.ZJ_BLBZ_2ID_DSSHBTG,//地市审核不过
                        ZjConstant.ZJ_BLBZ_2ID_STSHBTG)
                .orderBy(ZjtSlxxbDO::getCzsj, false)
                .limit(1);


        /**
         * 当该身份证进入打包程序，但是未完成打包，这时如果进行临时身份证的申请，更改zjt_slxxb. Lssfzslbz ,但是顺便就把slzt也一并修改了
         * 通过List<ZjtSlxxb>获取后，findOneAndWriteLock将不会再次查询数据库。导致zjtSlxxb未缓存数据费最新的。
         * 可以通过entityManager.refresh刷新，这种方式与mysql的数据库事务隔离级别有关，当为repeatable-read时，也无效（非其他线程提交的最新数据）
         * 稳妥起见，sql查询代替findAll，即保证findOneAndWriteLock拿到的是最新的而非findall得到的List的缓存数据
         * */
        ZjtSlxxbDTO zjtSlxxb = zjtSlxxbService.find(queryWrapper);
        if (Objects.isNull(zjtSlxxb)) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "没有找到证件受理信息,未受理证件不允许办理临时身份证");
        }

        //二代证受理表设置临时身份证受理标志
        // 防止其他字段也被更改，改为调用sql by xujh 20240329
        zjtSlxxbService.updateLssfzslbzById(zjtSlxxb.getNbslid(), Constants.YES);

        ZjtLssfzSlxxbDTO zjtLssfzSlxxb = ZjtLssfzSlxxbConvert.INSTANCE.convertToDTO(zjtSlxxb);
        ZjtLssfzSlxxbConvert.INSTANCE.convertToDTO(req, zjtLssfzSlxxb);

        //zlh add 20191115 如果前台没有把临时证的制证类型传过来，则使用二代证的制证类型
        if (zjtLssfzSlxxb.getZzlx() == null) {
            zjtLssfzSlxxb.setZzlx(zjtSlxxb.getZzlx());
        }

        //设置受理地相关数据归属单位代码
        zjtLssfzSlxxb.setSldsjgsdwdm(StringUtils.isEmpty(sldsjgsdwdm) ? sldsjgsdwdm : DwUtils.expandSjgsdw(sldsjgsdwdm));
        zjtLssfzSlxxb.setSldsjgsdwmc(sldsjgsdwmc);
        zjtLssfzSlxxb.setSldfjsjgsdwdm(StringUtils.isEmpty(sldfjsjgsdwdm) ? sldfjsjgsdwdm : DwUtils.expandSjgsdw(sldfjsjgsdwdm));
        zjtLssfzSlxxb.setSldfjsjgsdwmc(sldfjsjgsdwmc);

        Validate.notEmpty(zjtLssfzSlxxb.getRyid(), "临时身份证受理信息RYID不能为空！");
        Validate.notEmpty(zjtLssfzSlxxb.getGmsfhm(), "临时身份证受理信息GMSFHM不能为空！");
        Validate.notEmpty(zjtLssfzSlxxb.getXm(), "临时身份证受理信息XM不能为空！");
        Validate.notEmpty(zjtLssfzSlxxb.getHjdsjgsdwdm(), "临时身份证受理信息HJDSJGSDWDM不能为空！");
        Validate.notEmpty(zjtLssfzSlxxb.getSldsjgsdwdm(), "临时身份证受理信息SLDSJGSDWDM不能为空！");
        Validate.notEmpty(zjtLssfzSlxxb.getCsrq(), "临时身份证受理信息CSRQ不能为空！");
        Validate.notEmpty(zjtLssfzSlxxb.getXb(), "临时身份证受理信息XB不能为空！");
        //Validate.notEmpty(zjtLssfzSlxxb.getQfjg(),"临时身份证受理信息QFJG不能为空!");
        Validate.notEmpty(zjtLssfzSlxxb.getZzlx(), "临时身份证受理信息ZZLX不能为空!");
        Validate.notEmpty(zjtLssfzSlxxb.getSldsjgsdwdm(), "受理地数据归属单位名称SLDSJGSDWDM不能为空!");
        Validate.notEmpty(zjtLssfzSlxxb.getSldsjgsdwmc(), "受理地数据归属单位代码SLDSJGSDWMC不能为空!");


        //省库不做校验身份证号是否有效：由于可能存在刚迁入的户籍人员，省库还未有该人员信息
//        QVCzrkjbxx qvCzrkjbxx = QVCzrkjbxx.vCzrkjbxx;
//        BooleanExpression czrkExpression = qvCzrkjbxx.gmsfhm.eq(zjtLssfzSlxxb.getGmsfhm()).and(qvCzrkjbxx.ryzt.isNull());
//        List<VCzrkjbxx> czrkjbxxList = (List<VCzrkjbxx>) vCzrkjbxxService.findAll(czrkExpression);
//        if (czrkjbxxList.size() > 1) {
//            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "省库办证人重号[[" + zjtLssfzSlxxb.getGmsfhm() + "]],临时身份证无法受理.");
//        }
//        //判断是否存在该身份证号码
//        else if(czrkjbxxList.size() == 0){
//            throw new ServiceException(CkZzjErrorCode.ERR_COMMON,  "办证人在省库未找到信息[[" + zjtLssfzSlxxb.getGmsfhm() +  "]],临时身份证无法受理.");
//        }

        //判断此人是否有受理信息， 每人只允许有一条未打印的受理信息
        ZjtLssfzSlxxbDTO query = new ZjtLssfzSlxxbDTO();
        query.setGmsfhm(zjtLssfzSlxxb.getGmsfhm());
        query.setDybz(ZjConstant.LSSFZ_DYBZ_WDY);
        long count = count(query);
        if (count > 0) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON,
                    "办证人受理过一笔未打印,临时身份证不能同时受理多笔.");
        }

        //系统时间
        String nowTime = ServerTimeUtils.getCurrentTime();
//        String nowDay = nowTime.substring(0, 8);
        //设置默认数据
        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        zjtLssfzSlxxb.setLsjmsfzkh(CkConstants.NULL);
        zjtLssfzSlxxb.setYxqxjzrq(CkConstants.NULL);
        zjtLssfzSlxxb.setYxqxqsrq(CkConstants.NULL);
        zjtLssfzSlxxb.setCzyid(String.valueOf(currentUser.getUserId()));
        zjtLssfzSlxxb.setCzyxm(currentUser.getName());
        zjtLssfzSlxxb.setCzsj(nowTime);
        zjtLssfzSlxxb.setShjg(ZjConstant.LSSFZ_SHJG_WSH);//未审核
        zjtLssfzSlxxb.setCzyip(currentUser.getRemoteAddress());
        zjtLssfzSlxxb.setDybz(ZjConstant.LSSFZ_DYBZ_WDY);//未打印
//        zjtLssfzSlxxb.setLsslid( (Long) lssfz_slxxbDAO.getId());//entity配置StringIdGenerator
        this.insert(zjtLssfzSlxxb);

        //在线支付：费用支付流水
//        ZjtLssfzSlxxb zjtLssfzSlxxb1 = this.findOne(zjtLssfzSlxxb.getLsslid());
        ZjtSlxxbDTO zjtSlxxbFind = zjtSlxxbService.findById(zjtLssfzSlxxb.getNbslid());
        zjtFyzflsbService.addZjtLssfzSlxxbZjtFyzflsb(zjtLssfzSlxxb, zjtSlxxbFind);

        //更新互联网业务状态
//        if (req.getHlwsqid() != null){
//            CkhlwHjsqJb ckhlwHjsqJb = ckhlwHjsqJbService.findByProjectid(req.getHlwsqid());
//            if (ckhlwHjsqJb != null){
//                VoCkhlwGaythSh vo = new VoCkhlwGaythSh();
//                vo.setHlwsqid(voLssfzslxx.getHlwsqid());
//                vo.setSpywslh(zjtLssfzSlxxb.getLsslid());
//                ckhlwGaythShService.sl(vo);//互联网业务受理信息
//            }
//        }

        return zjtLssfzSlxxb;
    }

    @Override
    public ZjtLssfzSlxxbDTO lssfzSh(ZjtLssfzShReq req) {
        ZjtLssfzSlxxbDTO zjtLssfzSlxxbFind = findById(req.getLsslid());
        if (zjtLssfzSlxxbFind == null) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON,
                    "找不到临时身份证信息，审核无法完成。");
        }
        if (ZjConstant.LSSFZ_SHJG_SHTG.equals(req.getShjg())) {
            /*审核通过:校验是否已区县审核通过*/
            //只有存在未领证的二代证受理信息才能申领临时身份证
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq(ZjtSlxxbDO::getGmsfhm, zjtLssfzSlxxbFind.getGmsfhm())
                    .ge(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_QXSHTG)
                    .lt(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YLZ)
                    .notIn(ZjtSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF,//作废
                            ZjConstant.ZJ_BLBZ_2ID_CH,//重号
                            ZjConstant.ZJ_BLBZ_2ID_QXSHBTG,//区县审核不过
                            ZjConstant.ZJ_BLBZ_2ID_DSSHBTG,//地市审核不过
                            ZjConstant.ZJ_BLBZ_2ID_STSHBTG);

            long count = count(queryWrapper);
            if (count == 0) {
                throw new ServiceException(CkZzjErrorCode.ERR_COMMON,
                        "没有找到区县已审核的证件受理信息,未区县审核证件不允许签发临时身份证.");
            }
        }

        String nowTime = ServerTimeUtils.getCurrentTime();

        if (req.getFjshhs() != null) {
            zjtLssfzSlxxbFind.setFjshhs(req.getFjshhs());
        }
        zjtLssfzSlxxbFind.setShjg(req.getShjg());
        zjtLssfzSlxxbFind.setShsj(nowTime);

        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        zjtLssfzSlxxbFind.setShrid(String.valueOf(currentUser.getUserId()));
        zjtLssfzSlxxbFind.setShrxm(currentUser.getName());
        zjtLssfzSlxxbFind.setShrip(currentUser.getRemoteAddress());
        this.update(zjtLssfzSlxxbFind);
        return zjtLssfzSlxxbFind;
    }
}
