package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtLxSlxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLxSlxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtLxSlxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtLxSlxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtLxSlxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:39:12
 */
@Service
public class ZjtLxSlxxbServiceImpl extends AbstractBaseServiceImpl<ZjtLxSlxxbMapper, ZjtLxSlxxbDO, ZjtLxSlxxbDTO> implements ZjtLxSlxxbService {

    ZjtLxSlxxbConvert convert = ZjtLxSlxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtLxSlxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtLxSlxxbDO::getYwslh, dto.getYwslh(), StringUtils.isNotEmpty(dto.getYwslh()));
        query.eq(ZjtLxSlxxbDO::getNbsfzid, dto.getNbsfzid(), StringUtils.isNotEmpty(dto.getNbsfzid()));
        query.eq(ZjtLxSlxxbDO::getZpid, dto.getZpid(), StringUtils.isNotEmpty(dto.getZpid()));
        query.eq(ZjtLxSlxxbDO::getSlh, dto.getSlh(), StringUtils.isNotEmpty(dto.getSlh()));
        query.eq(ZjtLxSlxxbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtLxSlxxbDO::getRynbid, dto.getRynbid(), StringUtils.isNotEmpty(dto.getRynbid()));
        query.eq(ZjtLxSlxxbDO::getQfjg, dto.getQfjg(), StringUtils.isNotEmpty(dto.getQfjg()));
        query.ge(ZjtLxSlxxbDO::getYxqxqsrq, dto.getYxqxqsrqStart(), StringUtils.isNotEmpty(dto.getYxqxqsrqStart()));
        query.le(ZjtLxSlxxbDO::getYxqxqsrq, dto.getYxqxqsrqEnd(), StringUtils.isNotEmpty(dto.getYxqxqsrqEnd()));
        query.ge(ZjtLxSlxxbDO::getYxqxjzrq, dto.getYxqxjzrqStart(), StringUtils.isNotEmpty(dto.getYxqxjzrqStart()));
        query.le(ZjtLxSlxxbDO::getYxqxjzrq, dto.getYxqxjzrqEnd(), StringUtils.isNotEmpty(dto.getYxqxjzrqEnd()));
        query.eq(ZjtLxSlxxbDO::getZz, dto.getZz(), StringUtils.isNotEmpty(dto.getZz()));
        query.eq(ZjtLxSlxxbDO::getSlyy, dto.getSlyy(), StringUtils.isNotEmpty(dto.getSlyy()));
        query.eq(ZjtLxSlxxbDO::getZzlx, dto.getZzlx(), StringUtils.isNotEmpty(dto.getZzlx()));
        query.eq(ZjtLxSlxxbDO::getLqfs, dto.getLqfs(), StringUtils.isNotEmpty(dto.getLqfs()));
        query.eq(ZjtLxSlxxbDO::getSflx, dto.getSflx(), StringUtils.isNotEmpty(dto.getSflx()));
        query.eq(ZjtLxSlxxbDO::getSfje, dto.getSfje(), Objects.nonNull(dto.getSfje()));
        query.eq(ZjtLxSlxxbDO::getSjblsh, dto.getSjblsh(), StringUtils.isNotEmpty(dto.getSjblsh()));
        query.eq(ZjtLxSlxxbDO::getSlzt, dto.getSlzt(), StringUtils.isNotEmpty(dto.getSlzt()));
        query.eq(ZjtLxSlxxbDO::getTbbz, dto.getTbbz(), Objects.nonNull(dto.getTbbz()));
        query.eq(ZjtLxSlxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtLxSlxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtLxSlxxbDO::getXb, dto.getXb(), StringUtils.isNotEmpty(dto.getXb()));
        query.eq(ZjtLxSlxxbDO::getMz, dto.getMz(), StringUtils.isNotEmpty(dto.getMz()));
        query.ge(ZjtLxSlxxbDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(ZjtLxSlxxbDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.likeLeft(ZjtLxSlxxbDO::getCsdssxq, dto.getCsdssxq(), StringUtils.isNotEmpty(dto.getCsdssxq()));
        query.eq(ZjtLxSlxxbDO::getMlpnbid, dto.getMlpnbid(), StringUtils.isNotEmpty(dto.getMlpnbid()));
        query.likeLeft(ZjtLxSlxxbDO::getSsxq, dto.getSsxq(), StringUtils.isNotEmpty(dto.getSsxq()));
        query.eq(ZjtLxSlxxbDO::getJlx, dto.getJlx(), StringUtils.isNotEmpty(dto.getJlx()));
        query.eq(ZjtLxSlxxbDO::getMlph, dto.getMlph(), StringUtils.isNotEmpty(dto.getMlph()));
        query.eq(ZjtLxSlxxbDO::getMlxz, dto.getMlxz(), StringUtils.isNotEmpty(dto.getMlxz()));
        query.eq(ZjtLxSlxxbDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(ZjtLxSlxxbDO::getZrq, dto.getZrq(), StringUtils.isNotEmpty(dto.getZrq()));
        query.eq(ZjtLxSlxxbDO::getXzjd, dto.getXzjd(), StringUtils.isNotEmpty(dto.getXzjd()));
        query.eq(ZjtLxSlxxbDO::getJcwh, dto.getJcwh(), StringUtils.isNotEmpty(dto.getJcwh()));
        query.eq(ZjtLxSlxxbDO::getPxh, dto.getPxh(), StringUtils.isNotEmpty(dto.getPxh()));
        query.eq(ZjtLxSlxxbDO::getYwbz, dto.getYwbz(), StringUtils.isNotEmpty(dto.getYwbz()));
        query.eq(ZjtLxSlxxbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.ge(ZjtLxSlxxbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtLxSlxxbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtLxSlxxbDO::getDwdm, dto.getDwdm(), StringUtils.isNotEmpty(dto.getDwdm()));
        query.eq(ZjtLxSlxxbDO::getSjrxm, ColumnUtils.encryptColumn(dto.getSjrxm()), StringUtils.isNotEmpty(dto.getSjrxm()));
        query.eq(ZjtLxSlxxbDO::getSjrlxdh, ColumnUtils.encryptColumn(dto.getSjrlxdh()), StringUtils.isNotEmpty(dto.getSjrlxdh()));
        query.eq(ZjtLxSlxxbDO::getSjryb, dto.getSjryb(), StringUtils.isNotEmpty(dto.getSjryb()));
        query.likeLeft(ZjtLxSlxxbDO::getSjrssxq, dto.getSjrssxq(), StringUtils.isNotEmpty(dto.getSjrssxq()));
        query.eq(ZjtLxSlxxbDO::getSjrxz, dto.getSjrxz(), StringUtils.isNotEmpty(dto.getSjrxz()));
        query.eq(ZjtLxSlxxbDO::getSjrtxdz, dto.getSjrtxdz(), StringUtils.isNotEmpty(dto.getSjrtxdz()));
        query.eq(ZjtLxSlxxbDO::getZzxxcwlb, dto.getZzxxcwlb(), StringUtils.isNotEmpty(dto.getZzxxcwlb()));
        query.eq(ZjtLxSlxxbDO::getCwms, dto.getCwms(), StringUtils.isNotEmpty(dto.getCwms()));
        query.eq(ZjtLxSlxxbDO::getJydw, dto.getJydw(), StringUtils.isNotEmpty(dto.getJydw()));
        query.eq(ZjtLxSlxxbDO::getJyrxm, ColumnUtils.encryptColumn(dto.getJyrxm()), StringUtils.isNotEmpty(dto.getJyrxm()));
        query.ge(ZjtLxSlxxbDO::getJyrq, dto.getJyrqStart(), StringUtils.isNotEmpty(dto.getJyrqStart()));
        query.le(ZjtLxSlxxbDO::getJyrq, dto.getJyrqEnd(), StringUtils.isNotEmpty(dto.getJyrqEnd()));
        query.eq(ZjtLxSlxxbDO::getCldw, dto.getCldw(), StringUtils.isNotEmpty(dto.getCldw()));
        query.eq(ZjtLxSlxxbDO::getClqk, dto.getClqk(), StringUtils.isNotEmpty(dto.getClqk()));
        query.ge(ZjtLxSlxxbDO::getClrq, dto.getClrqStart(), StringUtils.isNotEmpty(dto.getClrqStart()));
        query.le(ZjtLxSlxxbDO::getClrq, dto.getClrqEnd(), StringUtils.isNotEmpty(dto.getClrqEnd()));
        query.eq(ZjtLxSlxxbDO::getZlhkzt, dto.getZlhkzt(), StringUtils.isNotEmpty(dto.getZlhkzt()));
        query.ge(ZjtLxSlxxbDO::getHksj, dto.getHksjStart(), StringUtils.isNotEmpty(dto.getHksjStart()));
        query.le(ZjtLxSlxxbDO::getHksj, dto.getHksjEnd(), StringUtils.isNotEmpty(dto.getHksjEnd()));
        query.eq(ZjtLxSlxxbDO::getBwbha, dto.getBwbha(), StringUtils.isNotEmpty(dto.getBwbha()));
        query.eq(ZjtLxSlxxbDO::getBwbhb, dto.getBwbhb(), StringUtils.isNotEmpty(dto.getBwbhb()));
        query.ge(ZjtLxSlxxbDO::getShrq, dto.getShrqStart(), StringUtils.isNotEmpty(dto.getShrqStart()));
        query.le(ZjtLxSlxxbDO::getShrq, dto.getShrqEnd(), StringUtils.isNotEmpty(dto.getShrqEnd()));
        query.ge(ZjtLxSlxxbDO::getStjssj, dto.getStjssjStart(), StringUtils.isNotEmpty(dto.getStjssjStart()));
        query.le(ZjtLxSlxxbDO::getStjssj, dto.getStjssjEnd(), StringUtils.isNotEmpty(dto.getStjssjEnd()));
        query.eq(ZjtLxSlxxbDO::getBwbhc, dto.getBwbhc(), StringUtils.isNotEmpty(dto.getBwbhc()));
        query.eq(ZjtLxSlxxbDO::getFjpch, dto.getFjpch(), StringUtils.isNotEmpty(dto.getFjpch()));
        query.eq(ZjtLxSlxxbDO::getRlbdid, dto.getRlbdid(), StringUtils.isNotEmpty(dto.getRlbdid()));
        query.eq(ZjtLxSlxxbDO::getRlbdbz, dto.getRlbdbz(), StringUtils.isNotEmpty(dto.getRlbdbz()));
        query.ge(ZjtLxSlxxbDO::getRlbdsj, dto.getRlbdsjStart(), StringUtils.isNotEmpty(dto.getRlbdsjStart()));
        query.le(ZjtLxSlxxbDO::getRlbdsj, dto.getRlbdsjEnd(), StringUtils.isNotEmpty(dto.getRlbdsjEnd()));
        query.eq(ZjtLxSlxxbDO::getZwyzw, dto.getZwyzw(), StringUtils.isNotEmpty(dto.getZwyzw()));
        query.eq(ZjtLxSlxxbDO::getZwyzcjg, dto.getZwyzcjg(), StringUtils.isNotEmpty(dto.getZwyzcjg()));
        query.eq(ZjtLxSlxxbDO::getZwezw, dto.getZwezw(), StringUtils.isNotEmpty(dto.getZwezw()));
        query.eq(ZjtLxSlxxbDO::getZwezcjg, dto.getZwezcjg(), StringUtils.isNotEmpty(dto.getZwezcjg()));
        query.eq(ZjtLxSlxxbDO::getZwcjjgdm, dto.getZwcjjgdm(), StringUtils.isNotEmpty(dto.getZwcjjgdm()));
        query.eq(ZjtLxSlxxbDO::getSzyczkdm, dto.getSzyczkdm(), StringUtils.isNotEmpty(dto.getSzyczkdm()));
        query.eq(ZjtLxSlxxbDO::getSfzwzj, dto.getSfzwzj(), StringUtils.isNotEmpty(dto.getSfzwzj()));
        query.eq(ZjtLxSlxxbDO::getDztbbz, dto.getDztbbz(), Objects.nonNull(dto.getDztbbz()));
        query.ge(ZjtLxSlxxbDO::getDztbsj, dto.getDztbsjStart(), StringUtils.isNotEmpty(dto.getDztbsjStart()));
        query.le(ZjtLxSlxxbDO::getDztbsj, dto.getDztbsjEnd(), StringUtils.isNotEmpty(dto.getDztbsjEnd()));
        query.eq(ZjtLxSlxxbDO::getDzsjbbh, dto.getDzsjbbh(), StringUtils.isNotEmpty(dto.getDzsjbbh()));
        query.eq(ZjtLxSlxxbDO::getSlfs, dto.getSlfs(), StringUtils.isNotEmpty(dto.getSlfs()));
        query.eq(ZjtLxSlxxbDO::getZwtxid, dto.getZwtxid(), StringUtils.isNotEmpty(dto.getZwtxid()));
        query.eq(ZjtLxSlxxbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(ZjtLxSlxxbDO::getHjddzbm, dto.getHjddzbm(), StringUtils.isNotEmpty(dto.getHjddzbm()));
        query.eq(ZjtLxSlxxbDO::getLssfzslbz, dto.getLssfzslbz(), StringUtils.isNotEmpty(dto.getLssfzslbz()));
        query.eq(ZjtLxSlxxbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtLxSlxxbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtLxSlxxbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.likeLeft(ZjtLxSlxxbDO::getZjzdssxq, dto.getZjzdssxq(), StringUtils.isNotEmpty(dto.getZjzdssxq()));
        query.eq(ZjtLxSlxxbDO::getZjzdxz, dto.getZjzdxz(), StringUtils.isNotEmpty(dto.getZjzdxz()));
        query.ge(ZjtLxSlxxbDO::getLqrq, dto.getLqrqStart(), StringUtils.isNotEmpty(dto.getLqrqStart()));
        query.le(ZjtLxSlxxbDO::getLqrq, dto.getLqrqEnd(), StringUtils.isNotEmpty(dto.getLqrqEnd()));
        query.eq(ZjtLxSlxxbDO::getLqrxm, ColumnUtils.encryptColumn(dto.getLqrxm()), StringUtils.isNotEmpty(dto.getLqrxm()));
        query.eq(ZjtLxSlxxbDO::getLqrsfhm, dto.getLqrsfhm(), StringUtils.isNotEmpty(dto.getLqrsfhm()));
        query.eq(ZjtLxSlxxbDO::getLqrzpid, dto.getLqrzpid(), StringUtils.isNotEmpty(dto.getLqrzpid()));
        query.ge(ZjtLxSlxxbDO::getZjddrq, dto.getZjddrqStart(), StringUtils.isNotEmpty(dto.getZjddrqStart()));
        query.le(ZjtLxSlxxbDO::getZjddrq, dto.getZjddrqEnd(), StringUtils.isNotEmpty(dto.getZjddrqEnd()));
        query.eq(ZjtLxSlxxbDO::getLzczrid, dto.getLzczrid(), StringUtils.isNotEmpty(dto.getLzczrid()));
        query.eq(ZjtLxSlxxbDO::getLzczrxm, ColumnUtils.encryptColumn(dto.getLzczrxm()), StringUtils.isNotEmpty(dto.getLzczrxm()));
        query.eq(ZjtLxSlxxbDO::getShrxm, ColumnUtils.encryptColumn(dto.getShrxm()), StringUtils.isNotEmpty(dto.getShrxm()));
        query.eq(ZjtLxSlxxbDO::getShdw, dto.getShdw(), StringUtils.isNotEmpty(dto.getShdw()));
        query.eq(ZjtLxSlxxbDO::getShqk, dto.getShqk(), StringUtils.isNotEmpty(dto.getShqk()));
        query.ge(ZjtLxSlxxbDO::getQfrq, dto.getQfrqStart(), StringUtils.isNotEmpty(dto.getQfrqStart()));
        query.le(ZjtLxSlxxbDO::getQfrq, dto.getQfrqEnd(), StringUtils.isNotEmpty(dto.getQfrqEnd()));
        query.eq(ZjtLxSlxxbDO::getQfrid, dto.getQfrid(), StringUtils.isNotEmpty(dto.getQfrid()));
        query.eq(ZjtLxSlxxbDO::getQfrxm, ColumnUtils.encryptColumn(dto.getQfrxm()), StringUtils.isNotEmpty(dto.getQfrxm()));
        query.eq(ZjtLxSlxxbDO::getQfdwjgdm, dto.getQfdwjgdm(), StringUtils.isNotEmpty(dto.getQfdwjgdm()));
        query.eq(ZjtLxSlxxbDO::getQfdw, dto.getQfdw(), StringUtils.isNotEmpty(dto.getQfdw()));
        query.ge(ZjtLxSlxxbDO::getDsshrq, dto.getDsshrqStart(), StringUtils.isNotEmpty(dto.getDsshrqStart()));
        query.le(ZjtLxSlxxbDO::getDsshrq, dto.getDsshrqEnd(), StringUtils.isNotEmpty(dto.getDsshrqEnd()));
        query.eq(ZjtLxSlxxbDO::getDsshrxm, ColumnUtils.encryptColumn(dto.getDsshrxm()), StringUtils.isNotEmpty(dto.getDsshrxm()));
        query.eq(ZjtLxSlxxbDO::getDsshdw, dto.getDsshdw(), StringUtils.isNotEmpty(dto.getDsshdw()));
        query.eq(ZjtLxSlxxbDO::getDsshqk, dto.getDsshqk(), StringUtils.isNotEmpty(dto.getDsshqk()));
        query.eq(ZjtLxSlxxbDO::getRwid, dto.getRwid(), StringUtils.isNotEmpty(dto.getRwid()));
        query.eq(ZjtLxSlxxbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtLxSlxxbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(ZjtLxSlxxbDO::getSldfjsjgsdwdm, dto.getSldfjsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwdm()));
        query.eq(ZjtLxSlxxbDO::getSldfjsjgsdwmc, dto.getSldfjsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwmc()));
        query.eq(ZjtLxSlxxbDO::getShdwdm, dto.getShdwdm(), StringUtils.isNotEmpty(dto.getShdwdm()));
        query.eq(ZjtLxSlxxbDO::getCldwdm, dto.getCldwdm(), StringUtils.isNotEmpty(dto.getCldwdm()));
        query.eq(ZjtLxSlxxbDO::getDsshdwdm, dto.getDsshdwdm(), StringUtils.isNotEmpty(dto.getDsshdwdm()));
        query.eq(ZjtLxSlxxbDO::getShrid, dto.getShrid(), StringUtils.isNotEmpty(dto.getShrid()));
        query.eq(ZjtLxSlxxbDO::getDsshrid, dto.getDsshrid(), StringUtils.isNotEmpty(dto.getDsshrid()));
        query.eq(ZjtLxSlxxbDO::getSfdjh, dto.getSfdjh(), StringUtils.isNotEmpty(dto.getSfdjh()));
        query.eq(ZjtLxSlxxbDO::getRwzxrzbh, dto.getRwzxrzbh(), StringUtils.isNotEmpty(dto.getRwzxrzbh()));
        query.ge(ZjtLxSlxxbDO::getRwddsj, dto.getRwddsjStart(), StringUtils.isNotEmpty(dto.getRwddsjStart()));
        query.le(ZjtLxSlxxbDO::getRwddsj, dto.getRwddsjEnd(), StringUtils.isNotEmpty(dto.getRwddsjEnd()));
        query.eq(ZjtLxSlxxbDO::getSlyckrxsfbd, dto.getSlyckrxsfbd(), StringUtils.isNotEmpty(dto.getSlyckrxsfbd()));
        query.ge(ZjtLxSlxxbDO::getRxbdkssj, dto.getRxbdkssjStart(), StringUtils.isNotEmpty(dto.getRxbdkssjStart()));
        query.le(ZjtLxSlxxbDO::getRxbdkssj, dto.getRxbdkssjEnd(), StringUtils.isNotEmpty(dto.getRxbdkssjEnd()));
        query.eq(ZjtLxSlxxbDO::getRxbdhs, dto.getRxbdhs(), StringUtils.isNotEmpty(dto.getRxbdhs()));
        query.eq(ZjtLxSlxxbDO::getRxbdxsd, dto.getRxbdxsd(), StringUtils.isNotEmpty(dto.getRxbdxsd()));
        query.eq(ZjtLxSlxxbDO::getRxbdkbh, dto.getRxbdkbh(), StringUtils.isNotEmpty(dto.getRxbdkbh()));
        query.eq(ZjtLxSlxxbDO::getRxbdjg, dto.getRxbdjg(), StringUtils.isNotEmpty(dto.getRxbdjg()));
        query.eq(ZjtLxSlxxbDO::getSlylszwsfbd, dto.getSlylszwsfbd(), StringUtils.isNotEmpty(dto.getSlylszwsfbd()));
        query.eq(ZjtLxSlxxbDO::getZwybdjg, dto.getZwybdjg(), StringUtils.isNotEmpty(dto.getZwybdjg()));
        query.eq(ZjtLxSlxxbDO::getZwybdxsd, dto.getZwybdxsd(), StringUtils.isNotEmpty(dto.getZwybdxsd()));
        query.eq(ZjtLxSlxxbDO::getZwebdjg, dto.getZwebdjg(), StringUtils.isNotEmpty(dto.getZwebdjg()));
        query.eq(ZjtLxSlxxbDO::getZwebdxsd, dto.getZwebdxsd(), StringUtils.isNotEmpty(dto.getZwebdxsd()));
        query.eq(ZjtLxSlxxbDO::getLzszwsfhy, dto.getLzszwsfhy(), StringUtils.isNotEmpty(dto.getLzszwsfhy()));
        query.eq(ZjtLxSlxxbDO::getLzszwyhyjg, dto.getLzszwyhyjg(), StringUtils.isNotEmpty(dto.getLzszwyhyjg()));
        query.eq(ZjtLxSlxxbDO::getLzszwyhyxsd, dto.getLzszwyhyxsd(), StringUtils.isNotEmpty(dto.getLzszwyhyxsd()));
        query.eq(ZjtLxSlxxbDO::getLzszwehyjg, dto.getLzszwehyjg(), StringUtils.isNotEmpty(dto.getLzszwehyjg()));
        query.eq(ZjtLxSlxxbDO::getLzszwehyxsd, dto.getLzszwehyxsd(), StringUtils.isNotEmpty(dto.getLzszwehyxsd()));
        query.eq(ZjtLxSlxxbDO::getLzssfjhjz, dto.getLzssfjhjz(), StringUtils.isNotEmpty(dto.getLzssfjhjz()));
        query.eq(ZjtLxSlxxbDO::getSlylszwbdsm, dto.getSlylszwbdsm(), StringUtils.isNotEmpty(dto.getSlylszwbdsm()));
        query.eq(ZjtLxSlxxbDO::getLzszwbdsm, dto.getLzszwbdsm(), StringUtils.isNotEmpty(dto.getLzszwbdsm()));
        query.eq(ZjtLxSlxxbDO::getSqrgmsfhm, ColumnUtils.encryptColumn(dto.getSqrgmsfhm()), StringUtils.isNotEmpty(dto.getSqrgmsfhm()));
        query.eq(ZjtLxSlxxbDO::getSqrxm, ColumnUtils.encryptColumn(dto.getSqrxm()), StringUtils.isNotEmpty(dto.getSqrxm()));
        query.eq(ZjtLxSlxxbDO::getSqrlxdh, ColumnUtils.encryptColumn(dto.getSqrlxdh()), StringUtils.isNotEmpty(dto.getSqrlxdh()));
        query.ge(ZjtLxSlxxbDO::getJzqsrq, dto.getJzqsrqStart(), StringUtils.isNotEmpty(dto.getJzqsrqStart()));
        query.le(ZjtLxSlxxbDO::getJzqsrq, dto.getJzqsrqEnd(), StringUtils.isNotEmpty(dto.getJzqsrqEnd()));
        query.eq(ZjtLxSlxxbDO::getCzylxdh, ColumnUtils.encryptColumn(dto.getCzylxdh()), StringUtils.isNotEmpty(dto.getCzylxdh()));
        query.eq(ZjtLxSlxxbDO::getShrlxdh, ColumnUtils.encryptColumn(dto.getShrlxdh()), StringUtils.isNotEmpty(dto.getShrlxdh()));
        query.eq(ZjtLxSlxxbDO::getDsshrlxdh, ColumnUtils.encryptColumn(dto.getDsshrlxdh()), StringUtils.isNotEmpty(dto.getDsshrlxdh()));
        query.eq(ZjtLxSlxxbDO::getZfcje, dto.getZfcje(), Objects.nonNull(dto.getZfcje()));
        query.eq(ZjtLxSlxxbDO::getQxfcje, dto.getQxfcje(), Objects.nonNull(dto.getQxfcje()));
        query.eq(ZjtLxSlxxbDO::getDsfcje, dto.getDsfcje(), Objects.nonNull(dto.getDsfcje()));
        query.eq(ZjtLxSlxxbDO::getZxfcje, dto.getZxfcje(), Objects.nonNull(dto.getZxfcje()));
        query.eq(ZjtLxSlxxbDO::getYzkddh, dto.getYzkddh(), StringUtils.isNotEmpty(dto.getYzkddh()));
        query.eq(ZjtLxSlxxbDO::getSpdz1, dto.getSpdz1(), StringUtils.isNotEmpty(dto.getSpdz1()));
        query.eq(ZjtLxSlxxbDO::getSfmsbswzp, dto.getSfmsbswzp(), StringUtils.isNotEmpty(dto.getSfmsbswzp()));
        query.eq(ZjtLxSlxxbDO::getHlwsqid, dto.getHlwsqid(), StringUtils.isNotEmpty(dto.getHlwsqid()));
        query.eq(ZjtLxSlxxbDO::getUsername, dto.getUsername(), StringUtils.isNotEmpty(dto.getUsername()));
        query.eq(ZjtLxSlxxbDO::getPjjg, dto.getPjjg(), StringUtils.isNotEmpty(dto.getPjjg()));
        query.eq(ZjtLxSlxxbDO::getPjpljc, dto.getPjpljc(), StringUtils.isNotEmpty(dto.getPjpljc()));
        query.ge(ZjtLxSlxxbDO::getPjsj, dto.getPjsjStart(), StringUtils.isNotEmpty(dto.getPjsjStart()));
        query.le(ZjtLxSlxxbDO::getPjsj, dto.getPjsjEnd(), StringUtils.isNotEmpty(dto.getPjsjEnd()));
        query.eq(ZjtLxSlxxbDO::getFwdx, dto.getFwdx(), StringUtils.isNotEmpty(dto.getFwdx()));
        query.eq(ZjtLxSlxxbDO::getSfdgszj, dto.getSfdgszj(), StringUtils.isNotEmpty(dto.getSfdgszj()));
        query.eq(ZjtLxSlxxbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(ZjtLxSlxxbDO::getSlshjdz, dto.getSlshjdz(), StringUtils.isNotEmpty(dto.getSlshjdz()));
        query.eq(ZjtLxSlxxbDO::getKsywlsh, dto.getKsywlsh(), StringUtils.isNotEmpty(dto.getKsywlsh()));
        query.eq(ZjtLxSlxxbDO::getKsfsbz, dto.getKsfsbz(), StringUtils.isNotEmpty(dto.getKsfsbz()));
        query.ge(ZjtLxSlxxbDO::getKsfssj, dto.getKsfssjStart(), StringUtils.isNotEmpty(dto.getKsfssjStart()));
        query.le(ZjtLxSlxxbDO::getKsfssj, dto.getKsfssjEnd(), StringUtils.isNotEmpty(dto.getKsfssjEnd()));
        query.eq(ZjtLxSlxxbDO::getZp, dto.getZp(), Objects.nonNull(dto.getZp()));
        query.eq(ZjtLxSlxxbDO::getSqrzp, dto.getSqrzp(), Objects.nonNull(dto.getSqrzp()));
        query.eq(ZjtLxSlxxbDO::getZwxx, dto.getZwxx(), Objects.nonNull(dto.getZwxx()));
        query.eq(ZjtLxSlxxbDO::getQzxx, dto.getQzxx(), Objects.nonNull(dto.getQzxx()));
        query.eq(ZjtLxSlxxbDO::getHkb1, dto.getHkb1(), Objects.nonNull(dto.getHkb1()));
        query.eq(ZjtLxSlxxbDO::getHkb2, dto.getHkb2(), Objects.nonNull(dto.getHkb2()));
        query.eq(ZjtLxSlxxbDO::getHkb3, dto.getHkb3(), Objects.nonNull(dto.getHkb3()));
        query.eq(ZjtLxSlxxbDO::getHkb4, dto.getHkb4(), Objects.nonNull(dto.getHkb4()));
        query.eq(ZjtLxSlxxbDO::getSfz1, dto.getSfz1(), Objects.nonNull(dto.getSfz1()));
        query.eq(ZjtLxSlxxbDO::getSfz2, dto.getSfz2(), Objects.nonNull(dto.getSfz2()));
        query.eq(ZjtLxSlxxbDO::getSfz3, dto.getSfz3(), Objects.nonNull(dto.getSfz3()));
        query.eq(ZjtLxSlxxbDO::getSfz4, dto.getSfz4(), Objects.nonNull(dto.getSfz4()));
        query.eq(ZjtLxSlxxbDO::getClsl, dto.getClsl(), Objects.nonNull(dto.getClsl()));
        return query;
    }

    @Override
    public ZjtLxSlxxbDTO convertToDTO(ZjtLxSlxxbDO zjtLxSlxxbDO) {
        return convert.convert(zjtLxSlxxbDO);
    }

    @Override
    public ZjtLxSlxxbDO convertToDO(ZjtLxSlxxbDTO zjtLxSlxxbDTO) {
        return convert.convertToDO(zjtLxSlxxbDTO);
    }
}
