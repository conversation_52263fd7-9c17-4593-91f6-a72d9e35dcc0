package com.zjjcnt.project.ck.zzj.third.sfzycj.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 身份证预采集信息的token返回信息
 */
@Data
public class SfzycjTokenResp {

    @JsonProperty(value = "access_token")
    private String accessToken;

    @JsonProperty(value = "token_type")
    private String tokenType;

    @JsonProperty(value = "expires_in")
    private String expiresIn;

}
