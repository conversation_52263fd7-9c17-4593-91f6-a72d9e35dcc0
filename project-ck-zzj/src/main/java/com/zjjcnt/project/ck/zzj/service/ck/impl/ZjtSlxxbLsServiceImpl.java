package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtSlxxbLsConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbLsDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtSlxxbLsDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtSlxxbLsMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtSlxxbLsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Service
public class ZjtSlxxbLsServiceImpl extends AbstractBaseServiceImpl<ZjtSlxxbLsMapper, ZjtSlxxbLsDO, ZjtSlxxbLsDTO> implements ZjtSlxxbLsService {

    ZjtSlxxbLsConvert convert = ZjtSlxxbLsConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtSlxxbLsDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtSlxxbLsDO::getYwslh, dto.getYwslh(), StringUtils.isNotEmpty(dto.getYwslh()));
        query.eq(ZjtSlxxbLsDO::getNbsfzid, dto.getNbsfzid(), StringUtils.isNotEmpty(dto.getNbsfzid()));
        query.eq(ZjtSlxxbLsDO::getZpid, dto.getZpid(), StringUtils.isNotEmpty(dto.getZpid()));
        query.eq(ZjtSlxxbLsDO::getSlh, dto.getSlh(), StringUtils.isNotEmpty(dto.getSlh()));
        query.eq(ZjtSlxxbLsDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtSlxxbLsDO::getRynbid, dto.getRynbid(), StringUtils.isNotEmpty(dto.getRynbid()));
        query.eq(ZjtSlxxbLsDO::getQfjg, dto.getQfjg(), StringUtils.isNotEmpty(dto.getQfjg()));
        query.ge(ZjtSlxxbLsDO::getYxqxqsrq, dto.getYxqxqsrqStart(), StringUtils.isNotEmpty(dto.getYxqxqsrqStart()));
        query.le(ZjtSlxxbLsDO::getYxqxqsrq, dto.getYxqxqsrqEnd(), StringUtils.isNotEmpty(dto.getYxqxqsrqEnd()));
        query.ge(ZjtSlxxbLsDO::getYxqxjzrq, dto.getYxqxjzrqStart(), StringUtils.isNotEmpty(dto.getYxqxjzrqStart()));
        query.le(ZjtSlxxbLsDO::getYxqxjzrq, dto.getYxqxjzrqEnd(), StringUtils.isNotEmpty(dto.getYxqxjzrqEnd()));
        query.eq(ZjtSlxxbLsDO::getZz, dto.getZz(), StringUtils.isNotEmpty(dto.getZz()));
        query.eq(ZjtSlxxbLsDO::getHjdzqhnxxdz, dto.getHjdzqhnxxdz(), StringUtils.isNotEmpty(dto.getHjdzqhnxxdz()));
        query.eq(ZjtSlxxbLsDO::getSlyy, dto.getSlyy(), StringUtils.isNotEmpty(dto.getSlyy()));
        query.eq(ZjtSlxxbLsDO::getZzlx, dto.getZzlx(), StringUtils.isNotEmpty(dto.getZzlx()));
        query.eq(ZjtSlxxbLsDO::getLqfs, dto.getLqfs(), StringUtils.isNotEmpty(dto.getLqfs()));
        query.eq(ZjtSlxxbLsDO::getSflx, dto.getSflx(), StringUtils.isNotEmpty(dto.getSflx()));
        query.eq(ZjtSlxxbLsDO::getSfje, dto.getSfje(), Objects.nonNull(dto.getSfje()));
        query.eq(ZjtSlxxbLsDO::getSjblsh, dto.getSjblsh(), StringUtils.isNotEmpty(dto.getSjblsh()));
        query.eq(ZjtSlxxbLsDO::getSlzt, dto.getSlzt(), StringUtils.isNotEmpty(dto.getSlzt()));
        query.eq(ZjtSlxxbLsDO::getTbbz, dto.getTbbz(), Objects.nonNull(dto.getTbbz()));
        query.eq(ZjtSlxxbLsDO::getQyfwdm, dto.getQyfwdm(), StringUtils.isNotEmpty(dto.getQyfwdm()));
        query.eq(ZjtSlxxbLsDO::getSyzbz, dto.getSyzbz(), StringUtils.isNotEmpty(dto.getSyzbz()));
        query.eq(ZjtSlxxbLsDO::getDzzdbz, dto.getDzzdbz(), StringUtils.isNotEmpty(dto.getDzzdbz()));
        query.eq(ZjtSlxxbLsDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtSlxxbLsDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtSlxxbLsDO::getXb, dto.getXb(), StringUtils.isNotEmpty(dto.getXb()));
        query.eq(ZjtSlxxbLsDO::getMz, dto.getMz(), StringUtils.isNotEmpty(dto.getMz()));
        query.eq(ZjtSlxxbLsDO::getMzfjxdm, dto.getMzfjxdm(), StringUtils.isNotEmpty(dto.getMzfjxdm()));
        query.ge(ZjtSlxxbLsDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(ZjtSlxxbLsDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.likeLeft(ZjtSlxxbLsDO::getCsdssxq, dto.getCsdssxq(), StringUtils.isNotEmpty(dto.getCsdssxq()));
        query.eq(ZjtSlxxbLsDO::getMlpnbid, dto.getMlpnbid(), StringUtils.isNotEmpty(dto.getMlpnbid()));
        query.likeLeft(ZjtSlxxbLsDO::getSsxq, dto.getSsxq(), StringUtils.isNotEmpty(dto.getSsxq()));
        query.eq(ZjtSlxxbLsDO::getJlx, dto.getJlx(), StringUtils.isNotEmpty(dto.getJlx()));
        query.eq(ZjtSlxxbLsDO::getMlph, dto.getMlph(), StringUtils.isNotEmpty(dto.getMlph()));
        query.eq(ZjtSlxxbLsDO::getMlxz, dto.getMlxz(), StringUtils.isNotEmpty(dto.getMlxz()));
        query.eq(ZjtSlxxbLsDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(ZjtSlxxbLsDO::getZrq, dto.getZrq(), StringUtils.isNotEmpty(dto.getZrq()));
        query.eq(ZjtSlxxbLsDO::getXzjd, dto.getXzjd(), StringUtils.isNotEmpty(dto.getXzjd()));
        query.eq(ZjtSlxxbLsDO::getJcwh, dto.getJcwh(), StringUtils.isNotEmpty(dto.getJcwh()));
        query.eq(ZjtSlxxbLsDO::getPxh, dto.getPxh(), StringUtils.isNotEmpty(dto.getPxh()));
        query.eq(ZjtSlxxbLsDO::getYwbz, dto.getYwbz(), StringUtils.isNotEmpty(dto.getYwbz()));
        query.eq(ZjtSlxxbLsDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.ge(ZjtSlxxbLsDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtSlxxbLsDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtSlxxbLsDO::getDwdm, dto.getDwdm(), StringUtils.isNotEmpty(dto.getDwdm()));
        query.eq(ZjtSlxxbLsDO::getSjrxm, ColumnUtils.encryptColumn(dto.getSjrxm()), StringUtils.isNotEmpty(dto.getSjrxm()));
        query.eq(ZjtSlxxbLsDO::getSjrlxdh, ColumnUtils.encryptColumn(dto.getSjrlxdh()), StringUtils.isNotEmpty(dto.getSjrlxdh()));
        query.eq(ZjtSlxxbLsDO::getSjryb, dto.getSjryb(), StringUtils.isNotEmpty(dto.getSjryb()));
        query.likeLeft(ZjtSlxxbLsDO::getSjrssxq, dto.getSjrssxq(), StringUtils.isNotEmpty(dto.getSjrssxq()));
        query.eq(ZjtSlxxbLsDO::getSjrxz, dto.getSjrxz(), StringUtils.isNotEmpty(dto.getSjrxz()));
        query.eq(ZjtSlxxbLsDO::getSjrtxdz, dto.getSjrtxdz(), StringUtils.isNotEmpty(dto.getSjrtxdz()));
        query.eq(ZjtSlxxbLsDO::getZzxxcwlb, dto.getZzxxcwlb(), StringUtils.isNotEmpty(dto.getZzxxcwlb()));
        query.eq(ZjtSlxxbLsDO::getCwms, dto.getCwms(), StringUtils.isNotEmpty(dto.getCwms()));
        query.eq(ZjtSlxxbLsDO::getJydw, dto.getJydw(), StringUtils.isNotEmpty(dto.getJydw()));
        query.eq(ZjtSlxxbLsDO::getJyrxm, ColumnUtils.encryptColumn(dto.getJyrxm()), StringUtils.isNotEmpty(dto.getJyrxm()));
        query.ge(ZjtSlxxbLsDO::getJyrq, dto.getJyrqStart(), StringUtils.isNotEmpty(dto.getJyrqStart()));
        query.le(ZjtSlxxbLsDO::getJyrq, dto.getJyrqEnd(), StringUtils.isNotEmpty(dto.getJyrqEnd()));
        query.eq(ZjtSlxxbLsDO::getCldw, dto.getCldw(), StringUtils.isNotEmpty(dto.getCldw()));
        query.eq(ZjtSlxxbLsDO::getClqk, dto.getClqk(), StringUtils.isNotEmpty(dto.getClqk()));
        query.ge(ZjtSlxxbLsDO::getClrq, dto.getClrqStart(), StringUtils.isNotEmpty(dto.getClrqStart()));
        query.le(ZjtSlxxbLsDO::getClrq, dto.getClrqEnd(), StringUtils.isNotEmpty(dto.getClrqEnd()));
        query.eq(ZjtSlxxbLsDO::getZlhkzt, dto.getZlhkzt(), StringUtils.isNotEmpty(dto.getZlhkzt()));
        query.ge(ZjtSlxxbLsDO::getHksj, dto.getHksjStart(), StringUtils.isNotEmpty(dto.getHksjStart()));
        query.le(ZjtSlxxbLsDO::getHksj, dto.getHksjEnd(), StringUtils.isNotEmpty(dto.getHksjEnd()));
        query.eq(ZjtSlxxbLsDO::getBwbha, dto.getBwbha(), StringUtils.isNotEmpty(dto.getBwbha()));
        query.eq(ZjtSlxxbLsDO::getBwbhb, dto.getBwbhb(), StringUtils.isNotEmpty(dto.getBwbhb()));
        query.ge(ZjtSlxxbLsDO::getShrq, dto.getShrqStart(), StringUtils.isNotEmpty(dto.getShrqStart()));
        query.le(ZjtSlxxbLsDO::getShrq, dto.getShrqEnd(), StringUtils.isNotEmpty(dto.getShrqEnd()));
        query.ge(ZjtSlxxbLsDO::getStjssj, dto.getStjssjStart(), StringUtils.isNotEmpty(dto.getStjssjStart()));
        query.le(ZjtSlxxbLsDO::getStjssj, dto.getStjssjEnd(), StringUtils.isNotEmpty(dto.getStjssjEnd()));
        query.eq(ZjtSlxxbLsDO::getBwbhc, dto.getBwbhc(), StringUtils.isNotEmpty(dto.getBwbhc()));
        query.eq(ZjtSlxxbLsDO::getFjpch, dto.getFjpch(), StringUtils.isNotEmpty(dto.getFjpch()));
        query.eq(ZjtSlxxbLsDO::getRlbdid, dto.getRlbdid(), StringUtils.isNotEmpty(dto.getRlbdid()));
        query.eq(ZjtSlxxbLsDO::getRlbdbz, dto.getRlbdbz(), StringUtils.isNotEmpty(dto.getRlbdbz()));
        query.ge(ZjtSlxxbLsDO::getRlbdsj, dto.getRlbdsjStart(), StringUtils.isNotEmpty(dto.getRlbdsjStart()));
        query.le(ZjtSlxxbLsDO::getRlbdsj, dto.getRlbdsjEnd(), StringUtils.isNotEmpty(dto.getRlbdsjEnd()));
        query.eq(ZjtSlxxbLsDO::getZwyzw, dto.getZwyzw(), StringUtils.isNotEmpty(dto.getZwyzw()));
        query.eq(ZjtSlxxbLsDO::getZwyzcjg, dto.getZwyzcjg(), StringUtils.isNotEmpty(dto.getZwyzcjg()));
        query.eq(ZjtSlxxbLsDO::getZwezw, dto.getZwezw(), StringUtils.isNotEmpty(dto.getZwezw()));
        query.eq(ZjtSlxxbLsDO::getZwezcjg, dto.getZwezcjg(), StringUtils.isNotEmpty(dto.getZwezcjg()));
        query.eq(ZjtSlxxbLsDO::getZwcjjgdm, dto.getZwcjjgdm(), StringUtils.isNotEmpty(dto.getZwcjjgdm()));
        query.eq(ZjtSlxxbLsDO::getSzyczkdm, dto.getSzyczkdm(), StringUtils.isNotEmpty(dto.getSzyczkdm()));
        query.eq(ZjtSlxxbLsDO::getSfzwzj, dto.getSfzwzj(), StringUtils.isNotEmpty(dto.getSfzwzj()));
        query.eq(ZjtSlxxbLsDO::getDztbbz, dto.getDztbbz(), Objects.nonNull(dto.getDztbbz()));
        query.ge(ZjtSlxxbLsDO::getDztbsj, dto.getDztbsjStart(), StringUtils.isNotEmpty(dto.getDztbsjStart()));
        query.le(ZjtSlxxbLsDO::getDztbsj, dto.getDztbsjEnd(), StringUtils.isNotEmpty(dto.getDztbsjEnd()));
        query.eq(ZjtSlxxbLsDO::getDzsjbbh, dto.getDzsjbbh(), StringUtils.isNotEmpty(dto.getDzsjbbh()));
        query.eq(ZjtSlxxbLsDO::getSlfs, dto.getSlfs(), StringUtils.isNotEmpty(dto.getSlfs()));
        query.eq(ZjtSlxxbLsDO::getSlfsx, dto.getSlfsx(), StringUtils.isNotEmpty(dto.getSlfsx()));
        query.eq(ZjtSlxxbLsDO::getZwtxid, dto.getZwtxid(), StringUtils.isNotEmpty(dto.getZwtxid()));
        query.eq(ZjtSlxxbLsDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(ZjtSlxxbLsDO::getHjddzbm, dto.getHjddzbm(), StringUtils.isNotEmpty(dto.getHjddzbm()));
        query.eq(ZjtSlxxbLsDO::getLssfzslbz, dto.getLssfzslbz(), StringUtils.isNotEmpty(dto.getLssfzslbz()));
        query.eq(ZjtSlxxbLsDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtSlxxbLsDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtSlxxbLsDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.likeLeft(ZjtSlxxbLsDO::getZjzdssxq, dto.getZjzdssxq(), StringUtils.isNotEmpty(dto.getZjzdssxq()));
        query.eq(ZjtSlxxbLsDO::getZjzdxz, dto.getZjzdxz(), StringUtils.isNotEmpty(dto.getZjzdxz()));
        query.ge(ZjtSlxxbLsDO::getLqrq, dto.getLqrqStart(), StringUtils.isNotEmpty(dto.getLqrqStart()));
        query.le(ZjtSlxxbLsDO::getLqrq, dto.getLqrqEnd(), StringUtils.isNotEmpty(dto.getLqrqEnd()));
        query.eq(ZjtSlxxbLsDO::getLqrxm, ColumnUtils.encryptColumn(dto.getLqrxm()), StringUtils.isNotEmpty(dto.getLqrxm()));
        query.eq(ZjtSlxxbLsDO::getLqrsfhm, dto.getLqrsfhm(), StringUtils.isNotEmpty(dto.getLqrsfhm()));
        query.eq(ZjtSlxxbLsDO::getLqrzpid, dto.getLqrzpid(), StringUtils.isNotEmpty(dto.getLqrzpid()));
        query.ge(ZjtSlxxbLsDO::getZjddrq, dto.getZjddrqStart(), StringUtils.isNotEmpty(dto.getZjddrqStart()));
        query.le(ZjtSlxxbLsDO::getZjddrq, dto.getZjddrqEnd(), StringUtils.isNotEmpty(dto.getZjddrqEnd()));
        query.eq(ZjtSlxxbLsDO::getLzczrid, dto.getLzczrid(), StringUtils.isNotEmpty(dto.getLzczrid()));
        query.eq(ZjtSlxxbLsDO::getLzczrxm, ColumnUtils.encryptColumn(dto.getLzczrxm()), StringUtils.isNotEmpty(dto.getLzczrxm()));
        query.eq(ZjtSlxxbLsDO::getLzczrdwdm, dto.getLzczrdwdm(), StringUtils.isNotEmpty(dto.getLzczrdwdm()));
        query.eq(ZjtSlxxbLsDO::getLzczrdwmc, dto.getLzczrdwmc(), StringUtils.isNotEmpty(dto.getLzczrdwmc()));
        query.eq(ZjtSlxxbLsDO::getShrxm, ColumnUtils.encryptColumn(dto.getShrxm()), StringUtils.isNotEmpty(dto.getShrxm()));
        query.eq(ZjtSlxxbLsDO::getShdw, dto.getShdw(), StringUtils.isNotEmpty(dto.getShdw()));
        query.eq(ZjtSlxxbLsDO::getShqk, dto.getShqk(), StringUtils.isNotEmpty(dto.getShqk()));
        query.ge(ZjtSlxxbLsDO::getQfrq, dto.getQfrqStart(), StringUtils.isNotEmpty(dto.getQfrqStart()));
        query.le(ZjtSlxxbLsDO::getQfrq, dto.getQfrqEnd(), StringUtils.isNotEmpty(dto.getQfrqEnd()));
        query.eq(ZjtSlxxbLsDO::getQfrid, dto.getQfrid(), StringUtils.isNotEmpty(dto.getQfrid()));
        query.eq(ZjtSlxxbLsDO::getQfrxm, ColumnUtils.encryptColumn(dto.getQfrxm()), StringUtils.isNotEmpty(dto.getQfrxm()));
        query.eq(ZjtSlxxbLsDO::getQfdwjgdm, dto.getQfdwjgdm(), StringUtils.isNotEmpty(dto.getQfdwjgdm()));
        query.eq(ZjtSlxxbLsDO::getQfdw, dto.getQfdw(), StringUtils.isNotEmpty(dto.getQfdw()));
        query.ge(ZjtSlxxbLsDO::getDsshrq, dto.getDsshrqStart(), StringUtils.isNotEmpty(dto.getDsshrqStart()));
        query.le(ZjtSlxxbLsDO::getDsshrq, dto.getDsshrqEnd(), StringUtils.isNotEmpty(dto.getDsshrqEnd()));
        query.eq(ZjtSlxxbLsDO::getDsshrxm, ColumnUtils.encryptColumn(dto.getDsshrxm()), StringUtils.isNotEmpty(dto.getDsshrxm()));
        query.eq(ZjtSlxxbLsDO::getDsshdw, dto.getDsshdw(), StringUtils.isNotEmpty(dto.getDsshdw()));
        query.eq(ZjtSlxxbLsDO::getDsshqk, dto.getDsshqk(), StringUtils.isNotEmpty(dto.getDsshqk()));
        query.eq(ZjtSlxxbLsDO::getRwid, dto.getRwid(), StringUtils.isNotEmpty(dto.getRwid()));
        query.eq(ZjtSlxxbLsDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtSlxxbLsDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(ZjtSlxxbLsDO::getSldfjsjgsdwdm, dto.getSldfjsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwdm()));
        query.eq(ZjtSlxxbLsDO::getSldfjsjgsdwmc, dto.getSldfjsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwmc()));
        query.eq(ZjtSlxxbLsDO::getShdwdm, dto.getShdwdm(), StringUtils.isNotEmpty(dto.getShdwdm()));
        query.eq(ZjtSlxxbLsDO::getCldwdm, dto.getCldwdm(), StringUtils.isNotEmpty(dto.getCldwdm()));
        query.eq(ZjtSlxxbLsDO::getDsshdwdm, dto.getDsshdwdm(), StringUtils.isNotEmpty(dto.getDsshdwdm()));
        query.eq(ZjtSlxxbLsDO::getShrid, dto.getShrid(), StringUtils.isNotEmpty(dto.getShrid()));
        query.eq(ZjtSlxxbLsDO::getDsshrid, dto.getDsshrid(), StringUtils.isNotEmpty(dto.getDsshrid()));
        query.eq(ZjtSlxxbLsDO::getSfdjh, dto.getSfdjh(), StringUtils.isNotEmpty(dto.getSfdjh()));
        query.eq(ZjtSlxxbLsDO::getRwzxrzbh, dto.getRwzxrzbh(), StringUtils.isNotEmpty(dto.getRwzxrzbh()));
        query.ge(ZjtSlxxbLsDO::getRwddsj, dto.getRwddsjStart(), StringUtils.isNotEmpty(dto.getRwddsjStart()));
        query.le(ZjtSlxxbLsDO::getRwddsj, dto.getRwddsjEnd(), StringUtils.isNotEmpty(dto.getRwddsjEnd()));
        query.eq(ZjtSlxxbLsDO::getSlyckrxsfbd, dto.getSlyckrxsfbd(), StringUtils.isNotEmpty(dto.getSlyckrxsfbd()));
        query.ge(ZjtSlxxbLsDO::getRxbdkssj, dto.getRxbdkssjStart(), StringUtils.isNotEmpty(dto.getRxbdkssjStart()));
        query.le(ZjtSlxxbLsDO::getRxbdkssj, dto.getRxbdkssjEnd(), StringUtils.isNotEmpty(dto.getRxbdkssjEnd()));
        query.eq(ZjtSlxxbLsDO::getRxbdhs, dto.getRxbdhs(), StringUtils.isNotEmpty(dto.getRxbdhs()));
        query.eq(ZjtSlxxbLsDO::getRxbdxsd, dto.getRxbdxsd(), StringUtils.isNotEmpty(dto.getRxbdxsd()));
        query.eq(ZjtSlxxbLsDO::getRxbdkbh, dto.getRxbdkbh(), StringUtils.isNotEmpty(dto.getRxbdkbh()));
        query.eq(ZjtSlxxbLsDO::getRxbdjg, dto.getRxbdjg(), StringUtils.isNotEmpty(dto.getRxbdjg()));
        query.eq(ZjtSlxxbLsDO::getSlylszwsfbd, dto.getSlylszwsfbd(), StringUtils.isNotEmpty(dto.getSlylszwsfbd()));
        query.eq(ZjtSlxxbLsDO::getZwybdjg, dto.getZwybdjg(), StringUtils.isNotEmpty(dto.getZwybdjg()));
        query.eq(ZjtSlxxbLsDO::getZwybdxsd, dto.getZwybdxsd(), StringUtils.isNotEmpty(dto.getZwybdxsd()));
        query.eq(ZjtSlxxbLsDO::getZwebdjg, dto.getZwebdjg(), StringUtils.isNotEmpty(dto.getZwebdjg()));
        query.eq(ZjtSlxxbLsDO::getZwebdxsd, dto.getZwebdxsd(), StringUtils.isNotEmpty(dto.getZwebdxsd()));
        query.eq(ZjtSlxxbLsDO::getLzszwsfhy, dto.getLzszwsfhy(), StringUtils.isNotEmpty(dto.getLzszwsfhy()));
        query.eq(ZjtSlxxbLsDO::getLzszwyhyjg, dto.getLzszwyhyjg(), StringUtils.isNotEmpty(dto.getLzszwyhyjg()));
        query.eq(ZjtSlxxbLsDO::getLzszwyhyxsd, dto.getLzszwyhyxsd(), StringUtils.isNotEmpty(dto.getLzszwyhyxsd()));
        query.eq(ZjtSlxxbLsDO::getLzszwehyjg, dto.getLzszwehyjg(), StringUtils.isNotEmpty(dto.getLzszwehyjg()));
        query.eq(ZjtSlxxbLsDO::getLzszwehyxsd, dto.getLzszwehyxsd(), StringUtils.isNotEmpty(dto.getLzszwehyxsd()));
        query.eq(ZjtSlxxbLsDO::getLzssfjhjz, dto.getLzssfjhjz(), StringUtils.isNotEmpty(dto.getLzssfjhjz()));
        query.eq(ZjtSlxxbLsDO::getSlylszwbdsm, dto.getSlylszwbdsm(), StringUtils.isNotEmpty(dto.getSlylszwbdsm()));
        query.eq(ZjtSlxxbLsDO::getLzszwbdsm, dto.getLzszwbdsm(), StringUtils.isNotEmpty(dto.getLzszwbdsm()));
        query.eq(ZjtSlxxbLsDO::getSqrgmsfhm, ColumnUtils.encryptColumn(dto.getSqrgmsfhm()), StringUtils.isNotEmpty(dto.getSqrgmsfhm()));
        query.eq(ZjtSlxxbLsDO::getSqrxm, ColumnUtils.encryptColumn(dto.getSqrxm()), StringUtils.isNotEmpty(dto.getSqrxm()));
        query.eq(ZjtSlxxbLsDO::getSqrlxdh, ColumnUtils.encryptColumn(dto.getSqrlxdh()), StringUtils.isNotEmpty(dto.getSqrlxdh()));
        query.ge(ZjtSlxxbLsDO::getJzqsrq, dto.getJzqsrqStart(), StringUtils.isNotEmpty(dto.getJzqsrqStart()));
        query.le(ZjtSlxxbLsDO::getJzqsrq, dto.getJzqsrqEnd(), StringUtils.isNotEmpty(dto.getJzqsrqEnd()));
        query.eq(ZjtSlxxbLsDO::getCzylxdh, ColumnUtils.encryptColumn(dto.getCzylxdh()), StringUtils.isNotEmpty(dto.getCzylxdh()));
        query.eq(ZjtSlxxbLsDO::getShrlxdh, ColumnUtils.encryptColumn(dto.getShrlxdh()), StringUtils.isNotEmpty(dto.getShrlxdh()));
        query.eq(ZjtSlxxbLsDO::getDsshrlxdh, ColumnUtils.encryptColumn(dto.getDsshrlxdh()), StringUtils.isNotEmpty(dto.getDsshrlxdh()));
        query.eq(ZjtSlxxbLsDO::getZfcje, dto.getZfcje(), Objects.nonNull(dto.getZfcje()));
        query.eq(ZjtSlxxbLsDO::getQxfcje, dto.getQxfcje(), Objects.nonNull(dto.getQxfcje()));
        query.eq(ZjtSlxxbLsDO::getDsfcje, dto.getDsfcje(), Objects.nonNull(dto.getDsfcje()));
        query.eq(ZjtSlxxbLsDO::getZxfcje, dto.getZxfcje(), Objects.nonNull(dto.getZxfcje()));
        query.eq(ZjtSlxxbLsDO::getYzkddh, dto.getYzkddh(), StringUtils.isNotEmpty(dto.getYzkddh()));
        query.eq(ZjtSlxxbLsDO::getSpdz1, dto.getSpdz1(), StringUtils.isNotEmpty(dto.getSpdz1()));
        query.eq(ZjtSlxxbLsDO::getSfmsbswzp, dto.getSfmsbswzp(), StringUtils.isNotEmpty(dto.getSfmsbswzp()));
        query.eq(ZjtSlxxbLsDO::getHlwsqid, dto.getHlwsqid(), StringUtils.isNotEmpty(dto.getHlwsqid()));
        query.eq(ZjtSlxxbLsDO::getUsername, dto.getUsername(), StringUtils.isNotEmpty(dto.getUsername()));
        query.eq(ZjtSlxxbLsDO::getPjjg, dto.getPjjg(), StringUtils.isNotEmpty(dto.getPjjg()));
        query.eq(ZjtSlxxbLsDO::getPjpljc, dto.getPjpljc(), StringUtils.isNotEmpty(dto.getPjpljc()));
        query.ge(ZjtSlxxbLsDO::getPjsj, dto.getPjsjStart(), StringUtils.isNotEmpty(dto.getPjsjStart()));
        query.le(ZjtSlxxbLsDO::getPjsj, dto.getPjsjEnd(), StringUtils.isNotEmpty(dto.getPjsjEnd()));
        query.eq(ZjtSlxxbLsDO::getFwdx, dto.getFwdx(), StringUtils.isNotEmpty(dto.getFwdx()));
        query.eq(ZjtSlxxbLsDO::getSfdgszj, dto.getSfdgszj(), StringUtils.isNotEmpty(dto.getSfdgszj()));
        query.eq(ZjtSlxxbLsDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(ZjtSlxxbLsDO::getSlshjdz, dto.getSlshjdz(), StringUtils.isNotEmpty(dto.getSlshjdz()));
        query.eq(ZjtSlxxbLsDO::getKsywlsh, dto.getKsywlsh(), StringUtils.isNotEmpty(dto.getKsywlsh()));
        query.eq(ZjtSlxxbLsDO::getKsfsbz, dto.getKsfsbz(), StringUtils.isNotEmpty(dto.getKsfsbz()));
        query.ge(ZjtSlxxbLsDO::getKsfssj, dto.getKsfssjStart(), StringUtils.isNotEmpty(dto.getKsfssjStart()));
        query.le(ZjtSlxxbLsDO::getKsfssj, dto.getKsfssjEnd(), StringUtils.isNotEmpty(dto.getKsfssjEnd()));
        query.eq(ZjtSlxxbLsDO::getBase64zp, dto.getBase64zp(), Objects.nonNull(dto.getBase64zp()));
        query.eq(ZjtSlxxbLsDO::getZplsid, dto.getZplsid(), StringUtils.isNotEmpty(dto.getZplsid()));
        query.eq(ZjtSlxxbLsDO::getZpcjlx, dto.getZpcjlx(), StringUtils.isNotEmpty(dto.getZpcjlx()));
        query.eq(ZjtSlxxbLsDO::getZpsbbsh, dto.getZpsbbsh(), StringUtils.isNotEmpty(dto.getZpsbbsh()));
        query.eq(ZjtSlxxbLsDO::getZpsbppxhdm, dto.getZpsbppxhdm(), StringUtils.isNotEmpty(dto.getZpsbppxhdm()));
        query.eq(ZjtSlxxbLsDO::getZpsbppxh, dto.getZpsbppxh(), StringUtils.isNotEmpty(dto.getZpsbppxh()));
        query.eq(ZjtSlxxbLsDO::getZpytbid, dto.getZpytbid(), Objects.nonNull(dto.getZpytbid()));
        query.eq(ZjtSlxxbLsDO::getSfbczpyt, dto.getSfbczpyt(), StringUtils.isNotEmpty(dto.getSfbczpyt()));
        query.ge(ZjtSlxxbLsDO::getLxcjsj, dto.getLxcjsjStart(), StringUtils.isNotEmpty(dto.getLxcjsjStart()));
        query.le(ZjtSlxxbLsDO::getLxcjsj, dto.getLxcjsjEnd(), StringUtils.isNotEmpty(dto.getLxcjsjEnd()));
        query.eq(ZjtSlxxbLsDO::getLxczymc, dto.getLxczymc(), StringUtils.isNotEmpty(dto.getLxczymc()));
        query.eq(ZjtSlxxbLsDO::getLxsldw, dto.getLxsldw(), StringUtils.isNotEmpty(dto.getLxsldw()));
        query.eq(ZjtSlxxbLsDO::getZpsbbh, dto.getZpsbbh(), StringUtils.isNotEmpty(dto.getZpsbbh()));
        query.likeLeft(ZjtSlxxbLsDO::getZpsbzcdw, ColumnUtils.removeZeroSuffix(dto.getZpsbzcdw()), StringUtils.isNotEmpty(dto.getZpsbzcdw()));
        query.eq(ZjtSlxxbLsDO::getZpsbscgs, dto.getZpsbscgs(), StringUtils.isNotEmpty(dto.getZpsbscgs()));
        query.eq(ZjtSlxxbLsDO::getZpsbxsgs, dto.getZpsbxsgs(), StringUtils.isNotEmpty(dto.getZpsbxsgs()));
        query.eq(ZjtSlxxbLsDO::getBzsbbh, dto.getBzsbbh(), StringUtils.isNotEmpty(dto.getBzsbbh()));
        query.likeLeft(ZjtSlxxbLsDO::getBzsbzcdw, ColumnUtils.removeZeroSuffix(dto.getBzsbzcdw()), StringUtils.isNotEmpty(dto.getBzsbzcdw()));
        query.eq(ZjtSlxxbLsDO::getBzsbppxhdm, dto.getBzsbppxhdm(), StringUtils.isNotEmpty(dto.getBzsbppxhdm()));
        query.eq(ZjtSlxxbLsDO::getBzsbppxh, dto.getBzsbppxh(), StringUtils.isNotEmpty(dto.getBzsbppxh()));
        query.eq(ZjtSlxxbLsDO::getBzsbscgs, dto.getBzsbscgs(), StringUtils.isNotEmpty(dto.getBzsbscgs()));
        query.eq(ZjtSlxxbLsDO::getBzsbxsgs, dto.getBzsbxsgs(), StringUtils.isNotEmpty(dto.getBzsbxsgs()));
        query.eq(ZjtSlxxbLsDO::getJjlxrxm, ColumnUtils.encryptColumn(dto.getJjlxrxm()), StringUtils.isNotEmpty(dto.getJjlxrxm()));
        query.eq(ZjtSlxxbLsDO::getJjlxrdh, dto.getJjlxrdh(), StringUtils.isNotEmpty(dto.getJjlxrdh()));
        query.eq(ZjtSlxxbLsDO::getJjlxryslrgx, dto.getJjlxryslrgx(), StringUtils.isNotEmpty(dto.getJjlxryslrgx()));
        query.eq(ZjtSlxxbLsDO::getZaglywxtbh, dto.getZaglywxtbh(), StringUtils.isNotEmpty(dto.getZaglywxtbh()));
        query.eq(ZjtSlxxbLsDO::getZaglzwfwsxbm, dto.getZaglzwfwsxbm(), StringUtils.isNotEmpty(dto.getZaglzwfwsxbm()));
        query.eq(ZjtSlxxbLsDO::getZaglywlbdm, dto.getZaglywlbdm(), StringUtils.isNotEmpty(dto.getZaglywlbdm()));
        query.eq(ZjtSlxxbLsDO::getXmmzwz, ColumnUtils.encryptColumn(dto.getXmmzwz()), StringUtils.isNotEmpty(dto.getXmmzwz()));
        query.eq(ZjtSlxxbLsDO::getXbmzwz, dto.getXbmzwz(), StringUtils.isNotEmpty(dto.getXbmzwz()));
        query.eq(ZjtSlxxbLsDO::getMzmzwz, dto.getMzmzwz(), StringUtils.isNotEmpty(dto.getMzmzwz()));
        query.eq(ZjtSlxxbLsDO::getMzfjxmzwz, ColumnUtils.encryptColumn(dto.getMzfjxmzwz()), StringUtils.isNotEmpty(dto.getMzfjxmzwz()));
        query.eq(ZjtSlxxbLsDO::getZzhymzwz, dto.getZzhymzwz(), StringUtils.isNotEmpty(dto.getZzhymzwz()));
        query.eq(ZjtSlxxbLsDO::getZzhemzwz, dto.getZzhemzwz(), StringUtils.isNotEmpty(dto.getZzhemzwz()));
        query.eq(ZjtSlxxbLsDO::getZzhsmzwz, dto.getZzhsmzwz(), StringUtils.isNotEmpty(dto.getZzhsmzwz()));
        query.eq(ZjtSlxxbLsDO::getQfjgmzwz, dto.getQfjgmzwz(), StringUtils.isNotEmpty(dto.getQfjgmzwz()));
        query.eq(ZjtSlxxbLsDO::getCzyip, dto.getCzyip(), StringUtils.isNotEmpty(dto.getCzyip()));
        query.eq(ZjtSlxxbLsDO::getZwcjqid, dto.getZwcjqid(), StringUtils.isNotEmpty(dto.getZwcjqid()));
        query.eq(ZjtSlxxbLsDO::getSfsqltz, dto.getSfsqltz(), StringUtils.isNotEmpty(dto.getSfsqltz()));
        query.eq(ZjtSlxxbLsDO::getLtzshjg, dto.getLtzshjg(), StringUtils.isNotEmpty(dto.getLtzshjg()));
        query.ge(ZjtSlxxbLsDO::getLtzspsj, dto.getLtzspsjStart(), StringUtils.isNotEmpty(dto.getLtzspsjStart()));
        query.le(ZjtSlxxbLsDO::getLtzspsj, dto.getLtzspsjEnd(), StringUtils.isNotEmpty(dto.getLtzspsjEnd()));
        query.eq(ZjtSlxxbLsDO::getLtzsprid, dto.getLtzsprid(), StringUtils.isNotEmpty(dto.getLtzsprid()));
        query.eq(ZjtSlxxbLsDO::getLtzsprxm, ColumnUtils.encryptColumn(dto.getLtzsprxm()), StringUtils.isNotEmpty(dto.getLtzsprxm()));
        query.eq(ZjtSlxxbLsDO::getLtzsqsy, dto.getLtzsqsy(), StringUtils.isNotEmpty(dto.getLtzsqsy()));
        return query;
    }

    @Override
    public ZjtSlxxbLsDTO convertToDTO(ZjtSlxxbLsDO zjtSlxxbLsDO) {
        return convert.convert(zjtSlxxbLsDO);
    }

    @Override
    public ZjtSlxxbLsDO convertToDO(ZjtSlxxbLsDTO zjtSlxxbLsDTO) {
        return convert.convertToDO(zjtSlxxbLsDTO);
    }

    @Override
    public long countGmsfhmSfyzj(String gmsfhm) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ZjtSlxxbLsDO::getGmsfhm, gmsfhm)
                .lt(ZjtSlxxbLsDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YCB)
                .ne(ZjtSlxxbLsDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF);
        return count(queryWrapper);
    }
}
