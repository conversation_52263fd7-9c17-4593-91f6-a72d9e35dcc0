package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.jk.ws.JkZalwcxApiManager;
import com.zjjcnt.project.ck.jk.ws.VoZalwcxqqHjgljbxx;
import com.zjjcnt.project.ck.jk.ws.ZalwcxqqHjgljbxxResultVo;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.sysadmin.domain.Xtsjfw;
import com.zjjcnt.project.ck.sysadmin.manager.XtywqxManager;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxCzrkjbxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxCzrkjbxxbViewQgResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxCzrkjbxxbViewResp;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxCzrkjbxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
* 常住人口基本信息表前端控制器
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@RequiredArgsConstructor
@Tag(name = "常住人口基本信息表")
@RestController
@RequestMapping("/hjxxCzrkjbxxb")
public class HjxxCzrkjbxxbController extends AbstractCrudController<HjxxCzrkjbxxbDTO> {

    private final HjxxCzrkjbxxbService hjxxCzrkjbxxbService;
    private final XtywqxManager xtywqxManager;
    private final JkZalwcxApiManager jkZalwcxApiManager;

    @Override
    protected IBaseService<HjxxCzrkjbxxbDTO> getService() {
        return hjxxCzrkjbxxbService;
    }

    @GetMapping("viewNormalXq")
    @Operation(summary = "查看正常状态的常住人口基本信息表详情(数据范围限制) F1001")
    public CommonResult<HjxxCzrkjbxxbViewResp> viewNormalXq(@RequestParam(value = "gmsfhm") String gmsfhm) {
        return CommonResult.success(doView(gmsfhm, CkZzjConstants.GNBH_HJ_RYXXHQ));
    }

    @GetMapping("viewNormal")
    @Operation(summary = "查看正常状态的常住人口基本信息表详情 F1201")
    public CommonResult<HjxxCzrkjbxxbViewResp> viewNormal(@RequestParam(value = "gmsfhm") String gmsfhm) {
        return CommonResult.success(doView(gmsfhm, CkZzjConstants.GNBH_HJ_RYXXHQ_CX));
    }

    @GetMapping("viewQg")
    @Operation(summary = "查看全国常住人口基本信息表详情")
    public CommonResult<HjxxCzrkjbxxbViewQgResp> viewQg(@RequestParam(value = "gmsfhm") String gmsfhm) {
        VoZalwcxqqHjgljbxx zalwcxqqHjgljbxx = new VoZalwcxqqHjgljbxx();
        zalwcxqqHjgljbxx.setGmsfhm(gmsfhm);
        zalwcxqqHjgljbxx.setZt(SysadminConstants.GAB_RKXX_CX_ZT_QB);
        zalwcxqqHjgljbxx.setInvokeSys(CkZzjConstants.INVOKE_SYS_XC_ZZJ);
        ZalwcxqqHjgljbxxResultVo gabRkxx = jkZalwcxApiManager.getGabRkxx(zalwcxqqHjgljbxx);
        if (Objects.nonNull(gabRkxx) && StringUtils.isBlank(gabRkxx.getRkglzxlbdm())) {
            return CommonResult.success(HjxxCzrkjbxxbConvert.INSTANCE.convertToResp(gabRkxx));
        }
        return CommonResult.success(null);
    }

    private HjxxCzrkjbxxbViewResp doView(String gmsfhm, String ywid) {
        List<Xtsjfw> xtsjfws = xtywqxManager.listDataRange(SecurityUtils.getUserId(), ywid);
        HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO = hjxxCzrkjbxxbService.findNormalByGmsfhmAndSjfw(gmsfhm, xtsjfws);
        HjxxCzrkjbxxbViewResp hjxxCzrkjbxxbViewResp = HjxxCzrkjbxxbConvert.INSTANCE.convertToViewResp(hjxxCzrkjbxxbDTO);

        if (Objects.nonNull(hjxxCzrkjbxxbDTO)) {
            hjxxCzrkjbxxbViewResp.setHjdz(hjxxCzrkjbxxbService.getHjdz(hjxxCzrkjbxxbDTO.getSsxq(), hjxxCzrkjbxxbDTO.getXzjd(),
                    hjxxCzrkjbxxbDTO.getJlx(), hjxxCzrkjbxxbDTO.getMlph(), hjxxCzrkjbxxbDTO.getMlxz()));
        }
        return hjxxCzrkjbxxbViewResp;
    }

}
