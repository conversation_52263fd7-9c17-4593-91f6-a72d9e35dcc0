package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxZplsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZplsbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZplsbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZplsbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZplsbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZplsbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZplsbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxZplsbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 照片临时表Convert
*
* <AUTHOR>
* @date 2024-05-07 11:35:02
*/
@Mapper
public interface HjxxZplsbConvert {

    HjxxZplsbConvert INSTANCE = Mappers.getMapper(HjxxZplsbConvert.class);

    HjxxZplsbDTO convert(HjxxZplsbDO entity);

    @InheritInverseConfiguration(name="convert")
    HjxxZplsbDO convertToDO(HjxxZplsbDTO dto);

    HjxxZplsbDTO convertToDTO(HjxxZplsbPageReq req);

    HjxxZplsbDTO convertToDTO(HjxxZplsbCreateReq req);

    HjxxZplsbDTO convertToDTO(HjxxZplsbUpdateReq req);

    HjxxZplsbPageResp convertToPageResp(HjxxZplsbDTO dto);

    HjxxZplsbViewResp convertToViewResp(HjxxZplsbDTO dto);

    HjxxZplsbCreateResp convertToCreateResp(HjxxZplsbDTO dto);

}
