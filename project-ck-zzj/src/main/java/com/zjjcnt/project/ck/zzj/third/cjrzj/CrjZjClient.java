package com.zjjcnt.project.ck.zzj.third.cjrzj;

import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.web.request.ZlpzCrjZjReq;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.net.SocketTimeoutException;
import java.time.Duration;

/**
 * 出入境质检
 *
 * <AUTHOR>
 * @date 2024-09-20 10:30:00
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class CrjZjClient {

    /**
     * 省厅主平台：41.190.21.100:9010，绍兴备平台：41.190.96.87:9010
     */
    private static final String CRJ_ZJ_URL = "http://41.244.50.36:9010/CropAndCheckImageQuality.ashx";
    private static final String CRJ_ZJ_URI = "/CropAndCheckImageQuality.ashx";

    private static final String BASE64_PREFIX = "data:image/jpeg;base64,";

    private static final long READ_TIMEOUT_SECONDS = 30L;
    private static final long CONNECT_TIMEOUT_SECONDS = 2L;

    private RestTemplate restTemplate;

    @PostConstruct
    public void init() {
        restTemplate = new RestTemplateBuilder()
                .readTimeout(Duration.ofSeconds(READ_TIMEOUT_SECONDS))
                .connectTimeout(Duration.ofSeconds(CONNECT_TIMEOUT_SECONDS))
                .build();
    }

    public CrjZjResp crjZjYzty(ZlpzCrjZjReq zlpzCrjZjReq, String url) {
        CrjZjReq crjZjReq = new CrjZjReq();
        crjZjReq.setModule("3");
        crjZjReq.setFltFile("crj-2024.flt");

        String imageBase64 = zlpzCrjZjReq.getImageBase64();
        if (!StringUtils.startsWith(imageBase64, BASE64_PREFIX)) {
            imageBase64 = BASE64_PREFIX + imageBase64;
        }

        crjZjReq.setImageBase64(imageBase64);
        crjZjReq.setImageFilename(zlpzCrjZjReq.getImageFilename());
        crjZjReq.setVersion("");
        crjZjReq.setOptAutoAd("0");
        if (CkZzjConstants.CRJZJ_TYPE_ZJ.equals(zlpzCrjZjReq.getType())) {
            crjZjReq.setMode(2);
            crjZjReq.setContent("");
        } else if (CkZzjConstants.CRJZJ_TYPE_QXDB.equals(zlpzCrjZjReq.getType())) {
            crjZjReq.setMode(8);
            crjZjReq.setContent("{\"func\":\"SetFaceQCMark\",\"param\":\"\"}");
        } else {
            throw new ServiceException(CkZzjErrorCode.CRJZJ_API_ERROR, "请选择质检模块运行模式");
        }

        if (StringUtils.isNotBlank(url)) {
            url = url + CRJ_ZJ_URI;
        } else {
            url = CRJ_ZJ_URL;
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.toJsonString(crjZjReq), headers);

        CrjZjResp crjZjResp;
        try {
            String result  = restTemplate.postForObject(url, entity, String.class);
            crjZjResp = JsonUtils.parseObject(result, CrjZjResp.class);
        } catch (ResourceAccessException e) {
            // 设置的超时时间内未返回，则返回成功
            Throwable cause = e.getCause();
            if (cause instanceof SocketTimeoutException) {
                crjZjResp = new CrjZjResp();
                crjZjResp.setOk(true);
                crjZjResp.setImageBase64(imageBase64);
            } else {
                log.error("出入境质检接口请求失败", e);
                throw new ServiceException(CkZzjErrorCode.CRJZJ_API_ERROR);
            }
        }
        String resultImageBase64 = crjZjResp.getImageBase64();
        if (StringUtils.isNotBlank(resultImageBase64) && StringUtils.startsWith(resultImageBase64, BASE64_PREFIX)) {
            crjZjResp.setImageBase64(StringUtils.removeStart(resultImageBase64, BASE64_PREFIX));
        }
        return crjZjResp;
    }
}
