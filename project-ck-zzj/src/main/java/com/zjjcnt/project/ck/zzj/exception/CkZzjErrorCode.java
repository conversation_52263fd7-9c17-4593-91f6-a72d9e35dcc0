package com.zjjcnt.project.ck.zzj.exception;

import com.zjjcnt.common.core.exception.ErrorCode;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CkZzjErrorCode implements ErrorCode {
    // 通用错误码
    ERR_COMMON(22000, "系统错误"),
    ERR_ZWGLYWITHDWDM_NULL(22001, "单位范围内无指纹管理员信息!"),
    ZWTX_SAVE_ERROR(22002, "保存指纹图像数据发生错误!"),
    DEVICE_NOT_REGISTER(22003, "设备未注册!"),
//    DEVICE_NOT_REGISTER(22003, "device not register"),
    DEVICE_NOT_FOUND(22004, "设备不存在"),
//    DEVICE_NOT_FOUND(22004, "device not found"),
    DEVICE_ACCOUNT_NOT_MATCH(22005, "注册设备和登录用户不一致"),
//    DEVICE_ACCOUNT_NOT_MATCH(22005, "device and account not match"),
    DEVICE_NOT_ENABLED(22006, "设备未启用"),
//    DEVICE_NOT_ENABLED(22006, "device not enabled"),
    DEVICE_IP_NOT_MATCH(22007, "注册设备ip和登录ip不一致"),
    DEVICE_ALREADY_EXIST(22008, "设备已存在"),
    DEVICE_SAVE_ERROR(22009, "设备信息保存失败"),

    // 指纹仪错误
    ERR_ZWCJSBXX_REPEAT(22010, "系统中已存在启用中的设备!"),
    VERSION_ALREADY_EXIST(22011, "版本号已存在!"),
    ZWXX_NOT_FOUND(22012, "指纹信息不存在"),

    //第三方错误
    PZYJS_SX_ERROR(32002, "绍兴拍照一件事错误"),
    CRJ_API_ERROR(32003, "调用出入境接口错误"),
    CRJZJ_API_ERROR(32004, "调用出入境质检接口错误"),
    YZTY_MPZJ_API_ERROR(32005, "调用一照通用母片质检接口错误"),
    JJ_API_ERROR(32006, "调用交警接口错误"),
    KSSFZ_ROP_API_ERROR(32007, "调用跨省身份证接口错误"),
    KSSFZ_ROP_API_BIZ_ERROR(32008, "调用跨省身份证接口业务错误"),
    YZTY_CJDWSC_API_ERROR(32009, "调用一照通用采集点位上传接口错误"),
    YZTY_CJDWCX_API_ERROR(32010, "调用一照通用采集点位查询接口错误"),
    YZTY_CRJ_ROP_API_ERROR(32011, "调用一照通用出入境照片上传接口错误"),
    YZTY_JJ_ROP_API_ERROR(32012, "调用一照通用交接照片上传接口错误"),
    SFZYCJ_API_ERROR(32013, "调用身份证预采集接口错误"),
    GAB_WCNSLZGSC_HQJMXX_ERROR(32014, "调用公安部未成年资格审查获取加密信息错误");

    /**
     * 错误类型码
     */
    private final Integer code;
    /**
     * 错误类型描述信息
     */
    private final String message;

    CkZzjErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
