package com.zjjcnt.project.ck.zzj.web.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 受理信息上传结果
 *
 * <AUTHOR>
 * @date 2024-10-15 15:24:00
 */
@Data
public class KssfzSlxxUploadResp {


    @Schema(description = "身份证受理id")
    private String sfzslid;

    @Schema(description = "身份证业务受理号")
    private String sfzslh;

    @Schema(description = "缴费金额")
    private String jfje;

    @Schema(description = "缴费二维码")
    private String jfewm;
}
