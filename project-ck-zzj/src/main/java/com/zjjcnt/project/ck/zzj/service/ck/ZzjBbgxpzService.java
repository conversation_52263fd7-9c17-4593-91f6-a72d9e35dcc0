package com.zjjcnt.project.ck.zzj.service.ck;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjBbgxpzDTO;

/**
 * 自助机版本更新配置Service
 *
 * <AUTHOR>
 * @date 2024-07-30 15:27:57
 */
public interface ZzjBbgxpzService extends IBaseService<ZzjBbgxpzDTO> {

    ZzjBbgxpzDTO findLatestVersion(String cxlx);

    /**
     * 查询最低版本
     * @param cxlx 程序类型
     * @return
     */
    ZzjBbgxpzDTO findLowestVersion(String cxlx);

}
