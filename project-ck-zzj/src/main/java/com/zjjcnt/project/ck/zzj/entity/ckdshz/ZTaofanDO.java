package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 逃犯库
 *
 * <AUTHOR>
 * @date 2024-11-22 10:47:00
 */
@Data
@Table(value = "z_taofan")
public class ZTaofanDO implements IdEntity<String> {

    @Id(keyType = KeyType.None)
    private String rybh;

    private String sfzh;

    private String xm;

    @Override
    public String getId() {
        return rybh;
    }

    @Override
    public void setId(String id) {
        this.rybh = id;
    }
}
