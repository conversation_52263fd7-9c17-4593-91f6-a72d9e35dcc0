package com.zjjcnt.project.ck.zzj.third.rop;

import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.third.rop.req.*;
import com.zjjcnt.project.ck.zzj.third.rop.resp.*;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * 终端办证设备办理跨省身份证rop接口
 *
 * <AUTHOR>
 * @date 2024-10-14 11:10:00
 */
@Slf4j
@Component
public class ZdbzsbKssfzRopClient {

    @Value("${zjjcnt.rop.app-key:czrkzdyh}")
    private String appKey;

    @Value("${zjjcnt.rop.url}")
    private String serverUrl;

    private static final String RESULT_CODE_SUCCESS = "1";
    private RestTemplate restTemplate;

    private static final long READ_TIMEOUT_SECONDS = 20L;
    private static final long CONNECT_TIMEOUT_SECONDS = 2L;

    @PostConstruct
    public void init() {
        restTemplate = new RestTemplateBuilder()
                .readTimeout(Duration.ofSeconds(READ_TIMEOUT_SECONDS))
                .connectTimeout(Duration.ofSeconds(CONNECT_TIMEOUT_SECONDS))
                .build();
    }

    /**
     * 办证资格审查
     *
     * @param kssfzBzzgscRopReq
     * @return
     */
    public KssfzBzzgscRopResp queryBzzgsc(KssfzBzzgscRopReq kssfzBzzgscRopReq) {
        kssfzBzzgscRopReq.setXthjdm(StringUtils.defaultIfBlank(kssfzBzzgscRopReq.getXthjdm(), CkZzjConstants.KSXT_XTHJDM_SFZBHL));
        MultiValueMap<String, String> form = createCommonParam();
        form.add("dataJson", Base64.getEncoder().encodeToString(JsonUtils.toJsonString(kssfzBzzgscRopReq)
                .getBytes(StandardCharsets.UTF_8)));

        String method;
        if (CkZzjConstants.KSXT_XTHJDM_SFZSS.equals(kssfzBzzgscRopReq.getXthjdm())) {
            //首申资格审查方法名
            method = "queryZdbzsbBzzgscSs";
        } else {
            //补换领资格审查方法名
            method = "queryZdbzsbBzzgscBhl";
        }

        return doRequest(method, form, KssfzBzzgscRopResp.class);
    }

    /**
     * 跨省身份证申领信息提交
     *
     * @param kssfzSlxxUploadRopReq
     * @param clxxUploadReqList
     * @return
     */
    public KssfzSlxxUploadRopResp processZdbzsbKssfzxxtj(KssfzSlxxUploadRopReq kssfzSlxxUploadRopReq,
                                                         List<KssfzClxxUploadRopReq> clxxUploadReqList) {
        MultiValueMap<String, String> form = createCommonParam();
        form.add("dataJson", Base64.getEncoder().encodeToString(JsonUtils.toJsonString(kssfzSlxxUploadRopReq)
                .getBytes(StandardCharsets.UTF_8)));
        form.add("fileJson", Base64.getEncoder().encodeToString(JsonUtils.toJsonString(clxxUploadReqList)
                .getBytes(StandardCharsets.UTF_8)));

        String method;
        if (CkZzjConstants.SLYY_WCNSS.equals(kssfzSlxxUploadRopReq.getSlyy())) {
            //首申受理方法名
            method = "processZdbzsbKssfzxxtjSs";
        } else {
            //补换领受理方法名
            method = "processZdbzsbKssfzxxtjBhl";
        }

        return doRequest(method, form, KssfzSlxxUploadRopResp.class);
    }

    /**
     * 缴费结果获取
     *
     * @param kssfzJfjghqRopReq
     * @return
     */
    public KssfzJfjghqRopResp queryZdbzsbJfjghq(KssfzJfjghqRopReq kssfzJfjghqRopReq) {
        MultiValueMap<String, String> form = createCommonParam();
        form.add("dataJson", Base64.getEncoder().encodeToString(JsonUtils.toJsonString(kssfzJfjghqRopReq)
                .getBytes(StandardCharsets.UTF_8)));
        return doRequest("queryZdbzsbJfjghq", form, KssfzJfjghqRopResp.class);
    }

    /**
     * 终端受理结束提交
     *
     * @param kssfzSljstjRopReq
     * @return
     */
    public boolean processZdbzsbSljstj(KssfzSljstjRopReq kssfzSljstjRopReq, List<KssfzClxxUploadRopReq> clxxUploadReqList) {
        MultiValueMap<String, String> form = createCommonParam();
        form.add("dataJson", Base64.getEncoder().encodeToString(JsonUtils.toJsonString(kssfzSljstjRopReq)
                .getBytes(StandardCharsets.UTF_8)));

        if (!CollectionUtils.isEmpty(clxxUploadReqList)) {
            form.add("fileJson", Base64.getEncoder().encodeToString(JsonUtils.toJsonString(clxxUploadReqList)
                    .getBytes(StandardCharsets.UTF_8)));
        }
        doRequest("processZdbzsbSljstj", form, Void.class);
        return true;
    }

    /**
     * 未成年首次办证资格审查
     *
     * @return
     */
    public KssfzWcnscbzzgscRopResp queryWcnscbzzgsc(KssfzWcnscbzzgscRopReq kssfzWcnscbzzgscRopReq) {
        MultiValueMap<String, String> form = createCommonParam();
        form.add("dataJson", Base64.getEncoder().encodeToString(JsonUtils.toJsonString(kssfzWcnscbzzgscRopReq)
                .getBytes(StandardCharsets.UTF_8)));
        return doRequest("queryZdbzsbWcnscbzzgsc", form, KssfzWcnscbzzgscRopResp.class);
    }

    private <T> T doRequest(String method, MultiValueMap<String, String> form, Class<T> tClass) {
        form.add("method", method);
        String result = restTemplate.postForObject(serverUrl, form, String.class);
        KssfzCommonResp response = JsonUtils.parseObject(result, KssfzCommonResp.class);
        assert response != null;
        if (Objects.isNull(response.getFhjg())) {
            RopErrorResp errorResponse = JsonUtils.parseObject(result, RopErrorResp.class);
            log.error("调用跨省身份证rop接口错误, method={}, code={}, message={}, solution={}", method,
                    errorResponse.getCode(), errorResponse.getMessage(), errorResponse.getSolution());
            throw new ServiceException(CkZzjErrorCode.KSSFZ_ROP_API_ERROR);
        } else if (RESULT_CODE_SUCCESS.equals(response.getFhjg())) {
            String retJson = response.getRetJson();
            if (Objects.isNull(retJson)) {
                return null;
            }
            return JsonUtils.parseObject(new String(Base64.getDecoder().decode(retJson), StandardCharsets.UTF_8),
                    tClass);
        } else {
            log.error("调用跨省身份证rop接口错误, method={}, fhjg={}, fhms={}", method, response.getFhjg(), response.getFhms());
            throw new ServiceException(CkZzjErrorCode.KSSFZ_ROP_API_BIZ_ERROR, response.getFhms());
        }
    }

    private MultiValueMap<String, String> createCommonParam() {
        MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
        form.add("appKey", appKey);
        form.add("v", "1.0");
        form.add("format", "json");
        return form;
    }

}
