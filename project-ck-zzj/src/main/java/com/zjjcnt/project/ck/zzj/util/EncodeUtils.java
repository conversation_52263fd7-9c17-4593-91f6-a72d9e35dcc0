package com.zjjcnt.project.ck.zzj.util;

import com.zjjcnt.common.core.exception.ServiceException;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

public class EncodeUtils {
    public static String encode(String content) {
        try {
            return URLEncoder.encode(content, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new ServiceException(500, "编码出错：" + content + " " + e.getMessage());
        }
    }

    public static String decode(String content) {
        try {
            return URLDecoder.decode(content, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new ServiceException(500, "解码出错：" + content + " " + e.getMessage());
        }
    }
}
