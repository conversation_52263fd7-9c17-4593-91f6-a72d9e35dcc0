package com.zjjcnt.project.ck.zzj.service.ck;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSlxxbDTO;
import com.zjjcnt.project.ck.zzj.web.request.ZjtLssfzReq;
import com.zjjcnt.project.ck.zzj.web.request.ZjtLssfzShReq;

/**
 * 临时身份证受理信息表Service
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
public interface ZjtLssfzSlxxbService extends IBaseService<ZjtLssfzSlxxbDTO> {

    ZjtLssfzSlxxbDTO lssfzSl(ZjtLssfzReq req, String sldsjgsdwdm, String sldsjgsdwmc, String sldfjsjgsdwdm, String sldfjsjgsdwmc);

    ZjtLssfzSlxxbDTO lssfzSh(ZjtLssfzShReq req);
}
