package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtJmsfzxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtJmsfzxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtJmsfzxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtJmsfzxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtJmsfzxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtJmsfzxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtJmsfzxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtJmsfzxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 居民身份证信息表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtJmsfzxxbConvert {

    ZjtJmsfzxxbConvert INSTANCE = Mappers.getMapper(ZjtJmsfzxxbConvert.class);

    ZjtJmsfzxxbDTO convert(ZjtJmsfzxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtJmsfzxxbDO convertToDO(ZjtJmsfzxxbDTO dto);

    ZjtJmsfzxxbDTO convertToDTO(ZjtJmsfzxxbPageReq req);

    ZjtJmsfzxxbDTO convertToDTO(ZjtJmsfzxxbCreateReq req);

    ZjtJmsfzxxbDTO convertToDTO(ZjtJmsfzxxbUpdateReq req);

    ZjtJmsfzxxbPageResp convertToPageResp(ZjtJmsfzxxbDTO dto);

    ZjtJmsfzxxbViewResp convertToViewResp(ZjtJmsfzxxbDTO dto);

    ZjtJmsfzxxbCreateResp convertToCreateResp(ZjtJmsfzxxbDTO dto);

}
