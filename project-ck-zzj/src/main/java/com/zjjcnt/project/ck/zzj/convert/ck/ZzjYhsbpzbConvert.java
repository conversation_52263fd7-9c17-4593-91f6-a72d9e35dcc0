package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYhsbpzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYhsbpzbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYhsbpzbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYhsbpzbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYhsbpzbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYhsbpzbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYhsbpzbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjYhsbpzbDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZzjYhsbpzbExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 自助用户设备配置表Convert
*
* <AUTHOR>
* @date 2024-07-19 14:06:40
*/
@Mapper
public interface ZzjYhsbpzbConvert {

    ZzjYhsbpzbConvert INSTANCE = Mappers.getMapper(ZzjYhsbpzbConvert.class);

    ZzjYhsbpzbDTO convert(ZzjYhsbpzbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZzjYhsbpzbDO convertToDO(ZzjYhsbpzbDTO dto);

    ZzjYhsbpzbDTO convertToDTO(ZzjYhsbpzbPageReq req);

    ZzjYhsbpzbDTO convertToDTO(ZzjYhsbpzbCreateReq req);

    List<ZzjYhsbpzbDTO> convertToDTO(List<ZzjYhsbpzbCreateReq> req);

    ZzjYhsbpzbDTO convertToDTO(ZzjYhsbpzbUpdateReq req);

    ZzjYhsbpzbPageResp convertToPageResp(ZzjYhsbpzbDTO dto);

    ZzjYhsbpzbViewResp convertToViewResp(ZzjYhsbpzbDTO dto);

    ZzjYhsbpzbCreateResp convertToCreateResp(ZzjYhsbpzbDTO dto);

    ZzjYhsbpzbExp convertToExp(ZzjYhsbpzbDTO dto);

}
