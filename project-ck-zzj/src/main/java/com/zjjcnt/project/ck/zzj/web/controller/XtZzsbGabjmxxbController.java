package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.XtZzsbGabjmxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbGabjmxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbGabjmxxbViewResp;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbGabjmxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 前端控制器
*
* <AUTHOR>
* @date 2025-05-21 10:04:10
*/
@RequiredArgsConstructor
@Tag(name = "自助设备公安部加密信息表")
@RestController
@RequestMapping("/xtZzsbGabjmxxb")
public class XtZzsbGabjmxxbController extends AbstractCrudController<XtZzsbGabjmxxbDTO> {

    private final XtZzsbGabjmxxbService xtZzsbGabjmxxbService;

    @Override
    protected IBaseService getService() {
        return xtZzsbGabjmxxbService;
    }

//    @GetMapping("page")
//    @Operation(summary = "查询")
//    public CommonResult<PageResult<XtZzsbGabjmxxbPageResp>> page(XtZzsbGabjmxxbPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, XtZzsbGabjmxxbConvert.INSTANCE::convertToDTO, XtZzsbGabjmxxbConvert.INSTANCE::convertToPageResp);
//    }
//
//    @GetMapping("view")
//    @Operation(summary = "查看详情")
//    public CommonResult<XtZzsbGabjmxxbViewResp> view(Long id) {
//        return super.view(id, XtZzsbGabjmxxbConvert.INSTANCE::convertToViewResp);
//    }

//    @PostMapping("create")
//    @Operation(summary = "新增")
//    public CommonResult<XtZzsbGabjmxxbCreateResp> create(@RequestBody XtZzsbGabjmxxbCreateReq req) {
//        return super.create(req, XtZzsbGabjmxxbConvert.INSTANCE::convertToDTO, XtZzsbGabjmxxbConvert.INSTANCE::convertToCreateResp);
//    }

    @GetMapping("getConfig")
    @Operation(summary = "获取加密配置信息")
    public CommonResult<XtZzsbGabjmxxbViewResp> getConfig(Long sbid) {
        XtZzsbGabjmxxbDTO result = xtZzsbGabjmxxbService.getConfig(sbid);
        return CommonResult.success(XtZzsbGabjmxxbConvert.INSTANCE.convertToViewResp(result));
    }

//    @PostMapping("update")
//    @Operation(summary = "编辑")
//    public CommonResult<Boolean> update(@RequestBody XtZzsbGabjmxxbUpdateReq req) {
//        return super.update(req, XtZzsbGabjmxxbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除")
//    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
//        return super.delete(id);
//    }

}
