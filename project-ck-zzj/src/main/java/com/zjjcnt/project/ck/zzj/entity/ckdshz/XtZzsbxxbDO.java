package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 自助设备信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 11:19:21
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO
 */
@Crypto
@Data
@Table("xt_zzsbxxb")
public class XtZzsbxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -5445099923281780828L;

    /**
     * 设备id
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 +  SID_XT_ZZSBXXB.nextval FROM dual")
    private Long id;

    /**
     * 所属派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 派出所名称
     */
    @Column(value = "pcsmc")
    private String pcsmc;

    /**
     * ROP用户
     */
    @Column(value = "appkey")
    private String appkey;

    /**
     * 设备ip
     */
    @Column(value = "sbip")
    private String sbip;

    /**
     * 设备mac
     */
    @Column(value = "sbmac")
    private String sbmac;

    /**
     * 设备是否vpn网络,0否1是
     */
    @Column(value = "sbsfvpn")
    private String sbsfvpn;

    /**
     * 设备使用地址
     */
    @Column(value = "sbsydz")
    private String sbsydz;

    /**
     * 设备型号
     */
    @Column(value = "sbxh")
    private String sbxh;

    /**
     * 设备编号
     */
    @Column(value = "sbxhmc")
    private String sbxhmc;

    /**
     * 设备类型，系统参数1259
     */
    @Column(value = "sblx")
    private String sblx;

    /**
     * 设备指纹采集器标识号1
     */
    @Column(value = "sbzwcjqbh1")
    private String sbzwcjqbh1;

    /**
     * 设备指纹采集器标识号2
     */
    @Column(value = "sbzwcjqbh2")
    private String sbzwcjqbh2;

    /**
     * 秘钥，json字符串生成的MD5码
     */
    @Column(value = "license")
    private String license;

    /**
     * 有效期开始时间,YYYYMMDD
     */
    @Column(value = "yxqkssj")
    private String yxqkssj;

    /**
     * 有效期结束时间,YYYYMMDD
     */
    @Column(value = "yxqjssj")
    private String yxqjssj;

    /**
     * 启用标志,0不启用1启用
     */
    @Column(value = "qybz")
    private String qybz;

    /**
     * 登记时间,YYYYMMDDHH24MISS
     */
    @Column(value = "djsj")
    private String djsj;

    /**
     * 注销时间,YYYYMMDDHH24MISS
     */
    @Column(value = "zxsj")
    private String zxsj;

    /**
     * 注销原因
     */
    @Column(value = "zxyy")
    private String zxyy;

    /**
     * 是否校验,0否1是
     */
    @Column(value = "sfjy")
    private String sfjy;

    /**
     * 是否校验ip,0否1是
     */
    @Column(value = "sfjyip")
    private String sfjyip;

    /**
     * 是否校验license,0否1是
     */
    @Column(value = "sfjylicense")
    private String sfjylicense;

    /**
     * 是否校验有效时间,0否1是
     */
    @Column(value = "sfjyyxsj")
    private String sfjyyxsj;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 设备生产公司,系统参数1280
     */
    @Column(value = "sbscgs")
    private String sbscgs;

    /**
     * 设备销售公司,系统参数1280
     */
    @Column(value = "sbxsgs")
    private String sbxsgs;

    /**
     * 是否允许上传照片,0否1是
     */
    @Column(value = "sfyxsczp")
    private String sfyxsczp;

    /**
     * 设备使用地名称
     */
    @Column(value = "sbsydmc")
    private String sbsydmc;

    /**
     * 终端登记人公民身份号码
     */
    @Crypto
    @Column(value = "zddjrgmsfhm")
    private String zddjrgmsfhm;

    /**
     * 终端登记人姓名
     */
    @Crypto
    @Column(value = "zddjrxm")
    private String zddjrxm;

    /**
     * 终端登记人联系电话
     */
    @Crypto
    @Column(value = "zddjrlxdh")
    private String zddjrlxdh;

    /**
     * 浙里拍照设备点位编号
     */
    @Column(value = "zlpzsbdwbh")
    private String zlpzsbdwbh;

    /**
     * 设备使用地名称
     */
    @Column(value = "sbzcgs")
    private String sbzcgs;

    /**
     * 经度
     */
    @Column(value = "jd")
    private String jd;

    /**
     * 纬度
     */
    @Column(value = "wd")
    private String wd;

    /**
     * 设备使用点类别代码
     */
    @Column(value = "sbsydlbdm")
    private String sbsydlbdm;

    /**
     * 同步系统
     */
    @Column(value = "tbxt")
    private String tbxt;

    /**
     * 网点id
     */
    @Column(value = "wdid")
    private String wdid;

    /**
     * 单位机构代码
     */
    @Column(value = "dwjgdm")
    private String dwjgdm;

    /**
     * 单位机构名称
     */
    @Column(value = "dwjgmc")
    private String dwjgmc;

    /**
     * 窗口点位所属单位
     */
    @Column(value = "ckdwssdw")
    private String ckdwssdw;

    /**
     * 同步自助设备国泰系统
     */
    @Column(value = "tsbzb")
    private String tsbzb;

    /**
     * 常口用户登录名
     */
    @Column(value = "ckyhdlm")
    private String ckyhdlm;

    /**
     * 常口操作员id
     */
    @Column(value = "ckczyid")
    private Long ckczyid;

    /**
     * 旧的设备点位编号
     */
    @Column(value = "sbdwbhold")
    private String sbdwbhold;

    /**
     * 新的设备点位编号
     */
    @Column(value = "sbdwbhnew")
    private String sbdwbhnew;

    /**
     * 详细地址
     */
    @Column(value = "xxdz")
    private String xxdz;

    /**
     * 是否浙里拍照设备，0否1是
     */
    @Column(value = "sfzlpzsb")
    private String sfzlpzsb;

    /**
     * 是否自动延续设备有效期，0否1是
     */
    @Column(value = "sfzdxq")
    private String sfzdxq;

    /**
     * 长三角-采集终端编码
     */
    @Column(value = "csjcjzdbm")
    private String csjcjzdbm;

    /**
     * 设备硬件码
     */
    @Column(value = "sbyjm")
    private String sbyjm;

    /**
     * 是否省外办证设备
     */
    @Column(value = "sfswbzsb")
    private String sfswbzsb;

    /**
     * 设备名称
     */
    @Column(value = "sbmc")
    private String sbmc;

    /**
     * 离线设备ID
     */
    @Column(value = "lxsbid")
    private String lxsbid;

    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
