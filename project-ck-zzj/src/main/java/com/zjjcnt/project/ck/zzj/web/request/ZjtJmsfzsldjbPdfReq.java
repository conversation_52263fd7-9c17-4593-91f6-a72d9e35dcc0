package com.zjjcnt.project.ck.zzj.web.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "繳款信息生成二维码入参")
public class ZjtJmsfzsldjbPdfReq {

    @Schema(description = "公民身份号码")
    private String gmsfhm;
    @Schema(description = "姓名")
    private String xm;
    @Schema(description = "出生日期")
    private String csrq;
    @Schema(description = "性别")
    private String xb;
    @Schema(description = "民族")
    private String mz;
    @Schema(description = "指位一")
    private String zwy;
    @Schema(description = "指位二")
    private String zwe;
    @Schema(description = "指纹注册信息")
    private String zwzcxx;
    @Schema(description = "人像zp")
    private String base64zp;
    @Schema(description = "常住户口所在地住址")
    private String ckhkszdzz;
    @Schema(description = "现居住地住址")
    private String xjzdzz;
    @Schema(description = "有效期限")
    private String yxqx;
    @Schema(description = "签发机关")
    private String qfjg;
    @Schema(description = "申领原因")
    private String slyy;
    @Schema(description = "受理号")
    private String slh;
    @Schema(description = "申请人姓名")
    private String sqrxm;
    @Schema(description = "申请人电话")
    private String sqrdh;
    @Schema(description = "领证方式")
    private String lzfs;
    @Schema(description = "收件人姓名")
    private String sjrxm;
    @Schema(description = "收件人电话")
    private String sjrdh;
    @Schema(description = "收件人地址")
    private String sjrdz;
    @Schema(description = "紧急联系人姓名")
    private String jjlxrxm;
    @Schema(description = "紧急联系人电话")
    private String jjlxrdh;
    @Schema(description = "年")
    private String year;
    @Schema(description = "月")
    private String month;
    @Schema(description = "日")
    private String day;
    @Schema(description = "签名")
    private String base64qm;
    @Schema(description = "受理单位名称")
    private String sldwmc;
    @Schema(description = "受理时间")
    private String slrq;
    @Schema(description = "受理时间")
    private String slsj;
    @Schema(description = "联系电话")
    private String lxdh;
    @Schema(description = "承办人")
    private String cbr;
    @Schema(description = "制证类型")
    private String zzlx;
}
