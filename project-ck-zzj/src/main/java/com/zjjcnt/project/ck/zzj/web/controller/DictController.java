package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.dict.Dictionary;
import com.zjjcnt.common.core.dict.DictionaryNames;
import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.project.ck.zzj.constant.CkZzjDictTypeConstants;
import com.zjjcnt.project.ck.zzj.web.response.DictRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 字典 Controller
 *
 * <AUTHOR>
 * @date 2024-05-13 16:42:00
 */
@Slf4j
@Tag(name = "字典")
@RestController
@RequestMapping("/dict")
public class DictController {

    private final Map<String, String> DICT_TYPE_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_DWXXB, DictionaryNames.DM_SJGSDW);
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_XZQHB, DictionaryNames.DM_XZQH);
//        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_YZBMSTR, CkZzjDictTypeConstants.DM_YZBM);
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_MZSTR, CkZzjDictTypeConstants.DM_MZ);
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_SLYYSTR, CkZzjDictTypeConstants.DM_SLYY);
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_XBSTR, CkZzjDictTypeConstants.DM_XB);
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_ZWXXSTR, CkZzjDictTypeConstants.DM_ZWXX);
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_ZWCJJGSTR, CkZzjDictTypeConstants.DM_ZWCJJG);
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_EDZZZLXSTR, CkZzjDictTypeConstants.DM_EDZZZLX);
        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_EDZLZFSSTR, CkZzjDictTypeConstants.DM_EDZLZFS);
//        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_EDZSFLXSTR, CkZzjDictTypeConstants.DM_EDZSFLX);
//        DICT_TYPE_MAP.put(CkZzjDictTypeConstants.DM_QXDYQFJGSTR, CkZzjDictTypeConstants.DM_QXDYQFJG);
    }


    @GetMapping("/value")
    @Operation(summary = "获取代码值 N0010")
    public CommonResult<DictRes> getValue(@RequestParam(value = "type") String type, @RequestParam(value = "key") String key) {
        String realType = DICT_TYPE_MAP.getOrDefault(type, type);
        String value = Dictionary.getValue(realType, key);
        return CommonResult.success(new DictRes(value));
    }


}
