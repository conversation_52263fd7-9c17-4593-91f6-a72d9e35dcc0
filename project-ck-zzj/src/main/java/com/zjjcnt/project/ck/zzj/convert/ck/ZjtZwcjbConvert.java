package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtZwcjbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 指纹采集表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtZwcjbConvert {

    ZjtZwcjbConvert INSTANCE = Mappers.getMapper(ZjtZwcjbConvert.class);

    ZjtZwcjbDTO convert(ZjtZwcjbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtZwcjbDO convertToDO(ZjtZwcjbDTO dto);

    ZjtZwcjbDTO convertToDTO(ZjtZwcjbPageReq req);

    ZjtZwcjbDTO convertToDTO(ZjtZwcjbCreateReq req);

    ZjtZwcjbDTO convertToDTO(ZjtZwcjbUpdateReq req);

    ZjtZwcjbPageResp convertToPageResp(ZjtZwcjbDTO dto);

    ZjtZwcjbViewResp convertToViewResp(ZjtZwcjbDTO dto);

    ZjtZwcjbCreateResp convertToCreateResp(ZjtZwcjbDTO dto);

    @Mapping(ignore = true, target = "zwtxid")
    ZjtZwcjbDTO convertToDTO(ZjtZwcjlsbDTO dto);

}
