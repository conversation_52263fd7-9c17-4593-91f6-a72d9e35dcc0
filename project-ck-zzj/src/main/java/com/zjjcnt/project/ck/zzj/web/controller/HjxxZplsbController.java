package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxZplsbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxZplsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZplsbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZplsbSaveReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZplsbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZplsbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxZplsbViewResp;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxZplsbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
* 照片临时表前端控制器
*
* <AUTHOR>
* @date 2024-05-07 11:35:02
*/

@Tag(name = "照片临时表")
@RestController
@RequestMapping("/hjxxZplsb")
public class HjxxZplsbController extends AbstractCrudController<HjxxZplsbDTO> {

    @Autowired
    private HjxxZplsbService hjxxZplsbService;

    @Override
    protected IBaseService<HjxxZplsbDTO> getService() {
        return hjxxZplsbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询照片临时表 F1032")
    public CommonResult<PageResult<HjxxZplsbPageResp>> page(HjxxZplsbPageReq req, PageParam pageParam) {
        pageParam.setOrderBy("DESC:bcsj");
        return super.page(req, pageParam, HjxxZplsbConvert.INSTANCE::convertToDTO, HjxxZplsbConvert.INSTANCE::convertToPageResp);
    }

    @PostMapping("save")
    @Operation(summary = "保存照片临时表 F11601")
    public CommonResult<HjxxZplsbCreateResp> create(@Validated @RequestBody HjxxZplsbSaveReq req) {
        HjxxZplsbDTO hjxxZplsbDTO = hjxxZplsbService.processSaveZplsb(req);
        return CommonResult.success(HjxxZplsbConvert.INSTANCE.convertToCreateResp(hjxxZplsbDTO));
    }

    @GetMapping("lastValidPhoto")
    @Operation(summary = "查询最近的有效照片")
    public CommonResult<HjxxZplsbViewResp> getLastValidPhoto(@RequestParam(value = "gmsfhm") String gmsfhm) {
        HjxxZplsbDTO hjxxZplsbDTO = hjxxZplsbService.getLastValidPhoto(gmsfhm);
        return CommonResult.success(HjxxZplsbConvert.INSTANCE.convertToViewResp(hjxxZplsbDTO));
    }

//
//    @GetMapping("view")
//    @Operation(summary = "查看照片临时表详情")
//    public CommonResult<HjxxZplsbViewResp> view(Long id) {
//        return super.view(id, HjxxZplsbConvert.INSTANCE::convertToViewResp);
//    }

//    @PostMapping("create")
//    @Operation(summary = "新增照片临时表")
//    public CommonResult<HjxxZplsbCreateResp> create(@RequestBody HjxxZplsbCreateReq req) {
//        return super.create(req, HjxxZplsbConvert.INSTANCE::convertToDTO, HjxxZplsbConvert.INSTANCE::convertToCreateResp);
//    }

//    @PostMapping("update")
//    @Operation(summary = "编辑照片临时表")
//    public CommonResult<Boolean> update(@RequestBody HjxxZplsbUpdateReq req) {
//        return super.update(req, HjxxZplsbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除照片临时表")
//    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
//        return super.delete(id);
//    }

}
