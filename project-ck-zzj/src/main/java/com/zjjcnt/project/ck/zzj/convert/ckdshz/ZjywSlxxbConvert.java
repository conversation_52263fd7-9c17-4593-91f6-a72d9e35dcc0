package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.ZjywSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.ZjywSlxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.ZjywSlxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.ZjywSlxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.ZjywSlxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.ZjywSlxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.ZjywSlxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.ZjywSlxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 受理信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface ZjywSlxxbConvert {

    ZjywSlxxbConvert INSTANCE = Mappers.getMapper(ZjywSlxxbConvert.class);

    ZjywSlxxbDTO convert(ZjywSlxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjywSlxxbDO convertToDO(ZjywSlxxbDTO dto);

    ZjywSlxxbDTO convertToDTO(ZjywSlxxbPageReq req);

    ZjywSlxxbDTO convertToDTO(ZjywSlxxbCreateReq req);

    ZjywSlxxbDTO convertToDTO(ZjywSlxxbUpdateReq req);

    ZjywSlxxbPageResp convertToPageResp(ZjywSlxxbDTO dto);

    ZjywSlxxbViewResp convertToViewResp(ZjywSlxxbDTO dto);

    ZjywSlxxbCreateResp convertToCreateResp(ZjywSlxxbDTO dto);

}
