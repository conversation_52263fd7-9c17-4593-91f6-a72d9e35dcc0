package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtFyzflsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtFyzflsbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtFyzflsbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtFyzflsbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtFyzflsbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtFyzflsbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtFyzflsbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtFyzflsbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 费用支付流水表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtFyzflsbConvert {

    ZjtFyzflsbConvert INSTANCE = Mappers.getMapper(ZjtFyzflsbConvert.class);

    ZjtFyzflsbDTO convert(ZjtFyzflsbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtFyzflsbDO convertToDO(ZjtFyzflsbDTO dto);

    ZjtFyzflsbDTO convertToDTO(ZjtFyzflsbPageReq req);

    ZjtFyzflsbDTO convertToDTO(ZjtFyzflsbCreateReq req);

    ZjtFyzflsbDTO convertToDTO(ZjtFyzflsbUpdateReq req);

    ZjtFyzflsbDTO convertToDTO(ZjtSlxxbDTO slxxbDTO);

    ZjtFyzflsbDTO convertToDTO(ZjtLssfzSlxxbDTO lssfzSlxxbDTO);

    ZjtFyzflsbPageResp convertToPageResp(ZjtFyzflsbDTO dto);

    ZjtFyzflsbViewResp convertToViewResp(ZjtFyzflsbDTO dto);

    ZjtFyzflsbCreateResp convertToCreateResp(ZjtFyzflsbDTO dto);

}
