package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 自助设备配置表DO
 *
 * <AUTHOR>
 * @date 2024-07-19 14:06:40
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZzjSbpzbDTO
 */
@Data
@Table("zzj_sbpzb")
public class ZzjSbpzbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -3494308540579575220L;

    /**
     * id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 配置编码
     */
    @Column(value = "pzbm")
    private String pzbm;

    /**
     * 配置描述
     */
    @Column(value = "pzms")
    private String pzms;

    /**
     * 默认值
     */
    @Column(value = "mrz")
    private String mrz;

    /**
     * 有效标志
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 修改时间
     */
    @Column(value = "xgsj")
    private String xgsj;


    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
