package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 户信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxHxxbDTO
 */
@Data
@Table("hjxx_hxxb")
public class HjxxHxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = 5604836436060747529L;

    /**
     * 户号内部ID
     */
    @Id(keyType = KeyType.Auto)
    private Long hhnbid;

    /**
     * 户号ID
     */
    @Column(value = "hhid")
    private Long hhid;

    /**
     * 门（楼）牌内部ID
     */
    @Column(value = "mlpnbid")
    private Long mlpnbid;

    /**
     * 户号
     */
    @Column(value = "hh")
    private String hh;

    /**
     * 户类型
     */
    @Column(value = "hlx")
    private String hlx;

    /**
     * 建户类别
     */
    @Column(value = "jhlb")
    private String jhlb;

    /**
     * 撤户类别
     */
    @Column(value = "chlb")
    private String chlb;

    /**
     * 建户时间
     */
    @Column(value = "jhsj")
    private String jhsj;

    /**
     * 撤户时间
     */
    @Column(value = "chsj")
    private String chsj;

    /**
     * 创建户籍业务ID
     */
    @Column(value = "cjhjywid")
    private Long cjhjywid;

    /**
     * 撤除户籍业务ID
     */
    @Column(value = "cchjywid")
    private Long cchjywid;

    /**
     * 变动范围
     */
    @Column(value = "bdfw")
    private String bdfw;

    /**
     * 变动原因
     */
    @Column(value = "bdyy")
    private String bdyy;

    /**
     * 户号状态
     */
    @Column(value = "hhzt")
    private String hhzt;

    /**
     * 离线DBID
     */
    @Column(value = "lxdbid")
    private Long lxdbid;

    /**
     * 记录标志
     */
    @Column(value = "jlbz")
    private String jlbz;

    /**
     * 起用时间
     */
    @Column(value = "qysj")
    private String qysj;

    /**
     * 结束时间
     */
    @Column(value = "jssj")
    private String jssj;

    /**
     * 冲销标志
     */
    @Column(value = "cxbz")
    private String cxbz;

    /**
     * 户籍地地址编码
     */
    @Column(value = "hdzdzbm")
    private String hdzdzbm;

    /**
     * 户籍地省市县（区）
     */
    @Column(value = "hdzssxq")
    private String hdzssxq;

    /**
     * 户籍地详细地址
     */
    @Column(value = "hdzxxdz")
    private String hdzxxdz;

    /**
     * 更新时间
     */
    @Column(value = "gxsj")
    private String gxsj;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sjgsdwdm")
    private String sjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sjgsdwmc")
    private String sjgsdwmc;


    @Override
    public Long getId() {
        return this.hhnbid;
    }

    @Override
    public void setId(Long id) {
        this.hhnbid = id;
    }
}
