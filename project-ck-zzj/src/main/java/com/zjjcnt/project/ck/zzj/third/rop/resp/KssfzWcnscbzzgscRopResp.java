package com.zjjcnt.project.ck.zzj.third.rop.resp;

import lombok.Data;

/**
 * 办证资格审查resp
 *
 * <AUTHOR>
 * @date 2025年4月9日 14点28分
 */
@Data
public class KssfzWcnscbzzgscRopResp {

    /**
     * 密文
     */
    private String mw;

    /**
     * 信封
     */
    private String xf;

//    /**
//     * 民族附加项代码
//     */
//    private String mzfjxdm;
//
//    /**
//     * 公民身份号码
//     */
//    private String gmsfhm;
//
//    /**
//     * 中文姓名
//     */
//    private String xm;
//
//    /**
//     * 性别
//     */
//    private String xbdm;
//
//    /**
//     * 民族
//     */
//    private String mzdm;
//
//    /**
//     * 出生日期
//     */
//    private String csrq;
//
//    /**
//     * 相片
//     */
//    private byte[] xp;
//
//    /**
//     * 户籍地址_省市县（区）
//     */
//    private String hjdzSsxqdm;
//
//    /**
//     * 户籍地址_区划内详细地址
//     */
//    private String hjdzQhnxxdz;
//
//    /**
//     * 户籍地址_数据归属单位代码
//     */
//    private String hjdzSjgsdwdm;
//
//    /**
//     * 户籍地址_数据归属单位名称
//     */
//    private String hjdzSjgsdwmc;
//
//    /**
//     * 监护人一公民身份号码
//     */
//    private String jhryGmsfhm;
//
//    /**
//     * 监护人一姓名
//     */
//    private String jhryXm;
//
//    /**
//     * 监护人一监护关系
//     */
//    private String jhryJhgxdm;
//
//    /**
//     * 监护人一联系电话
//     */
//    private String jhryLxdh;
//
//    /**
//     * 监护人二公民身份号码
//     */
//    private String jhreGmsfhm;
//
//    /**
//     * 监护人二姓名
//     */
//    private String jhreXm;
//
//    /**
//     * 监护人二监护关系
//     */
//    private String jhreJhgxdm;
//
//    /**
//     * 监护人二联系电话
//     */
//    private String jhreLxdh;
//
//    /**
//     * 持有居民身份证情况_公民身份号码
//     */
//    private String cyjmsfzqkGmsfhm;
//
//    /**
//     * 持有居民身份证情况_姓名
//     */
//    private String cyjmsfzqkXm;
//
//    /**
//     * 持有居民身份证情况_性别
//     */
//    private String cyjmsfzqkXbdm;
//
//    /**
//     * 持有居民身份证情况_民族
//     */
//    private String cyjmsfzqkMzdm;
//
//    /**
//     * 持有居民身份证情况_出生日期
//     */
//    private String cyjmsfzqkCsrq;
//
//    /**
//     * 签发公安机关名称
//     */
//    private String cyjmsfzqkQfjgGajgmc;
//
//    /**
//     * 有效期起始日期
//     */
//    private String cyjmsfzqkYxqqsrq;
//
//    /**
//     * 有效期截止日期
//     */
//    private String cyjmsfzqkYxqjzrq;
//
//    /**
//     * 持有居民身份证情况_视读相片
//     */
//    private byte[] cyjmsfzqkSdxp;
//
//    /**
//     * 持有居民身份证情况_指纹采集结果
//     */
//    private String cyjmsfzqkZwcjjgdm;
//
//    /**
//     * 持有居民身份证情况_指纹一_注册结果
//     */
//    private String cyjmsfzqkZwyZwzcjgdm;
//
//    /**
//     * 持有居民身份证情况_指纹一_指位
//     */
//    private String cyjmsfzqkZwyZwdm;
//
//    /**
//     * 持有居民身份证情况_指纹一_图像数据
//     */
//    private byte[] cyjmsfzqkZwyZwtxsj;
//
//    /**
//     * 持有居民身份证情况_指纹一_图像质量值
//     */
//    private Double cyjmsfzqkZwyZwtxzlz;
//
//    /**
//     * 持有居民身份证情况_指纹一_特征数据
//     */
//    private byte[] cyjmsfzqkZwyZwtzsj;
//
//    /**
//     * 持有居民身份证情况_指纹二_注册结果
//     */
//    private String cyjmsfzqkZweZwzcjgdm;
//
//    /**
//     * 持有居民身份证情况_指纹二_指位
//     */
//    private String cyjmsfzqkZweZwdm;
//
//    /**
//     * 持有居民身份证情况_指纹二_图像数据
//     */
//    private byte[] cyjmsfzqkZweZwtxsj;
//
//    /**
//     * 持有居民身份证情况_指纹二_图像质量值
//     */
//    private Double cyjmsfzqkZweZwtxzlz;
//
//    /**
//     * 持有居民身份证情况_指纹二_特征数据
//     */
//    private byte[] cyjmsfzqkZweZwtzsj;
//
//    /**
//     * 持有居民身份证情况_户籍地址_区划内详细地址
//     */
//    private String cyjmsfzqkHjdzQhnxxdz;
//
//    /**
//     * 持有居民身份证情况_申领原因
//     */
//    private String cyjmsfzqkJmsfzslyydm;
//
//    /**
//     * 持有居民身份证情况_制证类型
//     */
//    private String cyjmsfzqkJmsfzzzlxdm;
//
//    /**
//     * 持有居民身份证情况_领证方式
//     */
//    private String cyjmsfzqkJmsfzlzfsdm;
//
//    /**
//     * 持有居民身份证情况_联系电话
//     */
//    private String cyjmsfzqkLxdh;
//
//    /**
//     * 持有居民身份证情况_受理单位_公安机关机构代码
//     */
//    private String cyjmsfzqkSldwGajgjgdm;
//
//    /**
//     * 持有居民身份证情况_受理单位_公安机关名称
//     */
//    private String cyjmsfzqkSldwGajgmc;
//
//    /**
//     * 持有居民身份证情况_受理人_姓名
//     */
//    private String cyjmsfzqkSlrXm;
//
//    /**
//     * 持有居民身份证情况_受理时间
//     */
//    private String cyjmsfzqkSlsj;
//
//    /**
//     * 持有居民身份证情况_数据归属单位代码
//     */
//    private String cyjmsfzqkSjgsdwdm;
//
//    /**
//     * 持有居民身份证情况_数据归属单位名称
//     */
//    private String cyjmsfzqkSjgsdwmc;
//
//    /**
//     * 居民身份证重号_判断标识
//     */
//    private String jmsfzchPdbz;
//
//    /**
//     * 居民身份证挂失_判断标识
//     */
//    private String jmsfzgsPdbz;
//
//    /**
//     * 居民身份证捡拾_判断标识
//     */
//    private String jmsfzjsPdbz;
//
//    /**
//     * 居民身份证捡拾单位_公安机关名称
//     */
//    private String jmsfzjsdwGajgmc;
//
//    /**
//     * 居民身份证捡拾单位_联系电话
//     */
//    private String jmsfzjsdwLxdh;
//
//    /**
//     * 居民身份证受理中_判断标识
//     */
//    private String jmsfzslzPdbz;
//
//    /**
//     * 居民身份证受理单位_公安机关机构代码
//     */
//    private String jmsfzsldwGajgjgdm;
//
//    /**
//     * 居民身份证受理单位_公安机关名称
//     */
//    private String jmsfzsldwGajgmc;
//
//    /**
//     * 居民身份证受理中_受理时间
//     */
//    private String jmsfzslzSlsj;
//
//    /**
//     * 居民身份证状态_预留标识一
//     */
//    private String jmsfzztYlbsy;
//
//    /**
//     * 居民身份证状态_预留标识二
//     */
//    private String jmsfzztYlbse;
}
