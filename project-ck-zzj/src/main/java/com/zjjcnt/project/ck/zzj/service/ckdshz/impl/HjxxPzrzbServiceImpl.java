package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxPzrzbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxPzrzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxPzrzbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.HjxxPzrzbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxPzrzbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 拍照日志信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@Service
public class HjxxPzrzbServiceImpl extends AbstractBaseServiceImpl<HjxxPzrzbMapper, HjxxPzrzbDO, HjxxPzrzbDTO> implements HjxxPzrzbService {

    HjxxPzrzbConvert convert = HjxxPzrzbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(HjxxPzrzbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(HjxxPzrzbDO::getZplsid, dto.getZplsid(), Objects.nonNull(dto.getZplsid()));
        query.eq(HjxxPzrzbDO::getNbslid, dto.getNbslid(), Objects.nonNull(dto.getNbslid()));
        query.eq(HjxxPzrzbDO::getYhid, dto.getYhid(), Objects.nonNull(dto.getYhid()));
        query.eq(HjxxPzrzbDO::getIpdz, dto.getIpdz(), Objects.nonNull(dto.getIpdz()));
        query.eq(HjxxPzrzbDO::getYhdlm, dto.getYhdlm(), Objects.nonNull(dto.getYhdlm()));
        query.likeLeft(HjxxPzrzbDO::getYhdw, ColumnUtils.removeZeroSuffix(dto.getYhdw()), StringUtils.isNotEmpty(dto.getYhdw()));
        query.eq(HjxxPzrzbDO::getYhxm, ColumnUtils.encryptColumn(dto.getYhxm()), StringUtils.isNotEmpty(dto.getYhxm()));
        query.ge(HjxxPzrzbDO::getBcsj, dto.getBcsjStart(), StringUtils.isNotEmpty(dto.getBcsjStart()));
        query.le(HjxxPzrzbDO::getBcsj, dto.getBcsjEnd(), StringUtils.isNotEmpty(dto.getBcsjEnd()));
        query.ge(HjxxPzrzbDO::getRksj, dto.getRksjStart(), StringUtils.isNotEmpty(dto.getRksjStart()));
        query.le(HjxxPzrzbDO::getRksj, dto.getRksjEnd(), StringUtils.isNotEmpty(dto.getRksjEnd()));
        query.eq(HjxxPzrzbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(HjxxPzrzbDO::getSlh, dto.getSlh(), Objects.nonNull(dto.getSlh()));
        query.eq(HjxxPzrzbDO::getPzxlh, dto.getPzxlh(), Objects.nonNull(dto.getPzxlh()));
        query.eq(HjxxPzrzbDO::getZpcjlx, dto.getZpcjlx(), Objects.nonNull(dto.getZpcjlx()));
        query.eq(HjxxPzrzbDO::getZpsbbsh, dto.getZpsbbsh(), Objects.nonNull(dto.getZpsbbsh()));
        query.eq(HjxxPzrzbDO::getZpsbppxhdm, dto.getZpsbppxhdm(), Objects.nonNull(dto.getZpsbppxhdm()));
        query.eq(HjxxPzrzbDO::getZpsbppxh, dto.getZpsbppxh(), Objects.nonNull(dto.getZpsbppxh()));
        query.eq(HjxxPzrzbDO::getZpytfileurl, dto.getZpytfileurl(), Objects.nonNull(dto.getZpytfileurl()));
        query.eq(HjxxPzrzbDO::getBz1, dto.getBz1(), Objects.nonNull(dto.getBz1()));
        query.eq(HjxxPzrzbDO::getBz2, dto.getBz2(), Objects.nonNull(dto.getBz2()));
        query.eq(HjxxPzrzbDO::getZpytbid, dto.getZpytbid(), Objects.nonNull(dto.getZpytbid()));
        query.eq(HjxxPzrzbDO::getSbid, dto.getSbid(), Objects.nonNull(dto.getSbid()));
        query.eq(HjxxPzrzbDO::getZlpzsbdwbh, dto.getZlpzsbdwbh(), Objects.nonNull(dto.getZlpzsbdwbh()));
        return query;
    }

    @Override
    public HjxxPzrzbDTO convertToDTO(HjxxPzrzbDO hjxxPzrzbDO) {
        return convert.convert(hjxxPzrzbDO);
    }

    @Override
    public HjxxPzrzbDO convertToDO(HjxxPzrzbDTO hjxxPzrzbDTO) {
        return convert.convertToDO(hjxxPzrzbDTO);
    }
}
