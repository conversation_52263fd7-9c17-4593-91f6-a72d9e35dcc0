package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.XtZzsbxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.req.XtZzsbxxbCheckReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbxxbSaveReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbxxbCheckResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbxxbViewResp;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.manager.ZlpzManager;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 自助设备信息表前端控制器
 *
 * <AUTHOR>
 * @date 2024-05-07 11:19:21
 */
@RequiredArgsConstructor
@Tag(name = "自助设备信息表")
@RestController
@RequestMapping("/xtZzsbxxb")
public class XtZzsbxxbController extends AbstractCrudController<XtZzsbxxbDTO> {

    private final XtZzsbxxbService xtZzsbxxbService;
    private final ZlpzManager zlpzManager;

    @Override
    protected IBaseService<XtZzsbxxbDTO> getService() {
        return xtZzsbxxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询自助设备信息表")
    public CommonResult<PageResult<XtZzsbxxbPageResp>> page(XtZzsbxxbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, XtZzsbxxbConvert.INSTANCE::convertToDTO, XtZzsbxxbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看自助设备信息表详情")
    public CommonResult<XtZzsbxxbViewResp> view(@RequestParam(value = "id") Long id) {
        return super.view(id, XtZzsbxxbConvert.INSTANCE::convertToViewResp);
    }

    @GetMapping("viewByMac")
    @Operation(summary = "根据mac查看自助设备信息表详情")
    public CommonResult<XtZzsbxxbViewResp> viewByMac(@RequestParam(value = "sbmac") String sbmac) {

        XtZzsbxxbDTO query = new XtZzsbxxbDTO();
        query.setSbmac(sbmac);
        query.setQybz(Constants.YES);
        XtZzsbxxbDTO xtZzsbxxbDTO = xtZzsbxxbService.find(query);

        return CommonResult.success(XtZzsbxxbConvert.INSTANCE.convertToViewResp(xtZzsbxxbDTO));
    }

    @PostMapping("create")
    @Operation(summary = "新增自助设备信息表")
    public CommonResult<XtZzsbxxbCreateResp> create(@Validated @RequestBody XtZzsbxxbCreateReq req) {
        XtZzsbxxbDTO result;

        XtZzsbxxbDTO xtZzsbxxbDTO = XtZzsbxxbConvert.INSTANCE.convertToDTO(req);
        xtZzsbxxbDTO.setTbxt(StringUtils.defaultIfEmpty(xtZzsbxxbDTO.getTbxt(), CkZzjConstants.TBXT_CSJ));

        if (Constants.NO.equals(req.getSfzlpzsb())) {
            //直接修改设备
            result = xtZzsbxxbService.insertZzsb(xtZzsbxxbDTO);
        } else {
            result = zlpzManager.yztyZdsbxxSaveAndUpload(xtZzsbxxbDTO, CkZzjConstants.CJZD_CZLX_XZ);
        }
        return CommonResult.success(XtZzsbxxbConvert.INSTANCE.convertToCreateResp(result));
    }

    @PostMapping("update")
    @Operation(summary = "编辑自助设备信息表")
    public CommonResult<Boolean> update(@Validated @RequestBody XtZzsbxxbUpdateReq req) {
        XtZzsbxxbDTO xtZzsbxxbDTO = XtZzsbxxbConvert.INSTANCE.convertToDTO(req);
        xtZzsbxxbDTO.setTbxt(StringUtils.defaultIfEmpty(xtZzsbxxbDTO.getTbxt(), CkZzjConstants.TBXT_CSJ));
        if (Constants.NO.equals(req.getSfzlpzsb())) {
            //直接修改设备
            xtZzsbxxbService.updateZzsb(xtZzsbxxbDTO);
        } else {
            zlpzManager.yztyZdsbxxSaveAndUpload(xtZzsbxxbDTO, CkZzjConstants.CJZD_CZLX_XG);
        }

        return CommonResult.success(true);
    }

    @PostMapping("save")
    @Operation(summary = "保存自助设备信息表")
    public CommonResult<XtZzsbxxbCreateResp> save(@Validated @RequestBody XtZzsbxxbSaveReq req) {
        XtZzsbxxbDTO xtZzsbxxbDTO = XtZzsbxxbConvert.INSTANCE.convertToDTO(req);
        xtZzsbxxbDTO.setTbxt(CkZzjConstants.TBXT_CSJ);
        XtZzsbxxbDTO result = zlpzManager.yztyZdsbxxSaveAndUpload(xtZzsbxxbDTO);

        return CommonResult.success(XtZzsbxxbConvert.INSTANCE.convertToCreateResp(result));
    }

    @PostMapping("check")
    @Operation(summary = "校验自助设备信息表")
    public CommonResult<XtZzsbxxbCheckResp> check(@Validated @RequestBody XtZzsbxxbCheckReq req) {

        XtZzsbxxbDTO xtZzsbxxbDTO = xtZzsbxxbService.findEnabledByAppkeyAndSbmac(CkZzjConstants.APP_KEY_CZRKZDYH,
                req.getSbmac());

        boolean success = false;
        boolean deviceExit = true;
        String message = "";
        Long sbid = null;

        if (Objects.isNull(xtZzsbxxbDTO)) {
            deviceExit = false;
            message = CkZzjErrorCode.DEVICE_NOT_REGISTER.getMessage();
        } else if (!StringUtils.equals(xtZzsbxxbDTO.getCkyhdlm(), SecurityUtils.getUsername())) {
            message = CkZzjErrorCode.DEVICE_ACCOUNT_NOT_MATCH.getMessage();
        } else if (Constants.NO.equals(xtZzsbxxbDTO.getSbsfvpn()) && Constants.YES.equals(xtZzsbxxbDTO.getSfjyip())
                && !StringUtils.equals(xtZzsbxxbDTO.getSbip(), SecurityUtils.getRemoteAddress())) {
            message = String.format("注册设备信息的ip和实际的机器ip不一致，用户名：%s, 注册设备的ip：%s, 实际的机器ip：%s",
                    SecurityUtils.getUsername(), xtZzsbxxbDTO.getSbip(), SecurityUtils.getRemoteAddress());
        } else {
            success = true;
            sbid = xtZzsbxxbDTO.getId();
        }
        XtZzsbxxbCheckResp xtZzsbxxbCheckResp = new XtZzsbxxbCheckResp();
        xtZzsbxxbCheckResp.setSuccess(success);
        xtZzsbxxbCheckResp.setDeviceExit(deviceExit);
        xtZzsbxxbCheckResp.setMessage(message);
        xtZzsbxxbCheckResp.setSbid(sbid);
        return CommonResult.success(xtZzsbxxbCheckResp);
    }

//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除自助设备信息表")
//    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
//        return super.delete(id);
//    }
//
//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(XtZzsbxxbExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出自助设备信息表")
//    public void export(XtZzsbxxbPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "自助设备信息表" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, XtZzsbxxbExp.class,
//                    XtZzsbxxbConvert.INSTANCE::convertToDTO, XtZzsbxxbConvert.INSTANCE::convertToExp, response);
//    }

}
