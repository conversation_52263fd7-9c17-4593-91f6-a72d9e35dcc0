package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.dict.Dictionary;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.constant.CkConstants;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import com.zjjcnt.project.ck.sysadmin.service.XtXtkzcsbService;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.constant.CkZzjDictTypeConstants;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtFyzflsbConvert;
import com.zjjcnt.project.ck.zzj.domain.CkFyzflsbJkdhGenerateInfo;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtCzzsdwdybDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtFyzflsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtFyzflsbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtFyzflsbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtCzzsdwdybService;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtFyzflsbService;
import com.zjjcnt.project.ck.sysadmin.service.FwtXtXtpzService;
import com.zjjcnt.project.ck.zzj.util.CkFyzflsbUtils;
import com.zjjcnt.project.ck.zzj.util.OtherFyzflsbUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 费用支付流水表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ZjtFyzflsbServiceImpl extends ExBaseServiceImpl<ZjtFyzflsbMapper, ZjtFyzflsbDO, ZjtFyzflsbDTO> implements ZjtFyzflsbService {

    ZjtFyzflsbConvert convert = ZjtFyzflsbConvert.INSTANCE;

    private final ZjtCzzsdwdybService zjtCzzsdwdybService;

    @Autowired
    private XtXtkzcsbService xtXtkzcsbService;

    @Autowired
    private FwtXtXtpzService xtXtpzService;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtFyzflsbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtFyzflsbDO::getSflsid, dto.getSflsid(), StringUtils.isNotEmpty(dto.getSflsid()));
        query.eq(ZjtFyzflsbDO::getYwbmc, dto.getYwbmc(), StringUtils.isNotEmpty(dto.getYwbmc()));
        query.eq(ZjtFyzflsbDO::getYwbbh, dto.getYwbbh(), StringUtils.isNotEmpty(dto.getYwbbh()));
        query.eq(ZjtFyzflsbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtFyzflsbDO::getRynbid, dto.getRynbid(), StringUtils.isNotEmpty(dto.getRynbid()));
        query.eq(ZjtFyzflsbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtFyzflsbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtFyzflsbDO::getSfje, dto.getSfje(), Objects.nonNull(dto.getSfje()));
        query.eq(ZjtFyzflsbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtFyzflsbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtFyzflsbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtFyzflsbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.likeLeft(ZjtFyzflsbDO::getCzsj, dto.getCzsj(), StringUtils.isNotEmpty(dto.getCzsj()));
        query.ge(ZjtFyzflsbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtFyzflsbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtFyzflsbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.eq(ZjtFyzflsbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(ZjtFyzflsbDO::getCzydwdm, dto.getCzydwdm(), StringUtils.isNotEmpty(dto.getCzydwdm()));
        query.eq(ZjtFyzflsbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.eq(ZjtFyzflsbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(ZjtFyzflsbDO::getSfsf, dto.getSfsf(), StringUtils.isNotEmpty(dto.getSfsf()));
        query.eq(ZjtFyzflsbDO::getSfywlx, dto.getSfywlx(), StringUtils.isNotEmpty(dto.getSfywlx()));
        query.eq(ZjtFyzflsbDO::getJkdh, dto.getJkdh(), StringUtils.isNotEmpty(dto.getJkdh()));
        query.eq(ZjtFyzflsbDO::getJkrsfhm, dto.getJkrsfhm(), StringUtils.isNotEmpty(dto.getJkrsfhm()));
        query.eq(ZjtFyzflsbDO::getJkrlxdh, ColumnUtils.encryptColumn(dto.getJkrlxdh()), StringUtils.isNotEmpty(dto.getJkrlxdh()));
        query.eq(ZjtFyzflsbDO::getSfywlxmc, ColumnUtils.encryptColumn(dto.getSfywlxmc()), StringUtils.isNotEmpty(dto.getSfywlxmc()));
        query.eq(ZjtFyzflsbDO::getHbjkdh, dto.getHbjkdh(), StringUtils.isNotEmpty(dto.getHbjkdh()));
        query.eq(ZjtFyzflsbDO::getSldfjsjgsdwdm, dto.getSldfjsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwdm()));
        query.eq(ZjtFyzflsbDO::getSldfjsjgsdwmc, dto.getSldfjsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwmc()));
        query.eq(ZjtFyzflsbDO::getZsdwdm, dto.getZsdwdm(), StringUtils.isNotEmpty(dto.getZsdwdm()));
        query.eq(ZjtFyzflsbDO::getZsdwmc, dto.getZsdwmc(), StringUtils.isNotEmpty(dto.getZsdwmc()));
        query.eq(ZjtFyzflsbDO::getXzqhdm, dto.getXzqhdm(), StringUtils.isNotEmpty(dto.getXzqhdm()));
        query.eq(ZjtFyzflsbDO::getZsxmbm, ColumnUtils.encryptColumn(dto.getZsxmbm()), StringUtils.isNotEmpty(dto.getZsxmbm()));
        query.eq(ZjtFyzflsbDO::getZsxmmc, ColumnUtils.encryptColumn(dto.getZsxmmc()), StringUtils.isNotEmpty(dto.getZsxmmc()));
        query.eq(ZjtFyzflsbDO::getSfdj, dto.getSfdj(), Objects.nonNull(dto.getSfdj()));
        query.eq(ZjtFyzflsbDO::getSfsl, dto.getSfsl(), Objects.nonNull(dto.getSfsl()));
        query.eq(ZjtFyzflsbDO::getTfryid, dto.getTfryid(), StringUtils.isNotEmpty(dto.getTfryid()));
        query.eq(ZjtFyzflsbDO::getTfryxm, ColumnUtils.encryptColumn(dto.getTfryxm()), StringUtils.isNotEmpty(dto.getTfryxm()));
        query.eq(ZjtFyzflsbDO::getTfrydwdm, dto.getTfrydwdm(), StringUtils.isNotEmpty(dto.getTfrydwdm()));
        query.eq(ZjtFyzflsbDO::getTfrydwmc, dto.getTfrydwmc(), StringUtils.isNotEmpty(dto.getTfrydwmc()));
        query.ge(ZjtFyzflsbDO::getTfsj, dto.getTfsjStart(), StringUtils.isNotEmpty(dto.getTfsjStart()));
        query.le(ZjtFyzflsbDO::getTfsj, dto.getTfsjEnd(), StringUtils.isNotEmpty(dto.getTfsjEnd()));
        query.eq(ZjtFyzflsbDO::getJkdlyqdbh, dto.getJkdlyqdbh(), StringUtils.isNotEmpty(dto.getJkdlyqdbh()));
        query.eq(ZjtFyzflsbDO::getFwdx, dto.getFwdx(), StringUtils.isNotEmpty(dto.getFwdx()));
        return query;
    }

    @Override
    public ZjtFyzflsbDTO convertToDTO(ZjtFyzflsbDO zjtFyzflsbDO) {
        return convert.convert(zjtFyzflsbDO);
    }

    @Override
    public ZjtFyzflsbDO convertToDO(ZjtFyzflsbDTO zjtFyzflsbDTO) {
        return convert.convertToDO(zjtFyzflsbDTO);
    }

    @Override
    public ZjtFyzflsbDTO addZjtSlxxbZjtFyzflsb(ZjtSlxxbDTO zjtSlxxb, boolean issf) {
        if (zjtSlxxb == null) {
            throw new ServiceException(500, "未找到受理号【" + zjtSlxxb.getNbslid() + "】数据");
        }

        //获取缴费单位对应表数据，如果没有，将不再生成数据
        ZjtCzzsdwdybDTO zjtCzzsdwdyb = findCzzsdwdyb(zjtSlxxb.getSldsjgsdwdm(), zjtSlxxb.getSldfjsjgsdwdm(),
                CkConstants.SFYWLX_EDZ, zjtSlxxb.getSfje());
        if (zjtCzzsdwdyb == null) {
            log.info("二代证【{}】未正常生成费用支付流水表", zjtSlxxb.getNbslid());
            log.info("未设置ZJT_CZZSDWDYB分局【{}】收费业务类型【{}】收费金额【{}】的执收项目编码", zjtSlxxb.getSldfjsjgsdwdm(), CkConstants.SFYWLX_EDZ, zjtSlxxb.getSfje());
            return null;
        }

        ZjtFyzflsbDTO zjtFyzflsb = convert.convertToDTO(zjtSlxxb);
        copyCzzsdwdybToFyzflsb(zjtFyzflsb, zjtCzzsdwdyb);

        zjtFyzflsb.setYwbmc("ZjtSlxxb");
        zjtFyzflsb.setYwbbh(zjtSlxxb.getNbslid());
        zjtFyzflsb.setSfywlx(CkConstants.SFYWLX_EDZ);//二代证收费支付类型
        zjtFyzflsb.setSfywlxmc(Dictionary.getValue(CkZzjDictTypeConstants.CKSFYWLX, zjtFyzflsb.getSfywlx()));//二代证收费支付类型
        zjtFyzflsb.setSfje(zjtSlxxb.getSfje());//收费金额
        zjtFyzflsb.setSfsf(Constants.NO);//默认未收费
        zjtFyzflsb.setBz(CkFyzflsbUtils.buildBz(zjtSlxxb.getZzlx(), zjtSlxxb.getLqfs(), ""));//备注：异地普证，异地快递证，原籍普证，原籍快递证
        zjtFyzflsb.setJkrsfhm(zjtSlxxb.getSqrgmsfhm());
        zjtFyzflsb.setJkrlxdh(zjtSlxxb.getSqrlxdh());
        zjtFyzflsb.setFxjzt(CkZzjConstants.SF_FXJZT_1_YX);
        zjtFyzflsb.setSfxxly(CkZzjConstants.SF_SFXXLY_1_YWSC);
        if (issf){
            zjtFyzflsb.setSfsf(CkConstants.SFLXBM_7_CBSBYSF);//重办时如果上一笔已收费，该笔标记重办无需收费
        }
        //生成缴款单号
        generateNewJkdhInfo(zjtFyzflsb);
        //设置审计字段
        setCurrentOperateInfo(zjtFyzflsb);

        return insert(zjtFyzflsb);
    }

    @Override
    public ZjtFyzflsbDTO addZjtLssfzSlxxbZjtFyzflsb(ZjtLssfzSlxxbDTO zjtLssfzSlxxb, ZjtSlxxbDTO zjtSlxxbDTO) {
        if (zjtLssfzSlxxb == null) {
            throw new ServiceException(500, "未找到受理号【" + zjtLssfzSlxxb.getLsslid() + "】数据");
        }
        //获取缴费单位对应表数据，如果没有，将不再生成数据，受理地分局数据归属单位有可能为空（前端未升级，导致该字段未填）
        BigDecimal lssfzSfje = CkFyzflsbUtils.getLssfzSfje(zjtLssfzSlxxb.getSldsjgsdwdm());
        ZjtCzzsdwdybDTO zjtCzzsdwdyb = findCzzsdwdyb(zjtLssfzSlxxb.getSldsjgsdwdm(), zjtLssfzSlxxb.getSldfjsjgsdwdm(), CkConstants.SFYWLX_LSSFZ, lssfzSfje);
        if (zjtCzzsdwdyb == null) {
            log.info("临时证【{}】未正常生成费用支付流水表", zjtLssfzSlxxb.getGmsfhm());
            log.info("未设置ZJT_CZZSDWDYB分局【{}】收费业务类型【{}】收费金额【{}】的执收项目编码", zjtLssfzSlxxb.getSldfjsjgsdwdm(), CkConstants.SFYWLX_LSSFZ, lssfzSfje);
            return null;
        }

        ZjtFyzflsbDTO zjtFyzflsb = convert.convertToDTO(zjtLssfzSlxxb);
        copyCzzsdwdybToFyzflsb(zjtFyzflsb, zjtCzzsdwdyb);

        zjtFyzflsb.setYwbmc("ZjtLssfzSlxxb");
        zjtFyzflsb.setYwbbh(zjtLssfzSlxxb.getLsslid());
        zjtFyzflsb.setSfywlx(CkConstants.SFYWLX_LSSFZ);//二代证收费支付类型
        zjtFyzflsb.setSfywlxmc(Dictionary.getValue(CkZzjDictTypeConstants.CKSFYWLX, zjtFyzflsb.getSfywlx()));//二代证收费支付类型
        zjtFyzflsb.setSfje(lssfzSfje);//收费金额
        zjtFyzflsb.setSfsf(Constants.NO);//默认未收费
        zjtFyzflsb.setBz("临时身份证");//备注：临时身份证
        zjtFyzflsb.setFxjzt(CkZzjConstants.SF_FXJZT_1_YX);
        zjtFyzflsb.setSfxxly(CkZzjConstants.SF_SFXXLY_1_YWSC);

        if (ZjConstant.ZJ_FWDX_70_GAB.equals(zjtLssfzSlxxb.getFwdx())) {
            //部级临证办理从临证受理信息中提取 20230206
            zjtFyzflsb.setJkrsfhm(zjtLssfzSlxxb.getGmsfhm());
            zjtFyzflsb.setJkrlxdh(zjtLssfzSlxxb.getLxdh());
        } else if (StringUtils.isNotEmpty(zjtLssfzSlxxb.getNbslid())) {
            if (zjtSlxxbDTO == null) {
                throw new ServiceException(500, "无法找到受理号【" + zjtLssfzSlxxb.getNbslid() + "】");
            } else {
                zjtFyzflsb.setJkrsfhm(zjtSlxxbDTO.getSqrgmsfhm());
                zjtFyzflsb.setJkrlxdh(zjtSlxxbDTO.getSqrlxdh());
            }
        }

        //生成缴款单号
        generateNewJkdhInfo(zjtFyzflsb);
        //设置审计字段
        setCurrentOperateInfo(zjtFyzflsb);

        return insert(zjtFyzflsb);
    }


    /**
     * 查询指定分局、业务类型、金额对应的财政执收单位对应表数据，如果没有，返回null
     */
    private ZjtCzzsdwdybDTO findCzzsdwdyb(String sjgsdwdm, String fjsjgsdwdm, String sfywlx, BigDecimal sfje) {
        ZjtCzzsdwdybDTO zjtCzzsdwdyb = zjtCzzsdwdybService.findDwdyb(sjgsdwdm, sfywlx, sfje).stream().findFirst().orElse(null);
        if (zjtCzzsdwdyb == null) {
            zjtCzzsdwdyb = zjtCzzsdwdybService.findDwdyb(fjsjgsdwdm, sfywlx, sfje).stream().findFirst().orElse(null);
        }
        return zjtCzzsdwdyb;
    }

    private void copyCzzsdwdybToFyzflsb(ZjtFyzflsbDTO zjtFyzflsb, ZjtCzzsdwdybDTO czzsdwdyb) {
        zjtFyzflsb.setZsdwdm(czzsdwdyb.getZsdwdm());
        zjtFyzflsb.setZsdwmc(czzsdwdyb.getZsdwmc());
        zjtFyzflsb.setXzqhdm(czzsdwdyb.getXzqhdm());
        zjtFyzflsb.setZsxmbm(czzsdwdyb.getZsxmbm());
        zjtFyzflsb.setZsxmmc(czzsdwdyb.getZsxmmc());
        zjtFyzflsb.setSfdj(czzsdwdyb.getSfdj());
        zjtFyzflsb.setSfsl(czzsdwdyb.getSfsl());
    }

    /**
     * 设置缴款单号数据及主键信息
     */
    private void generateNewJkdhInfo(ZjtFyzflsbDTO zjtFyzflsb) {
        CkFyzflsbJkdhGenerateInfo jkdhInfo = OtherFyzflsbUtils.generateNewJkdh(zjtFyzflsb.getSldsjgsdwdm(), zjtFyzflsb.getSfywlx());
        zjtFyzflsb.setJkdh(jkdhInfo.getJkdh());
        zjtFyzflsb.setSflsid(jkdhInfo.getJkdhsxh());
        zjtFyzflsb.setJkdlyqdbh(jkdhInfo.getJkdlyqdbh());
    }

    /**
     * 设置审计字段
     */

    private void setCurrentOperateInfo(ZjtFyzflsbDTO zjtFyzflsb) {
        zjtFyzflsb.setCzsj(ServerTimeUtils.getCurrentTime());
        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        zjtFyzflsb.setCzyid(String.valueOf(currentUser.getUserId()));
        zjtFyzflsb.setCzyxm(currentUser.getName());
        zjtFyzflsb.setCzydwdm(currentUser.getDeptCode());
        zjtFyzflsb.setCzydwmc(currentUser.getDeptName());
    }

    /**
     * 根据缴款单号获取缴费二维码
     *
     * @param jkdh
     * @return
     */
    @Override
    public String queryJfewm(String jkdh) {
        String base64qr = null;

        if (StringUtils.isEmpty(jkdh)) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "生成二维码错误：缴款单号未指定");
        }

        try {
            //20210325增加判断缴款信息是否15天以前，如果是，则不允许返回二维码
            ZjtFyzflsbDTO po = findById(jkdh);
            if (po == null) {
                throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "查无缴款信息");
            }

            //获取控制参数 1603 缴费时间阈值
            int isjyz = xtXtkzcsbService.findKzzIntBykzlb("1603", -15);

            //获取非现金的创建时间
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            Date czsj = format.parse(po.getCzsj());

            //获取当前时间 减去阈值
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(new Date());
            rightNow.add(Calendar.DAY_OF_YEAR, isjyz);

            //比对时间
            int compareTo = czsj.compareTo(rightNow.getTime());
            if (compareTo < 0) {
                throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "该缴款信息已超过设置的时间阈值，无法缴费。");
            }

            //生成缴费二维码
            String with = xtXtpzService.getCszByCslx(CkFyzflsbUtils.CK_FYZFLSB_QR_WIDTH, "200");
            String height = xtXtpzService.getCszByCslx(CkFyzflsbUtils.CK_FYZFLSB_QR_HEIGHT, "200");
            String template = xtXtpzService.getCszByCslx(CkFyzflsbUtils.CK_FYZFLSB_QR_TEMPLATE);
            base64qr = CkFyzflsbUtils.generateJkdhBase64QR(jkdh, template, with, height);


        } catch (Exception e) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "生成二维码错误：" + e.getMessage());
        }

        return base64qr;
    }
}
