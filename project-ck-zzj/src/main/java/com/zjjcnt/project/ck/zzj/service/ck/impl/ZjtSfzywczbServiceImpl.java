package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtSfzywczbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSfzywczbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtSfzywczbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtSfzywczbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtSfzywczbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 身份证业务操作表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Service
public class ZjtSfzywczbServiceImpl extends ExBaseServiceImpl<ZjtSfzywczbMapper, ZjtSfzywczbDO, ZjtSfzywczbDTO> implements ZjtSfzywczbService {

    ZjtSfzywczbConvert convert = ZjtSfzywczbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtSfzywczbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtSfzywczbDO::getYwslh, dto.getYwslh(), StringUtils.isNotEmpty(dto.getYwslh()));
        query.eq(ZjtSfzywczbDO::getSlh, dto.getSlh(), StringUtils.isNotEmpty(dto.getSlh()));
        query.eq(ZjtSfzywczbDO::getYwbz, dto.getYwbz(), StringUtils.isNotEmpty(dto.getYwbz()));
        query.eq(ZjtSfzywczbDO::getSlzt, dto.getSlzt(), StringUtils.isNotEmpty(dto.getSlzt()));
        query.eq(ZjtSfzywczbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.eq(ZjtSfzywczbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.ge(ZjtSfzywczbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtSfzywczbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtSfzywczbDO::getCzip, dto.getCzip(), StringUtils.isNotEmpty(dto.getCzip()));
        query.eq(ZjtSfzywczbDO::getCzydwdm, dto.getCzydwdm(), StringUtils.isNotEmpty(dto.getCzydwdm()));
        query.eq(ZjtSfzywczbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.eq(ZjtSfzywczbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        return query;
    }

    @Override
    public ZjtSfzywczbDTO convertToDTO(ZjtSfzywczbDO zjtSfzywczbDO) {
        return convert.convert(zjtSfzywczbDO);
    }

    @Override
    public ZjtSfzywczbDO convertToDO(ZjtSfzywczbDTO zjtSfzywczbDTO) {
        return convert.convertToDO(zjtSfzywczbDTO);
    }


    @Override
    protected void beforeInsert(ZjtSfzywczbDTO entity) throws ServiceException {
        if (StringUtils.isBlank(entity.getSlh())) {
            throw new ServiceException(500, "新增身份证业务操作记录时 slh 不能为空.");
        }
        if (StringUtils.isBlank(entity.getSlzt())) {
            throw new ServiceException(500, "新增身份证业务操作记录时 slzt 不能为空.");
        }
        super.beforeInsert(entity);
        if (StringUtils.isBlank(entity.getCzsj())) {
            entity.setCzsj(ServerTimeUtils.getCurrentTime());
        }

        CustomUserDetails user = SecurityUtils.getCurrentUser();
        entity.setCzyid(String.valueOf(user.getUserId()));
        entity.setCzyxm(user.getName());
        entity.setCzip(user.getRemoteAddress());
        entity.setCzydwdm(user.getDeptCode());
        entity.setCzydwmc(user.getDeptName());
    }

    @Override
    public ZjtSfzywczbDTO insert(String ywslh, String slh, String slzt, String ywbz, String bz) {
        ZjtSfzywczbDTO zjtSfzywczb = new ZjtSfzywczbDTO();
        zjtSfzywczb.setYwslh(ywslh);
        zjtSfzywczb.setSlh(slh);
        zjtSfzywczb.setSlzt(slzt);
        zjtSfzywczb.setYwbz(ywbz);
        zjtSfzywczb.setBz(bz);
        return insert(zjtSfzywczb);
    }

}
