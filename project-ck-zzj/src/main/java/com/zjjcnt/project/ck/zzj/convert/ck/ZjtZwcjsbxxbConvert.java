package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjsbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjsbxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjsbxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjsbxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtZwcjsbxxbDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZjtZwcjsbxxbExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-09 13:35:50
*/
@Mapper
public interface ZjtZwcjsbxxbConvert {

    ZjtZwcjsbxxbConvert INSTANCE = Mappers.getMapper(ZjtZwcjsbxxbConvert.class);

    ZjtZwcjsbxxbDTO convert(ZjtZwcjsbxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtZwcjsbxxbDO convertToDO(ZjtZwcjsbxxbDTO dto);

    ZjtZwcjsbxxbDTO convertToDTO(ZjtZwcjsbxxbPageReq req);

    ZjtZwcjsbxxbDTO convertToDTO(ZjtZwcjsbxxbCreateReq req);

    ZjtZwcjsbxxbDTO convertToDTO(ZjtZwcjsbxxbUpdateReq req);

    ZjtZwcjsbxxbPageResp convertToPageResp(ZjtZwcjsbxxbDTO dto);

    ZjtZwcjsbxxbViewResp convertToViewResp(ZjtZwcjsbxxbDTO dto);

    ZjtZwcjsbxxbCreateResp convertToCreateResp(ZjtZwcjsbxxbDTO dto);

    ZjtZwcjsbxxbExp convertToExp(ZjtZwcjsbxxbDTO dto);

}
