package com.zjjcnt.project.ck.zzj.domain;

import jakarta.xml.bind.annotation.XmlElement;

public class CkdshzJjzpHeadDto {
    private String code;
    private String message;
    private String retval;
    private  String retinfo_1;
    private  String retdesc;
    private  String description;
    private  String result;
    private  String retcode;
    private  String retinfo;


    @XmlElement(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    @XmlElement(name = "message")
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @XmlElement(name = "retval")
    public String getRetval() {
        return retval;
    }

    public void setRetval(String retval) {
        this.retval = retval;
    }

    @XmlElement(name = "retinfo_1")
    public String getRetinfo_1() {
        return retinfo_1;
    }

    public void setRetinfo_1(String retinfo_1) {
        this.retinfo_1 = retinfo_1;
    }

    @XmlElement(name = "retdesc")
    public String getRetdesc() {
        return retdesc;
    }

    public void setRetdesc(String retdesc) {
        this.retdesc = retdesc;
    }

    @XmlElement(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @XmlElement(name = "result")
    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    @XmlElement(name = "retcode")
    public String getRetcode() {
        return retcode;
    }

    public void setRetcode(String retcode) {
        this.retcode = retcode;
    }

    @XmlElement(name = "retinfo")
    public String getRetinfo() {
        return retinfo;
    }

    public void setRetinfo(String retinfo) {
        this.retinfo = retinfo;
    }
}
