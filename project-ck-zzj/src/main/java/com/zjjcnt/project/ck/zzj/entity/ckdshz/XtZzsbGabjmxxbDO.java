package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2025-05-21 10:04:10
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbGabjmxxbDTO
 */
@Data
@Table("xt_zzsb_gabjmxxb")
public class XtZzsbGabjmxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -6841707282877443534L;

    /**
     * 加密表id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 设备表id
     */
    @Column(value = "sbid")
    private Long sbid;

    /**
     * 启用标志
     */
    @Column(value = "qybz")
    private String qybz;

    /**
     * 设备码
     */
    @Column(value = "deviceid")
    private String deviceID;

    /**
     * 加密key
     */
    @Column(value = "enckey")
    private String encKey;

    /**
     * 版本号
     */
    @Column(value = "keyversion")
    private String keyVersion;

    /**
     * mac加密串
     */
    @Column(value = "mackey")
    private String macKey;

    /**
     * 无意义
     */
    @Column(value = "samplevalue")
    private String sampleValue;

    /**
     * 单位编码
     */
    @Column(value = "user")
    private String user;

    /**
     *
     */
    @Column(value = "validdate")
    private String validDate;

    /**
     * 校验值
     */
    @Column(value = "verifyvalue")
    private String verifyValue;


    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
