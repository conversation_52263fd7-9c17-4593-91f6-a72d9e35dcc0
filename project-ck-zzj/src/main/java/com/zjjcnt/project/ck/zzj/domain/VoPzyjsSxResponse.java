package com.zjjcnt.project.ck.zzj.domain;


import com.alibaba.fastjson2.annotation.J<PERSON><PERSON>ield;

import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "RopData")
public class VoPzyjsSxResponse {

    @J<PERSON><PERSON>ield(name = "isSuccess")
    private String isSuccess;
    @JSO<PERSON>ield(name = "message")
    private String message;
    @JSO<PERSON>ield(name = "errorCode")
    private String errorCode;
    @<PERSON><PERSON>NField(name = "stack")
    private String stack;
    @JSO<PERSON>ield(name = "data")
    private String data;

    public String getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(String isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getStack() {
        return stack;
    }

    public void setStack(String stack) {
        this.stack = stack;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
