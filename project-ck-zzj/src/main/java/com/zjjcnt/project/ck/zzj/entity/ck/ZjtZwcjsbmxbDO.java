package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-05-09 13:35:50
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjsbmxbDTO
 */
@Data
@Table("zjt_zwcjsbmxb")
public class ZjtZwcjsbmxbDO implements IdEntity<String> {
    private static final long serialVersionUID = -619453528498855805L;

    /**
     *
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String nbbh;

    /**
     *
     */
    @Column(value = "txzlpdmx")
    private String txzlpdmx;

    /**
     *
     */
    @Column(value = "bdzlpdmx")
    private String bdzlpdmx;

    /**
     *
     */
    @Column(value = "yszlpdmx")
    private String yszlpdmx;

    /**
     *
     */
    @Column(value = "dzzdcjcs")
    private String dzzdcjcs;

    /**
     *
     */
    @Column(value = "blcs1")
    private String blcs1;

    /**
     *
     */
    @Column(value = "blcs2")
    private String blcs2;

    /**
     *
     */
    @Column(value = "blcs3")
    private String blcs3;

    /**
     *
     */
    @Column(value = "blcs4")
    private String blcs4;

    /**
     *
     */
    @Column(value = "blcs5")
    private String blcs5;


    @Override
    public String getId() {
        return this.nbbh;
    }

    @Override
    public void setId(String id) {
        this.nbbh = id;
    }
}
