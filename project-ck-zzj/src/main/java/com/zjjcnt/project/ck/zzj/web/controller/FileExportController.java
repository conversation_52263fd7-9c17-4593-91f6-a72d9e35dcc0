package com.zjjcnt.project.ck.zzj.web.controller;

import com.deepoove.poi.data.Pictures;
import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtJmsfzxxbDTO;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtJmsfzxxbService;
import com.zjjcnt.project.ck.zzj.util.PdfTempleteUtil;
import com.zjjcnt.project.ck.zzj.web.request.BmpXpclReq;
import com.zjjcnt.project.ck.zzj.web.request.ZjtJmsfzsldjbPdfReq;
import com.zjjcnt.project.ck.zzj.web.request.ZjtLsjmsfzsldjbPdfReq;
import com.zjjcnt.project.ck.zzj.web.request.ZjtScsljmsfzkstbgzcnsPdfReq;
import com.zjjcnt.project.ck.zzj.web.response.BmpXpclRes;
import com.zjjcnt.project.ck.zzj.web.response.PdfRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Map;

/**
 * 文件导出控制器
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Slf4j
@Tag(name = "文件导出")
@RestController
@RequestMapping("/fileExport")
public class FileExportController extends AbstractCrudController<ZjtJmsfzxxbDTO> {

    @Autowired
    private ZjtJmsfzxxbService zjtJmsfzxxbService;

    @Override
    protected IBaseService getService() {
        return zjtJmsfzxxbService;
    }

    @PostMapping("exportJmsfzsldjbPdf")
    @Operation(summary = "获取居民身份证受理登记表pdf接口 N23010")
    public CommonResult<PdfRes> exportJmsfzsldjbPdf(@RequestBody ZjtJmsfzsldjbPdfReq req) {

        Map<String, Object> map = JsonUtils.parseMap(JsonUtils.toJsonString(req));
        map.put("zp", Pictures.ofBytes(Base64.getDecoder().decode(req.getBase64zp())).size(110, 130).create());
        map.remove("base64zp");
        map.put("qm", Pictures.ofBytes(Base64.getDecoder().decode(req.getBase64qm())).size(200, 80).create());
        map.remove("base64qm");

        //时间用数据库时间覆盖
        String currentTime = ServerTimeUtils.getCurrentTime();
        String slsj = DateTimeUtils.formatDateTime(currentTime, DateTimeUtils.CHAR_DATETIME_PATTERN);
        String slrq =  StringUtils.substring(slsj, 0, 11);;

        map.put("slsj", slsj);
        map.put("slrq", slrq);
        map.put("year", StringUtils.substring(currentTime, 0, 4));
        map.put("month", StringUtils.substring(currentTime, 4, 6));
        map.put("day", StringUtils.substring(currentTime, 6, 8));

        return CommonResult.success(new PdfRes(PdfTempleteUtil.getBase64(map, "/pdftemplate/jmsfzsldjb_template.docx")));
    }


    @PostMapping("exportLsjmsfzsldjbPdf")
    @Operation(summary = "临时居民身份证受理登记表pdf接口 N27011")
    public CommonResult<PdfRes> exportLsjmsfzsldjbPdf(@RequestBody ZjtLsjmsfzsldjbPdfReq req) {
        Map<String, Object> map = JsonUtils.parseMap(JsonUtils.toJsonString(req));
        map.put("zp", Pictures.ofBytes(Base64.getDecoder().decode(req.getBase64zp())).size(110, 130).create());
        map.remove("base64zp");
        map.put("qm", Pictures.ofBytes(Base64.getDecoder().decode(req.getBase64qm())).size(100, 50).create());
        map.remove("base64qm");

        //时间用数据库时间覆盖
        String currentTime = ServerTimeUtils.getCurrentTime();
        String czsj = DateTimeUtils.formatDateTime(currentTime, DateTimeUtils.CHAR_DATETIME_PATTERN);
        map.put("czsj", czsj);
        map.put("year", StringUtils.substring(currentTime, 0, 4));
        map.put("month", StringUtils.substring(currentTime, 4, 6));
        map.put("day", StringUtils.substring(currentTime, 6, 8));

        return CommonResult.success(new PdfRes(PdfTempleteUtil.getBase64(map, "/pdftemplate/lsjmsfzsldyb_template.docx")));
    }

    @PostMapping("exportScsljmsfzkstbgzcnsPdf")
    @Operation(summary = "首次申领居民身份证跨省通办告知承诺书pdf接口")
    public CommonResult<PdfRes> exportScsljmsfzkstbgzcnsPdf(@RequestBody ZjtScsljmsfzkstbgzcnsPdfReq req) {
        Map<String, Object> map = JsonUtils.parseMap(JsonUtils.toJsonString(req));
        if (StringUtils.isNotEmpty(req.getBase64qm())) {
            map.put("qm", Pictures.ofBytes(Base64.getDecoder().decode(req.getBase64qm())).size(100, 50).create());
        }
        map.remove("base64qm");

        //时间用数据库时间覆盖
        String currentTime = ServerTimeUtils.getCurrentTime();
        map.put("year", StringUtils.substring(currentTime, 0, 4));
        map.put("month", StringUtils.substring(currentTime, 4, 6));
        map.put("day", StringUtils.substring(currentTime, 6, 8));

        return CommonResult.success(new PdfRes(PdfTempleteUtil.getBase64(map, "/pdftemplate/scsljmsfzkstbgzcns_template.docx")));
    }

    @PostMapping("bmpXpcl")
    @Operation(summary = "图片转小票bmp接口 N1001")
    public CommonResult<BmpXpclRes> exportLsjmsfzsldjbPdf(@RequestBody BmpXpclReq req) {
        String base64String = null;
        byte[] byteArray = Base64.getDecoder().decode(req.getBase64String());
        try (ByteArrayInputStream bai = new ByteArrayInputStream(byteArray);
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            BufferedImage sourceImage = ImageIO.read(bai);
            BufferedImage outputImage = new BufferedImage(sourceImage.getWidth(), sourceImage.getHeight(),
                    BufferedImage.TYPE_BYTE_BINARY);
            outputImage.createGraphics().drawImage(sourceImage, 0, 0, null);

            // "bmp"格式
            ImageIO.write(outputImage, "bmp", byteArrayOutputStream);
            // byte
            byte[] imageBytes = byteArrayOutputStream.toByteArray();
            // 对字节数组进行Base64编码
            base64String = Base64.getEncoder().encodeToString(imageBytes);
        } catch (IOException e) {
            log.error("bmp转换失败", e);
        }
        return CommonResult.success(new BmpXpclRes(base64String));
    }

}
