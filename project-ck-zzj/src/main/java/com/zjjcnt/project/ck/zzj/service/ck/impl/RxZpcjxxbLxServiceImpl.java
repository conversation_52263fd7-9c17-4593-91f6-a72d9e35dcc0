package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ck.RxZpcjxxbLxConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.RxZpcjxxbLxDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.XtZzsbxxbLxDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.RxZpcjxxbLxDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.RxZpcjxxbLxMapper;
import com.zjjcnt.project.ck.zzj.service.ck.RxZpcjxxbLxService;
import com.zjjcnt.project.ck.zzj.service.ck.XtZzsbxxbLxService;
import com.zjjcnt.project.ck.zzj.service.ck.YwtbtSendTaskService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 离线照片采集信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-06-21 09:30:28
 */
@RequiredArgsConstructor
@Service
public class RxZpcjxxbLxServiceImpl extends AbstractBaseServiceImpl<RxZpcjxxbLxMapper, RxZpcjxxbLxDO, RxZpcjxxbLxDTO> implements RxZpcjxxbLxService {

    RxZpcjxxbLxConvert convert = RxZpcjxxbLxConvert.INSTANCE;

    private final XtZzsbxxbLxService xtZzsbxxbLxService;
    private final YwtbtSendTaskService ywtbtSendTaskService;

    @Override
    protected QueryWrapper genQueryWrapper(RxZpcjxxbLxDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(RxZpcjxxbLxDO::getZlpzsbdwbh, dto.getZlpzsbdwbh(), StringUtils.isNotEmpty(dto.getZlpzsbdwbh()));
        query.eq(RxZpcjxxbLxDO::getSbyjm, dto.getSbyjm(), StringUtils.isNotEmpty(dto.getSbyjm()));
        query.eq(RxZpcjxxbLxDO::getSbid, dto.getSbid(), Objects.nonNull(dto.getSbid()));
        query.eq(RxZpcjxxbLxDO::getZjzl, dto.getZjzl(), StringUtils.isNotEmpty(dto.getZjzl()));
        query.eq(RxZpcjxxbLxDO::getZjhm, dto.getZjhm(), StringUtils.isNotEmpty(dto.getZjhm()));
        query.eq(RxZpcjxxbLxDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(RxZpcjxxbLxDO::getGjdm, dto.getGjdm(), StringUtils.isNotEmpty(dto.getGjdm()));
        query.eq(RxZpcjxxbLxDO::getGjmc, dto.getGjmc(), StringUtils.isNotEmpty(dto.getGjmc()));
        query.eq(RxZpcjxxbLxDO::getCjyt, dto.getCjyt(), StringUtils.isNotEmpty(dto.getCjyt()));
        query.eq(RxZpcjxxbLxDO::getYtzp, dto.getYtzp(), Objects.nonNull(dto.getYtzp()));
        query.eq(RxZpcjxxbLxDO::getSfzzp, dto.getSfzzp(), Objects.nonNull(dto.getSfzzp()));
        query.eq(RxZpcjxxbLxDO::getCrjzp, dto.getCrjzp(), Objects.nonNull(dto.getCrjzp()));
        query.eq(RxZpcjxxbLxDO::getJszzp, dto.getJszzp(), Objects.nonNull(dto.getJszzp()));
        query.eq(RxZpcjxxbLxDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(RxZpcjxxbLxDO::getPcsmc, dto.getPcsmc(), StringUtils.isNotEmpty(dto.getPcsmc()));
        query.eq(RxZpcjxxbLxDO::getCzyid, dto.getCzyid(), Objects.nonNull(dto.getCzyid()));
        query.eq(RxZpcjxxbLxDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(RxZpcjxxbLxDO::getCzygmsfhm, ColumnUtils.encryptColumn(dto.getCzygmsfhm()), StringUtils.isNotEmpty(dto.getCzygmsfhm()));
        query.eq(RxZpcjxxbLxDO::getCzyip, dto.getCzyip(), StringUtils.isNotEmpty(dto.getCzyip()));
        query.ge(RxZpcjxxbLxDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(RxZpcjxxbLxDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(RxZpcjxxbLxDO::getTbbz, dto.getTbbz(), StringUtils.isNotEmpty(dto.getTbbz()));
        query.ge(RxZpcjxxbLxDO::getTbsj, dto.getTbsjStart(), StringUtils.isNotEmpty(dto.getTbsjStart()));
        query.le(RxZpcjxxbLxDO::getTbsj, dto.getTbsjEnd(), StringUtils.isNotEmpty(dto.getTbsjEnd()));
        query.eq(RxZpcjxxbLxDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(RxZpcjxxbLxDO::getDtbxt, dto.getDtbxt(), StringUtils.isNotEmpty(dto.getDtbxt()));
        query.eq(RxZpcjxxbLxDO::getSfscck, dto.getSfscck(), StringUtils.isNotEmpty(dto.getSfscck()));
        query.eq(RxZpcjxxbLxDO::getSfscjj, dto.getSfscjj(), StringUtils.isNotEmpty(dto.getSfscjj()));
        query.eq(RxZpcjxxbLxDO::getSfsccrj, dto.getSfsccrj(), StringUtils.isNotEmpty(dto.getSfsccrj()));
        query.eq(RxZpcjxxbLxDO::getSfsccsj, dto.getSfsccsj(), StringUtils.isNotEmpty(dto.getSfsccsj()));
        query.eq(RxZpcjxxbLxDO::getXb, dto.getXb(), StringUtils.isNotEmpty(dto.getXb()));
        query.eq(RxZpcjxxbLxDO::getCjly, dto.getCjly(), StringUtils.isNotEmpty(dto.getCjly()));
        return query;
    }

    @Override
    public RxZpcjxxbLxDTO convertToDTO(RxZpcjxxbLxDO rxZpcjxxbLxDO) {
        return convert.convert(rxZpcjxxbLxDO);
    }

    @Override
    public RxZpcjxxbLxDO convertToDO(RxZpcjxxbLxDTO rxZpcjxxbLxDTO) {
        return convert.convertToDO(rxZpcjxxbLxDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RxZpcjxxbLxDTO insert(RxZpcjxxbLxDTO dto) {

        // 获取设备信息
        XtZzsbxxbLxDTO zzsbxxb = xtZzsbxxbLxService.findById(dto.getSbid());
        if (zzsbxxb == null) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "查无设备信息:" + dto.getSbid());
        } else if (Constants.NO.equals(zzsbxxb.getQybz())) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "该设备未启用:" + dto.getSbid());
        }
        // 不是浙里拍照设备的不允许保存照片
        if (Constants.NO.equals(zzsbxxb.getSfzlpzsb())) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "该设备不是浙里拍照设备,不允许保存照片!");
        }

        CustomUserDetails user = SecurityUtils.getCurrentUser();
        dto.setCzsj(ServerTimeUtils.getCurrentTime());
        dto.setCjsj(ServerTimeUtils.getCurrentTime());
        dto.setCzyid(user.getUserId());
        dto.setCzyxm(user.getName());
        dto.setCzyip(user.getRemoteAddress());
        dto.setCzygmsfhm(user.getIdCard());
        dto.setTbbz(CkZzjConstants.TBBZ_DTB);

        dto.setZlpzsbdwbh(zzsbxxb.getZlpzsbdwbh());
        dto.setSbyjm(zzsbxxb.getSbyjm());

        dto.setSfscck(StringUtils.defaultIfEmpty(dto.getSfscck(), Constants.NO));
        dto.setSfscjj(StringUtils.defaultIfEmpty(dto.getSfscjj(), Constants.NO));
        dto.setSfsccrj(StringUtils.defaultIfEmpty(dto.getSfsccrj(), Constants.NO));
        dto.setSfsccsj(StringUtils.defaultIfEmpty(dto.getSfsccsj(), CkZzjConstants.SFSCBZ_YSC));

        RxZpcjxxbLxDTO insertEntity = super.insert(dto);

        ywtbtSendTaskService.createLxZpTask(insertEntity.getId(), insertEntity.getPcs());

        return insertEntity;
    }
}
