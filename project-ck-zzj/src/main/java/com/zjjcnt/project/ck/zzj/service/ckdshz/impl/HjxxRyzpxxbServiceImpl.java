package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxRyzpxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxRyzpxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxRyzpxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.HjxxRyzpxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxRyzpxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 人员照片信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 11:35:02
 */
@Service
public class HjxxRyzpxxbServiceImpl extends AbstractBaseServiceImpl<HjxxRyzpxxbMapper, HjxxRyzpxxbDO, HjxxRyzpxxbDTO> implements HjxxRyzpxxbService {

    HjxxRyzpxxbConvert convert = HjxxRyzpxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(HjxxRyzpxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(HjxxRyzpxxbDO::getRyid, dto.getRyid(), Objects.nonNull(dto.getRyid()));
        query.eq(HjxxRyzpxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(HjxxRyzpxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(HjxxRyzpxxbDO::getZp, dto.getZp(), Objects.nonNull(dto.getZp()));
        query.ge(HjxxRyzpxxbDO::getLrrq, dto.getLrrqStart(), StringUtils.isNotEmpty(dto.getLrrqStart()));
        query.le(HjxxRyzpxxbDO::getLrrq, dto.getLrrqEnd(), StringUtils.isNotEmpty(dto.getLrrqEnd()));
        query.eq(HjxxRyzpxxbDO::getDocfrom, dto.getDocfrom(), Objects.nonNull(dto.getDocfrom()));
        query.eq(HjxxRyzpxxbDO::getZpdx, dto.getZpdx(), Objects.nonNull(dto.getZpdx()));
        query.eq(HjxxRyzpxxbDO::getZphash, dto.getZphash(), Objects.nonNull(dto.getZphash()));
        query.eq(HjxxRyzpxxbDO::getZpsjdzlx, dto.getZpsjdzlx(), Objects.nonNull(dto.getZpsjdzlx()));
        query.eq(HjxxRyzpxxbDO::getZpsjdz, dto.getZpsjdz(), Objects.nonNull(dto.getZpsjdz()));
        query.eq(HjxxRyzpxxbDO::getYxbz, dto.getYxbz(), Objects.nonNull(dto.getYxbz()));
        query.eq(HjxxRyzpxxbDO::getSjgsdwdm, dto.getSjgsdwdm(), Objects.nonNull(dto.getSjgsdwdm()));
        query.ge(HjxxRyzpxxbDO::getScsj, dto.getScsjStart(), StringUtils.isNotEmpty(dto.getScsjStart()));
        query.le(HjxxRyzpxxbDO::getScsj, dto.getScsjEnd(), StringUtils.isNotEmpty(dto.getScsjEnd()));
        query.eq(HjxxRyzpxxbDO::getScrid, dto.getScrid(), Objects.nonNull(dto.getScrid()));
        query.eq(HjxxRyzpxxbDO::getScrip, dto.getScrip(), Objects.nonNull(dto.getScrip()));
        query.eq(HjxxRyzpxxbDO::getScrdwdm, dto.getScrdwdm(), Objects.nonNull(dto.getScrdwdm()));
        return query;
    }

    @Override
    public HjxxRyzpxxbDTO convertToDTO(HjxxRyzpxxbDO hjxxRyzpxxbDO) {
        return convert.convert(hjxxRyzpxxbDO);
    }

    @Override
    public HjxxRyzpxxbDO convertToDO(HjxxRyzpxxbDTO hjxxRyzpxxbDTO) {
        return convert.convertToDO(hjxxRyzpxxbDTO);
    }
}
