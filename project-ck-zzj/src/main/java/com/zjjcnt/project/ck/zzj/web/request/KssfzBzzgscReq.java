package com.zjjcnt.project.ck.zzj.web.request;

import com.zjjcnt.common.core.validation.IdCardNumber;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 办证资格审查req
 *
 * <AUTHOR>
 * @date 2024-10-14 11:14:00
 */
@Data
public class KssfzBzzgscReq {

    /**
     * 公民身份号码
     */
    @Schema(description = "公民身份号码")
    @NotBlank(message = "公民身份号码不能为空")
    @IdCardNumber
    private String gmsfhm;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @NotBlank(message = "姓名不能为空")
    private String xm;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    @NotNull(message = "设备id不能为空")
    private Long sbid;

    /**
     * 协同类型代码
     * 40：居民身份证首申业务跨省通办
     * 41: 居民身份证补换领业务跨省通办
     */
    @Schema(description = "协同类型代码, 40-首申 41-补换领")
    private String xthjdm;
}
