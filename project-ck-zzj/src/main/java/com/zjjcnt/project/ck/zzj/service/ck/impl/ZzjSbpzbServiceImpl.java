package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjSbpzbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjSbpzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjSbpzbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZzjSbpzbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjSbpzbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

/**
 * 自助设备配置表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-07-19 14:06:40
 */
@Service
public class ZzjSbpzbServiceImpl extends ExBaseServiceImpl<ZzjSbpzbMapper, ZzjSbpzbDO, ZzjSbpzbDTO> implements ZzjSbpzbService {

    ZzjSbpzbConvert convert = ZzjSbpzbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZzjSbpzbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZzjSbpzbDO::getPzbm, dto.getPzbm(), StringUtils.isNotEmpty(dto.getPzbm()));
        query.like(ZzjSbpzbDO::getPzms, dto.getPzms(), StringUtils.isNotEmpty(dto.getPzms()));
        query.eq(ZzjSbpzbDO::getMrz, dto.getMrz(), StringUtils.isNotEmpty(dto.getMrz()));
        query.eq(ZzjSbpzbDO::getYxbz, dto.getYxbz(), StringUtils.isNotEmpty(dto.getYxbz()));
        return query;
    }

    @Override
    public ZzjSbpzbDTO convertToDTO(ZzjSbpzbDO zzjSbpzbDO) {
        return convert.convert(zzjSbpzbDO);
    }

    @Override
    public ZzjSbpzbDO convertToDO(ZzjSbpzbDTO zzjSbpzbDTO) {
        return convert.convertToDO(zzjSbpzbDTO);
    }

    @Override
    protected boolean checkUnique(ZzjSbpzbDTO dto) {
        if (StringUtils.isBlank(dto.getPzbm())) {
            return true;
        }

        ZzjSbpzbDTO query = new ZzjSbpzbDTO();
        query.setPzbm(dto.getPzbm());
        query.setYxbz(Constants.YES);
        ZzjSbpzbDTO exist = find(query);
        return doCheckUnique(dto, exist);
    }

    @Override
    public boolean deleteById(Serializable id) {
        ZzjSbpzbDO zzjSbpzbDO = new ZzjSbpzbDO();
        zzjSbpzbDO.setId((Long) id);
        zzjSbpzbDO.setYxbz(Constants.NO);
        zzjSbpzbDO.setXgsj(ServerTimeUtils.getCurrentTime());
        return updateById(zzjSbpzbDO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSave(List<ZzjSbpzbDTO> list) {
        updateBatch(convert.convertToDO(list));
    }

    @Override
    public List<ZzjSbpzbDTO> listByPrefix(String prefix) {
        QueryWrapper query = QueryWrapper.create();
        query.likeLeft(ZzjSbpzbDO::getPzbm, prefix);
        return convertToDTO(list(query));
    }

}
