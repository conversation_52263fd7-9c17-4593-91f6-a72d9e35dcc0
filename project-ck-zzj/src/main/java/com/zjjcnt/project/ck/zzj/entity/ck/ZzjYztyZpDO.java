package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 自助机一照通用照片DO
 *
 * <AUTHOR>
 * @date 2025-01-14 16:13:45
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZzjYztyZpDTO
 */
@Data
@Table("zzj_yzty_zp")
public class ZzjYztyZpDO implements IdEntity<Long> {
    private static final long serialVersionUID = 1091470135651124918L;

    /**
     * id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 证件号码
     */
    @Column(value = "zjhm")
    private String zjhm;

    /**
     * 保存时间
     */
    @Column(value = "bcsj")
    private String bcsj;

    /**
     * 设备id
     */
    @Column(value = "sbid")
    private Long sbid;

    /**
     * 裁切算法
     */
    @Column(value = "cqsf")
    private String cqsf;

    /**
     * 裁切结果 1-成功 2-失败
     */
    @Column(value = "cqjg")
    private String cqjg;

    /**
     * 裁切参数
     */
    @Column(value = "cqcs")
    private String cqcs;

    /**
     * 裁切失败描述
     */
    @Column(value = "cqsbms")
    private String cqsbms;

    /**
     * 出入境质检结果
     */
    @Column(value = "crjzjjg")
    private String crjzjjg;

    /**
     * 出入境质检描述
     */
    @Column(value = "crjzjms")
    private String crjzjms;

    /**
     * 母片质检结果
     */
    @Column(value = "mpzjjg")
    private String mpzjjg;

    /**
     * 母片质检描述
     */
    @Column(value = "mpzjms")
    private String mpzjms;

    /**
     * 照片大小
     */
    @Column(value = "zpdx")
    private Integer zpdx;

    /**
     * 照片文件类型
     */
    @Column(value = "zpwjlx")
    private String zpwjlx;

    /**
     * 照片数据地址类型
     */
    @Column(value = "zpsjdzlx")
    private String zpsjdzlx;

    /**
     * 照片数据地址
     */
    @Column(value = "zpsjdz")
    private String zpsjdz;

    /**
     * 照片HASH
     */
    @Column(value = "zphash")
    private String zphash;

    /**
     * 创建人
     */
    @Column(value = "cjr")
    private String cjr;

    /**
     * 创建人账号
     */
    @Column(value = "cjrzh")
    private String cjrzh;

    /**
     * 创建人ip
     */
    @Column(value = "cjrip")
    private String cjrip;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;


    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
