package com.zjjcnt.project.ck.zzj.util;

import com.google.zxing.WriterException;
import com.zjjcnt.common.core.dict.Dictionary;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Base64;

/**
 * 费用支付流水表相关工具
 **/
public class CkFyzflsbUtils {
    //二维码模板相关配置：FWT_XT_XTPZ表
    public static final String CK_FYZFLSB_QR_TEMPLATE = "ck.fyzflsb.qr.template";
    public static final String CK_FYZFLSB_QR_WIDTH = "ck.fyzflsb.qr.width";
    public static final String CK_FYZFLSB_QR_HEIGHT = "ck.fyzflsb.qr.height";
    public static final String CK_FYZFLSB_JKDH_LYQDBH = "ck.fyzflsb.jkdh.lyqdbh";
    public static final String CK_FYZFLSB_SFJE_LSSFZ = "ck.fyzflsb.sfje.lssfz";//全省统一临时身份收费金额

    //自助申领机WEBSERVICE地址
    public static final String CK_ZZSLJ_WEBSERVICE_ADDRESS = "ck.zzslj.webservice.address";


    /**
     * 根据受理地数据归属单位获取临时身份证收费金额：
     * 目前全省统一设置
     */
    public static BigDecimal getLssfzSfje(String sldsjgsdwdm) {
        String value = Dictionary.getValue(SysadminConstants.FWT_XT_XTPZ, CK_FYZFLSB_SFJE_LSSFZ, "10");
        return new BigDecimal(value);
    }

//
//    /**
//     * 获取缴款单号
//     * sldsjgsdwdm 受理地数据归属单位代码
//     * sfywlx 收费业务类型
//     *
//     */
//    public static CkFyzflsbJkdhGenerateInfo generateNewJkdh(String sldsjgsdwdm, String sfywlx) {
//         return OtherFyzflsbUtils.generateNewJkdh(sldsjgsdwdm,sfywlx);
//    }

    /**
     * 根据缴款单号生成二维码
     */
    public static String generateJkdhBase64QR(String jkdh, String template, String width, String height) throws WriterException, IOException {
        String fileType = "png";
//        String url = "";//;PropertiesUtils.get(CK_FYZFLSB_QR_URL);
//        String width = "400";//PropertiesUtils.get(CK_FYZFLSB_QR_WIDTH,"100");
//        String height = "400";//PropertiesUtils.get(CK_FYZFLSB_QR_HEIGHT,"100");
        String content = buildQrContent(jkdh, template);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try {
            BufferedImage bufferedImage = QRCodeUtils.encode(content, Integer.parseInt(width), Integer.parseInt(height), 1);//0：不留白
            ImageIO.write(bufferedImage, fileType, baos);
        } finally {
            IOUtils.closeQuietly(baos);
        }

//        FileUtils.writeByteArrayToFile(new File("d:/abc.png"),baos.toByteArray());
        return Base64.getEncoder().encodeToString(baos.toByteArray());
    }


//    /**
//     * 根据缴款单号生成二维码
//     */
//    public static String generateJkdhBase64QR(String jkdh) throws ServiceException, IOException {
//           String fileType = "png";
////        String url = "";//;PropertiesUtils.get(CK_FYZFLSB_QR_URL);
////        String width = "400";//PropertiesUtils.get(CK_FYZFLSB_QR_WIDTH,"100");
////        String height = "400";//PropertiesUtils.get(CK_FYZFLSB_QR_HEIGHT,"100");
//        String content = buildQrContent(jkdh);
//        String width = PropertiesUtils.get(CK_FYZFLSB_QR_WIDTH, "200");
//        String height = PropertiesUtils.get(CK_FYZFLSB_QR_HEIGHT, "200");
//        ByteArrayOutputStream baos = new ByteArrayOutputStream();
//
//        try {
//            BufferedImage bufferedImage = QRCodeUtils.encode(content, Integer.parseInt(width), Integer.parseInt(height), 1);//0：不留白
//            ImageIO.write(bufferedImage, fileType, baos);
//        } finally {
//            IOUtils.closeQuietly(baos);
//        }
//
////        FileUtils.writeByteArrayToFile(new File("d:/abc.png"),baos.toByteArray());
//        return CkCommonUtils.encodeBase64(baos.toByteArray());
//    }

//    public static String buildQrContent(String jkdh) {
//        String template = PropertiesUtils.get(CK_FYZFLSB_QR_TEMPLATE);
//        if (StringUtils.isEmpty(template)) {
//            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "二维码内容模板未在FWT_XT_XTPZ[" + CK_FYZFLSB_QR_TEMPLATE + "]");
//        }
//        return template.replaceAll("\\{jkdh\\}", jkdh);
//    }

    public static String buildQrContent(String jkdh, String template) {
        if (StringUtils.isEmpty(template)) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "二维码内容模板未在FWT_XT_XTPZ[" + CK_FYZFLSB_QR_TEMPLATE + "]");
        }
        return template.replaceAll("\\{jkdh\\}", jkdh);
    }

    /**
     * 不覆盖所有zzlx及领取方式，只是简单翻译
     */
    public static String buildBz(String zzlx, String lqfs, String end) {
        if (StringUtils.isEmpty(zzlx) && StringUtils.isEmpty(lqfs)) {
            return end;
        }
        String zzlxDesc = ZjConstant.ZJ_ZZLX_SNYDZJ_NEW.equals(zzlx) ? "异地" : "原籍";
        String lqfsDesc = ZjConstant.ZJ_LQFS_YZKD.equals(lqfs) ? "快递证" : "普证";
        return zzlxDesc + lqfsDesc + end;
    }

//    /**
//     * 获取自助申领机WEBSERVICE地址
//     */
//    public static String getZzsljwebserviceAddress() {
//        return PropertiesUtils.get(CK_ZZSLJ_WEBSERVICE_ADDRESS);
//    }

//    public static void main(String[] args) {
//        try {
//            System.out.println(generateJkdhBase64QR("aaaaaadfasdf打发斯蒂芬阿斯顿发啥第三方大师傅发达大师傅阿道夫阿道夫aaaa"));
//        } catch (ServiceException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }

}
