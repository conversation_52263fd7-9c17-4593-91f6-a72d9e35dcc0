package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.XtZzsbxxbLxDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.XtZzsbxxbLxCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.XtZzsbxxbLxPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.XtZzsbxxbLxUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.XtZzsbxxbLxCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.XtZzsbxxbLxPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.XtZzsbxxbLxViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.XtZzsbxxbLxDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 自助设备信息表_离线Convert
*
* <AUTHOR>
* @date 2024-06-18 13:39:25
*/
@Mapper
public interface XtZzsbxxbLxConvert {

    XtZzsbxxbLxConvert INSTANCE = Mappers.getMapper(XtZzsbxxbLxConvert.class);

    XtZzsbxxbLxDTO convert(XtZzsbxxbLxDO entity);

    @InheritInverseConfiguration(name="convert")
    XtZzsbxxbLxDO convertToDO(XtZzsbxxbLxDTO dto);

    XtZzsbxxbLxDTO convertToDTO(XtZzsbxxbLxPageReq req);

    XtZzsbxxbLxDTO convertToDTO(XtZzsbxxbLxCreateReq req);

    XtZzsbxxbLxDTO convertToDTO(XtZzsbxxbLxUpdateReq req);

    XtZzsbxxbLxPageResp convertToPageResp(XtZzsbxxbLxDTO dto);

    XtZzsbxxbLxViewResp convertToViewResp(XtZzsbxxbLxDTO dto);

    XtZzsbxxbLxCreateResp convertToCreateResp(XtZzsbxxbLxDTO dto);

}
