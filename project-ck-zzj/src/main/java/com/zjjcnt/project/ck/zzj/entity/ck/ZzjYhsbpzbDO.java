package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 自助用户设备配置表DO
 *
 * <AUTHOR>
 * @date 2024-07-19 14:06:40
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZzjYhsbpzbDTO
 */
@Data
@Table("zzj_yhsbpzb")
public class ZzjYhsbpzbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -5330692463535158781L;

    /**
     * id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 用户id
     */
    @Column(value = "yhid")
    private Long yhid;

    /**
     * 设备配置id
     */
    @Column(value = "sbpzid")
    private Long sbpzid;

    /**
     * 配置值
     */
    @Column(value = "pzz")
    private String pzz;

    /**
     * 有效标志
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 修改时间
     */
    @Column(value = "xgsj")
    private String xgsj;


    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
