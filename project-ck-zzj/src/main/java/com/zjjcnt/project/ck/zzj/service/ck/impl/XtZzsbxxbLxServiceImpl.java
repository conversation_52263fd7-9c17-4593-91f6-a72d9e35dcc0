package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ck.XtZzsbxxbLxConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.XtZzsbxxbLxDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.XtZzsbxxbLxDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.XtZzsbxxbLxMapper;
import com.zjjcnt.project.ck.zzj.service.ck.XtZzsbxxbLxService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 自助设备信息表_离线ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-06-18 13:39:25
 */
@Service
public class XtZzsbxxbLxServiceImpl extends AbstractBaseServiceImpl<XtZzsbxxbLxMapper, XtZzsbxxbLxDO, XtZzsbxxbLxDTO> implements XtZzsbxxbLxService {

    XtZzsbxxbLxConvert convert = XtZzsbxxbLxConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(XtZzsbxxbLxDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(XtZzsbxxbLxDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(XtZzsbxxbLxDO::getPcsmc, dto.getPcsmc(), StringUtils.isNotEmpty(dto.getPcsmc()));
        query.eq(XtZzsbxxbLxDO::getAppkey, dto.getAppkey(), StringUtils.isNotEmpty(dto.getAppkey()));
        query.eq(XtZzsbxxbLxDO::getSbip, dto.getSbip(), StringUtils.isNotEmpty(dto.getSbip()));
        query.eq(XtZzsbxxbLxDO::getSbmac, dto.getSbmac(), StringUtils.isNotEmpty(dto.getSbmac()));
        query.eq(XtZzsbxxbLxDO::getSbsfvpn, dto.getSbsfvpn(), StringUtils.isNotEmpty(dto.getSbsfvpn()));
        query.eq(XtZzsbxxbLxDO::getSbsydz, dto.getSbsydz(), StringUtils.isNotEmpty(dto.getSbsydz()));
        query.eq(XtZzsbxxbLxDO::getSbxh, dto.getSbxh(), StringUtils.isNotEmpty(dto.getSbxh()));
        query.eq(XtZzsbxxbLxDO::getSbxhmc, dto.getSbxhmc(), StringUtils.isNotEmpty(dto.getSbxhmc()));
        query.eq(XtZzsbxxbLxDO::getSblx, dto.getSblx(), StringUtils.isNotEmpty(dto.getSblx()));
        query.eq(XtZzsbxxbLxDO::getSbzwcjqbh1, dto.getSbzwcjqbh1(), StringUtils.isNotEmpty(dto.getSbzwcjqbh1()));
        query.eq(XtZzsbxxbLxDO::getSbzwcjqbh2, dto.getSbzwcjqbh2(), StringUtils.isNotEmpty(dto.getSbzwcjqbh2()));
        query.eq(XtZzsbxxbLxDO::getLicense, dto.getLicense(), StringUtils.isNotEmpty(dto.getLicense()));
        query.ge(XtZzsbxxbLxDO::getYxqkssj, dto.getYxqkssjStart(), StringUtils.isNotEmpty(dto.getYxqkssjStart()));
        query.le(XtZzsbxxbLxDO::getYxqkssj, dto.getYxqkssjEnd(), StringUtils.isNotEmpty(dto.getYxqkssjEnd()));
        query.ge(XtZzsbxxbLxDO::getYxqjssj, dto.getYxqjssjStart(), StringUtils.isNotEmpty(dto.getYxqjssjStart()));
        query.le(XtZzsbxxbLxDO::getYxqjssj, dto.getYxqjssjEnd(), StringUtils.isNotEmpty(dto.getYxqjssjEnd()));
        query.eq(XtZzsbxxbLxDO::getQybz, dto.getQybz(), StringUtils.isNotEmpty(dto.getQybz()));
        query.ge(XtZzsbxxbLxDO::getDjsj, dto.getDjsjStart(), StringUtils.isNotEmpty(dto.getDjsjStart()));
        query.le(XtZzsbxxbLxDO::getDjsj, dto.getDjsjEnd(), StringUtils.isNotEmpty(dto.getDjsjEnd()));
        query.eq(XtZzsbxxbLxDO::getZxyy, dto.getZxyy(), StringUtils.isNotEmpty(dto.getZxyy()));
        query.eq(XtZzsbxxbLxDO::getSfjy, dto.getSfjy(), StringUtils.isNotEmpty(dto.getSfjy()));
        query.eq(XtZzsbxxbLxDO::getSfjyip, dto.getSfjyip(), StringUtils.isNotEmpty(dto.getSfjyip()));
        query.eq(XtZzsbxxbLxDO::getSfjylicense, dto.getSfjylicense(), StringUtils.isNotEmpty(dto.getSfjylicense()));
        query.eq(XtZzsbxxbLxDO::getSfjyyxsj, dto.getSfjyyxsj(), StringUtils.isNotEmpty(dto.getSfjyyxsj()));
        query.eq(XtZzsbxxbLxDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(XtZzsbxxbLxDO::getSbscgs, dto.getSbscgs(), StringUtils.isNotEmpty(dto.getSbscgs()));
        query.eq(XtZzsbxxbLxDO::getSbxsgs, dto.getSbxsgs(), StringUtils.isNotEmpty(dto.getSbxsgs()));
        query.eq(XtZzsbxxbLxDO::getSfyxsczp, dto.getSfyxsczp(), StringUtils.isNotEmpty(dto.getSfyxsczp()));
        query.eq(XtZzsbxxbLxDO::getSbsydmc, dto.getSbsydmc(), StringUtils.isNotEmpty(dto.getSbsydmc()));
        query.eq(XtZzsbxxbLxDO::getZddjrgmsfhm, ColumnUtils.encryptColumn(dto.getZddjrgmsfhm()), StringUtils.isNotEmpty(dto.getZddjrgmsfhm()));
        query.eq(XtZzsbxxbLxDO::getZddjrxm, ColumnUtils.encryptColumn(dto.getZddjrxm()), StringUtils.isNotEmpty(dto.getZddjrxm()));
        query.eq(XtZzsbxxbLxDO::getZlpzsbdwbh, dto.getZlpzsbdwbh(), StringUtils.isNotEmpty(dto.getZlpzsbdwbh()));
        query.eq(XtZzsbxxbLxDO::getSbzcgs, dto.getSbzcgs(), StringUtils.isNotEmpty(dto.getSbzcgs()));
        query.eq(XtZzsbxxbLxDO::getJd, dto.getJd(), StringUtils.isNotEmpty(dto.getJd()));
        query.eq(XtZzsbxxbLxDO::getWd, dto.getWd(), StringUtils.isNotEmpty(dto.getWd()));
        query.eq(XtZzsbxxbLxDO::getSbsydlbdm, dto.getSbsydlbdm(), StringUtils.isNotEmpty(dto.getSbsydlbdm()));
        query.eq(XtZzsbxxbLxDO::getTbxt, dto.getTbxt(), StringUtils.isNotEmpty(dto.getTbxt()));
        query.eq(XtZzsbxxbLxDO::getWdid, dto.getWdid(), StringUtils.isNotEmpty(dto.getWdid()));
        query.eq(XtZzsbxxbLxDO::getDwjgdm, dto.getDwjgdm(), StringUtils.isNotEmpty(dto.getDwjgdm()));
        query.eq(XtZzsbxxbLxDO::getDwjgmc, dto.getDwjgmc(), StringUtils.isNotEmpty(dto.getDwjgmc()));
        query.eq(XtZzsbxxbLxDO::getCkdwssdw, dto.getCkdwssdw(), StringUtils.isNotEmpty(dto.getCkdwssdw()));
        query.eq(XtZzsbxxbLxDO::getTsbzb, dto.getTsbzb(), StringUtils.isNotEmpty(dto.getTsbzb()));
        query.eq(XtZzsbxxbLxDO::getCkyhdlm, dto.getCkyhdlm(), StringUtils.isNotEmpty(dto.getCkyhdlm()));
        query.eq(XtZzsbxxbLxDO::getCkczyid, dto.getCkczyid(), Objects.nonNull(dto.getCkczyid()));
        query.eq(XtZzsbxxbLxDO::getSbdwbhold, dto.getSbdwbhold(), StringUtils.isNotEmpty(dto.getSbdwbhold()));
        query.eq(XtZzsbxxbLxDO::getSbdwbhnew, dto.getSbdwbhnew(), StringUtils.isNotEmpty(dto.getSbdwbhnew()));
        query.eq(XtZzsbxxbLxDO::getXxdz, dto.getXxdz(), StringUtils.isNotEmpty(dto.getXxdz()));
        query.eq(XtZzsbxxbLxDO::getSfzlpzsb, dto.getSfzlpzsb(), StringUtils.isNotEmpty(dto.getSfzlpzsb()));
        query.eq(XtZzsbxxbLxDO::getSfzdxq, dto.getSfzdxq(), StringUtils.isNotEmpty(dto.getSfzdxq()));
        query.eq(XtZzsbxxbLxDO::getSbyjm, dto.getSbyjm(), StringUtils.isNotEmpty(dto.getSbyjm()));
        return query;
    }

    @Override
    public XtZzsbxxbLxDTO convertToDTO(XtZzsbxxbLxDO xtZzsbxxbLxDO) {
        return convert.convert(xtZzsbxxbLxDO);
    }

    @Override
    public XtZzsbxxbLxDO convertToDO(XtZzsbxxbLxDTO xtZzsbxxbLxDTO) {
        return convert.convertToDO(xtZzsbxxbLxDTO);
    }

    @Override
    public XtZzsbxxbLxDTO insert(XtZzsbxxbLxDTO dto) {

        XtZzsbxxbLxDTO xtZzsbxxbLxDTO = findBySbyjm(dto.getSbyjm());
        if (Objects.nonNull(xtZzsbxxbLxDTO)) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "终端信息已存在，不能重复注册。");
        }

        dto.setSfzlpzsb(StringUtils.defaultIfEmpty(dto.getSfzlpzsb(), Constants.YES));
        dto.setSbscgs(CkZzjConstants.SBGS_HTZN);
        dto.setSbxsgs(CkZzjConstants.SBGS_HTZN);
        dto.setSbzcgs(CkZzjConstants.SBGS_HTZN);
        dto.setSfjyip(Constants.YES);
        dto.setSfjylicense(Constants.YES);
        dto.setSfjyyxsj(Constants.NO);
        dto.setSfyxsczp(Constants.YES);

        String today = ServerTimeUtils.getCurrentDate();
        dto.setYxqkssj(today);
        if (StringUtils.isBlank(dto.getYxqjssj())) {
            dto.setYxqjssj(DateTimeUtils.addYears(today, 1));
        }

        dto.setDjsj(ServerTimeUtils.getCurrentTime());
        dto.setQybz(Constants.YES);

        if (StringUtils.isBlank(dto.getSbxhmc())) {
            dto.setSbxhmc(dto.getSbxh());
        }

        dto.setCkczyid(SecurityUtils.getUserId());
        if (StringUtils.isNotBlank(dto.getCkyhdlm())) {
            dto.setCkyhdlm(SecurityUtils.getUsername());
        }

        dto.setLicense(creatLicense(dto.getSbip(), dto.getYxqkssj(),
                dto.getYxqjssj(), dto.getSbzwcjqbh1(), dto.getSbxh()));

        dto.setSfjy(Constants.NO);
        if (!Constants.NO.equals(dto.getSfjyip()) || !Constants.NO.equals(dto.getSfjylicense())
                || !Constants.NO.equals(dto.getSfjyyxsj())) {
            dto.setSfjy(Constants.YES);
        }
        return super.insert(dto);
    }


    @Override
    public boolean update(XtZzsbxxbLxDTO dto) {

        XtZzsbxxbLxDTO exist = findById(dto.getId());
        if (Objects.isNull(exist)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_NOT_FOUND);
        }

        dto.setCkczyid(SecurityUtils.getUserId());
        dto.setLicense(creatLicense(StringUtils.defaultString(dto.getSbip(), exist.getSbip()), exist.getYxqkssj(),
                StringUtils.defaultString(dto.getYxqjssj(), exist.getYxqjssj()),
                StringUtils.defaultString(dto.getSbzwcjqbh1(), exist.getSbzwcjqbh1()),
                StringUtils.defaultString(dto.getSbxh(), exist.getSbxh())));

        return super.update(dto);
    }

    @Override
    public void checkZzsb(String sbmac, String sbyjm) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(XtZzsbxxbLxDO::getSbmac, sbmac);
        query.eq(XtZzsbxxbLxDO::getSbyjm, sbyjm);
        XtZzsbxxbLxDTO xtZzsbxxbLxDTO = find(query);
        if (Objects.isNull(xtZzsbxxbLxDTO)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_NOT_REGISTER);
        }

        if (!StringUtils.equals(xtZzsbxxbLxDTO.getQybz(), Constants.YES)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_NOT_ENABLED);
        }

        if (!StringUtils.equals(xtZzsbxxbLxDTO.getCkyhdlm(), SecurityUtils.getUsername())) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_ACCOUNT_NOT_MATCH);
        }
    }

    private XtZzsbxxbLxDTO findBySbyjm(String sbyjm) {
        XtZzsbxxbLxDTO dto = new XtZzsbxxbLxDTO();
        dto.setSbyjm(sbyjm);
        return find(dto);
    }

    private String creatLicense(String sbip, String yxqkssj, String yxqjssj, String sbzwcjqbh1, String sbxh) {
        String strLicense = "";
        try {
            JSONObject json = new JSONObject();
            json.put("ip", sbip);
            json.put("yxqkssj", yxqkssj);
            json.put("yxqjssj", "".equals(yxqjssj) ? null : yxqjssj);
            json.put("zwcjqbh", "".equals(sbzwcjqbh1) ? null : sbzwcjqbh1);
            json.put("sbxh", sbxh);
            strLicense = DigestUtils.md5Hex(json.toJSONString());
        } catch (Exception ignore) {
        }
        return strLicense;
    }

}
