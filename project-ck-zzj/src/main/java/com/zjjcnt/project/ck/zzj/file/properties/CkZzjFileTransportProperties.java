package com.zjjcnt.project.ck.zzj.file.properties;

import com.zjjcnt.project.ck.core.file.property.AliyunOssProperties;
import com.zjjcnt.project.ck.core.file.property.FileTransportProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * zjjcnt:
 * file-transport:
 * type: local         #默认local，（local本地文件，database数据库, minio开源oss, aliyun_oss阿里云oss）
 * watermark:
 * enabled: true      #默认false
 * position: 2       #默认2，（0左上，1右上，2右下，3左下）
 * text: zjjcnt.com  #默认空
 * compress:
 * enabled: true           #默认false
 * max-height: 1000       #默认1000
 * max-weight: 1000       #默认1000
 * max-size: 1024*1024    #默认1024*1024
 * local:
 * root-path: /root/file  #默认/root/file
 * minio:
 * url: http://127.0.0.1:9000      #默认http://127.0.0.1:9000
 * access-key: minioadmin          #默认minioadmin
 * security-key: minioadmin        #默认minioadmin
 * bucket-name: khproj             #默认空
 * aliyun-oss:
 * endpoint: http://127.0.0.1:80      #默认https://oss-cn-hangzhou.aliyuncs.com
 * access-key-id: yourAccessKeyId          #默认空
 * access-key-secret: yourAccessKeySecret        #默认空
 * bucket-name: test             #默认空
 *
 * <AUTHOR>
 * @Date 2021-06-10 09:27:00
 * @Description 文件传输相关配置
 */
@Data
@ConfigurationProperties(prefix = "zjjcnt.file-transport.ck-zzj")
public class CkZzjFileTransportProperties {

    @NestedConfigurationProperty
    private FileTransportProperties ckFile = new FileTransportProperties();

    @NestedConfigurationProperty
    private FileTransportProperties ywslcl = new FileTransportProperties();

    @NestedConfigurationProperty
    private FileTransportProperties zpyt = new FileTransportProperties();

    @NestedConfigurationProperty
    private FileTransportProperties ryzp = new FileTransportProperties();

    @NestedConfigurationProperty
    private FileTransportProperties yztyzp = new FileTransportProperties();

    @NestedConfigurationProperty
    private AliyunOssProperties oss = new AliyunOssProperties();

}
