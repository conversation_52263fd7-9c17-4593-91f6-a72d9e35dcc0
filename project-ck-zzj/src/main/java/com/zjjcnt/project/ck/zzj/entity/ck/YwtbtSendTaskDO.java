package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-06-21 15:36:54
 * @see com.zjjcnt.project.ck.zzj.dto.ck.YwtbtSendTaskDTO
 */
@Data
@Table("ywtbt_send_task")
public class YwtbtSendTaskDO implements IdEntity<Long> {
    private static final long serialVersionUID = 3757488948054695509L;

    /**
     * 任务主键
     */
    @Id(keyType = KeyType.Auto)
    private Long taskid;

    /**
     * 业务类型（根据这个得到解析器）
     */
    @Column(value = "biztype")
    private String biztype;

    /**
     * 业务物理业务表
     */
    @Column(value = "tablename")
    private String tablename;

    /**
     * 业务主键名称
     */
    @Column(value = "pkname")
    private String pkname;

    /**
     * 业务主键
     */
    @Column(value = "pkvalue")
    private String pkvalue;

    /**
     * 业务数据
     */
    @Column(value = "data")
    private String data;

    /**
     * 任务类型：10:create，11:update，12:delete
     */
    @Column(value = "action")
    private String action;

    /**
     * 推送时间,一般当前时间，早于这个不发送
     */
    @Column(value = "tasktime")
    private String tasktime;

    /**
     * 任务类型：任务状态 :0:待推送 1 已推送 9 推送失败
     */
    @Column(value = "status")
    private String status;

    /**
     * 接口平台要求的type业务类型
     */
    @Column(value = "sendtype")
    private String sendtype;

    /**
     * 推送状态
     */
    @Column(value = "sendstatus")
    private String sendstatus;

    /**
     * 推送时间
     */
    @Column(value = "sendtime")
    private String sendtime;

    /**
     * 推送反馈结果
     */
    @Column(value = "sendresult")
    private String sendresult;

    /**
     * 错误信息
     */
    @Column(value = "message")
    private String message;

    /**
     *
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     *
     */
    @Column(value = "cjr")
    private String cjr;

    /**
     *
     */
    @Column(value = "cjrid")
    private String cjrid;

    /**
     *
     */
    @Column(value = "cjrip")
    private String cjrip;

    /**
     *
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     *
     */
    @Column(value = "xgr")
    private String xgr;

    /**
     *
     */
    @Column(value = "xgrid")
    private String xgrid;

    /**
     *
     */
    @Column(value = "xgrip")
    private String xgrip;

    /**
     *
     */
    @Column(value = "xgsj")
    private String xgsj;

    /**
     * 户籍地数据归属单位代码
     */
    @Column(value = "SJGSDWDM")
    private String sjgsdwdm;

//    /**
//     * 户籍地数据归属单位名称
//     */
//    @Column(value = "SJGSDWMC")
//    private String sjgsdwmc;


    @Override
    public Long getId() {
        return this.taskid;
    }

    @Override
    public void setId(Long id) {
        this.taskid = id;
    }
}
