package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLxSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLxSlxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLxSlxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLxSlxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLxSlxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLxSlxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLxSlxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtLxSlxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-09 11:39:12
*/
@Mapper
public interface ZjtLxSlxxbConvert {

    ZjtLxSlxxbConvert INSTANCE = Mappers.getMapper(ZjtLxSlxxbConvert.class);

    ZjtLxSlxxbDTO convert(ZjtLxSlxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtLxSlxxbDO convertToDO(ZjtLxSlxxbDTO dto);

    ZjtLxSlxxbDTO convertToDTO(ZjtLxSlxxbPageReq req);

    ZjtLxSlxxbDTO convertToDTO(ZjtLxSlxxbCreateReq req);

    ZjtLxSlxxbDTO convertToDTO(ZjtLxSlxxbUpdateReq req);

    ZjtLxSlxxbPageResp convertToPageResp(ZjtLxSlxxbDTO dto);

    ZjtLxSlxxbViewResp convertToViewResp(ZjtLxSlxxbDTO dto);

    ZjtLxSlxxbCreateResp convertToCreateResp(ZjtLxSlxxbDTO dto);

}
