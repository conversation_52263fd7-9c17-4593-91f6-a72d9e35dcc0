package com.zjjcnt.project.ck.zzj.util;

import java.awt.*;

public class QrcodeConfig {

     private String bgimgPath;

     private int bgimgWidth;

     private int bgimgHeight;

     private int qrcodeSize;

     private int qrcodeX;

     private int qrcodeY;

    private Integer textX;
     private int textY;

     private int textFontSize;

     //文字最大宽度，到达最大宽度后换行
     private Integer textMaxWidth;

     private String textFontColorRGB;

     //文字是否放到二维码图片中 1 是
     private String textInQrcode;

     private Font font;

    public String getBgimgPath() {
        return bgimgPath;
    }

    public void setBgimgPath(String bgimgPath) {
        this.bgimgPath = bgimgPath;
    }

    public int getBgimgWidth() {
        return bgimgWidth;
    }

    public void setBgimgWidth(int bgimgWidth) {
        this.bgimgWidth = bgimgWidth;
    }

    public int getBgimgHeight() {
        return bgimgHeight;
    }

    public void setBgimgHeight(int bgimgHeight) {
        this.bgimgHeight = bgimgHeight;
    }

    public int getQrcodeSize() {
        return qrcodeSize;
    }

    public void setQrcodeSize(int qrcodeSize) {
        this.qrcodeSize = qrcodeSize;
    }

    public int getQrcodeX() {
        return qrcodeX;
    }

    public void setQrcodeX(int qrcodeX) {
        this.qrcodeX = qrcodeX;
    }

    public int getQrcodeY() {
        return qrcodeY;
    }

    public void setQrcodeY(int qrcodeY) {
        this.qrcodeY = qrcodeY;
    }

    public Integer getTextX() {
        return textX;
    }

    public void setTextX(Integer textX) {
        this.textX = textX;
    }

    public int getTextY() {
        return textY;
    }

    public void setTextY(int textY) {
        this.textY = textY;
    }

    public int getTextFontSize() {
        return textFontSize;
    }

    public void setTextFontSize(int textFontSize) {
        this.textFontSize = textFontSize;
    }

    public String getTextFontColorRGB() {
        return textFontColorRGB;
    }

    public void setTextFontColorRGB(String textFontColorRGB) {
        this.textFontColorRGB = textFontColorRGB;
    }

    public Integer getTextMaxWidth() {
        return textMaxWidth;
    }

    public void setTextMaxWidth(Integer textMaxWidth) {
        this.textMaxWidth = textMaxWidth;
    }

    public String getTextInQrcode() {
        return textInQrcode;
    }

    public void setTextInQrcode(String textInQrcode) {
        this.textInQrcode = textInQrcode;
    }

    public Font getFont() {
        return font;
    }

    public void setFont(Font font) {
        this.font = font;
    }
}
