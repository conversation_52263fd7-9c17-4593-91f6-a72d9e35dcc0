package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSfzywczbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSfzywczbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSfzywczbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSfzywczbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSfzywczbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSfzywczbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSfzywczbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtSfzywczbDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZjtSfzywczbExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 身份证业务操作表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtSfzywczbConvert {

    ZjtSfzywczbConvert INSTANCE = Mappers.getMapper(ZjtSfzywczbConvert.class);

    ZjtSfzywczbDTO convert(ZjtSfzywczbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtSfzywczbDO convertToDO(ZjtSfzywczbDTO dto);

    ZjtSfzywczbDTO convertToDTO(ZjtSfzywczbPageReq req);

    ZjtSfzywczbDTO convertToDTO(ZjtSfzywczbCreateReq req);

    ZjtSfzywczbDTO convertToDTO(ZjtSfzywczbUpdateReq req);

    ZjtSfzywczbPageResp convertToPageResp(ZjtSfzywczbDTO dto);

    ZjtSfzywczbViewResp convertToViewResp(ZjtSfzywczbDTO dto);

    ZjtSfzywczbCreateResp convertToCreateResp(ZjtSfzywczbDTO dto);

    ZjtSfzywczbExp convertToExp(ZjtSfzywczbDTO dto);

}
