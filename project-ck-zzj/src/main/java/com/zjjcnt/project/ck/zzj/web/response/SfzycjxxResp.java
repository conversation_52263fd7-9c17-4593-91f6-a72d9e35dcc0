package com.zjjcnt.project.ck.zzj.web.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 身份证预采集信息resp
 *
 * <AUTHOR>
 * @date 2025-05-09 16:44:00
 */
@Data
public class SfzycjxxResp {

    @Schema(description = "办理人公民身份号码")
    private String blrGmsfhm;

    @Schema(description = "办理人姓名")
    private String blrXm;

    @Schema(description = "办理人联系电话")
    private String blrLxdh;

    @Schema(description = "办理人现居住所在区域")
    private String blrXjzszqy;

    @Schema(description = "办理人现居住详细地址")
    private String blrXjzxxdz;

    @Schema(description = "收件人姓名")
    private String sjrXm;

    @Schema(description = "收件人联系电话")
    private String sjrLxdh;

    @Schema(description = "收件人现居住所在区域")
    private String sjrXjzszqy;

    @Schema(description = "收件人现居住详细地址")
    private String sjrXjzxxdz;

    @Schema(description = "紧急联系人姓名")
    private String jjlxrXm;

    @Schema(description = "紧急联系人联系电话")
    private String jjlxrLxdh;

    @Schema(description = "紧急联系人与收件人关系")
    private String jjlxrYsjrgx;
}
