package com.zjjcnt.project.ck.zzj.util;

import com.google.zxing.*;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.GlobalHistogramBinarizer;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.Rectangle2D;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import static java.awt.image.BufferedImage.TYPE_INT_RGB;


public class QRCodeUtils {

    public static final String UTF8 = "UTF-8";
    public static final String YES = "1";

    public static BufferedImage encode(String content, int width, int height) throws WriterException {
        return encode(content, width, height, 1);
    }

    public static BufferedImage encode(String content, int width, int height, Integer margin) throws WriterException {
        return encode(content, width, height, margin, UTF8);
    }

    public static BufferedImage encode(String content, int width, int height, Integer margin, String characterSet) throws WriterException {
        MultiFormatWriter formatWriter = new MultiFormatWriter();
        Map hints = new HashMap();
        hints.put(EncodeHintType.CHARACTER_SET, characterSet);
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.Q);
        if (margin != null) {
            hints.put(EncodeHintType.MARGIN, margin);
        }
        BitMatrix bitMatrix = formatWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);
        return MatrixToImageWriter.toBufferedImage(bitMatrix);
    }

    public static String decode(String name) throws IOException {
        return decode(new FileInputStream(name));
    }

    public static String decode(File file) throws IOException {
        return decode(new FileInputStream(file));
    }

    public static String decode(InputStream in) throws IOException {
        MultiFormatReader formatReader = new MultiFormatReader();
        BufferedImage image = ImageIO.read(in);
        LuminanceSource source = new BufferedImageLuminanceSource(image);
        BinaryBitmap bitmap = new BinaryBitmap(new GlobalHistogramBinarizer(source));
        Map hints = new HashMap();
        hints.put(DecodeHintType.CHARACTER_SET, UTF8);
        try {
            Result result = formatReader.decode(bitmap, hints);
            return result.getText();
        } catch (NotFoundException e) {
            return null;
        }
    }

    public static BufferedImage generate(QrcodeConfig config, String qrcode, String text,boolean showBg) throws WriterException, IOException {
        BufferedImage image = new BufferedImage(config.getBgimgWidth(), config.getBgimgHeight(),
                TYPE_INT_RGB);
        if(showBg && StringUtils.isNotBlank(config.getBgimgPath())) {
            setBg(config, image);
        }
        setQrcode(config, image, qrcode, text);
        if(!isTextInQrcode(config)) {
            setText(config, image, text);
        }
        return image;
    }

    public static boolean isTextInQrcode(QrcodeConfig config) {
        return YES.equals(config.getTextInQrcode());
    }

    public static BufferedImage generate(QrcodeConfig config, String qrcode, String text) throws WriterException, IOException {
        /**
         * 默认显示背景图片
         */
        return generate(config,qrcode,text,true);
    }

    private static void setBg(QrcodeConfig config, BufferedImage image) throws IOException {
        // 构建绘图对象
        Graphics2D g = image.createGraphics();
        setGraphics2D(g);
        File bgFile = new File(config.getBgimgPath());
        BufferedImage bg = ImageIO.read(bgFile);
        g.drawImage(bg, 0, 0, bg.getWidth(), bg.getHeight(), null);
        g.dispose();
        bg.flush();
    }

    private static void setQrcode(QrcodeConfig config, BufferedImage image, String qrcode, String text) throws WriterException {
        // 构建绘图对象
        Graphics2D g = image.createGraphics();
        setGraphics2D(g);
        // 读取Logo图片
        BufferedImage qrcodeImage = QRCodeUtils.encode(qrcode, config.getQrcodeSize(), config.getQrcodeSize());
        if(isTextInQrcode(config) && StringUtils.isNotBlank(text)) {
            //如果文字嵌入到二维码内
            BufferedImage textImage = createTextImage(config, text);
            try {
                insertImage(qrcodeImage, textImage.getScaledInstance(textImage.getWidth(),textImage.getHeight(),Image.SCALE_SMOOTH));
            } catch (Exception e) {
                throw new WriterException(e);
            }
        }
        g.drawImage(qrcodeImage, config.getQrcodeX(), config.getQrcodeY(), qrcodeImage.getWidth(), qrcodeImage.getHeight(), null);
        g.dispose();
        qrcodeImage.flush();
    }

    /**
     * 附加说明文字
     *
     * @param text  文字
     * @param image 需要添加文字的图片
     */
    private static void setText(QrcodeConfig config, BufferedImage image, String text) {
        int fontSize = config.getTextFontSize();
        // 创建一个 Graphics
        Graphics2D g = image.createGraphics();
        setGraphics2D(g);
        // 设置 Graphics 的绘制颜色
        g.setColor(getColor(config));

        // 设置字体
        Font font = config.getFont();
        if (null == font) {
            font = new Font("黑体", Font.PLAIN, fontSize);
        }
        g.setFont(font);

        Double startX = null != config.getTextX() ? config.getTextX() : (config.getBgimgWidth() - getFontSize(g, font, text)) / 2;
        if (startX < 0) {
            int index = Double.valueOf(text.length() * 0.6).intValue();
            String subText = text.substring(0, index);
            startX = (config.getBgimgWidth() - getFontSize(g, font, subText)) / 2;
            g.drawString(subText, startX.intValue(), config.getTextY() - 15);
            subText = text.substring(index);
            startX = (config.getBgimgWidth() - getFontSize(g, font, subText)) / 2;
            int fontHeight = Double.valueOf(getFontHeight(g, font)).intValue();
            if(null != config.getTextMaxWidth()) {
                drawTextInCenter(g, font, subText, startX.intValue(), config.getTextY() + fontHeight, config.getTextMaxWidth());
            }else {
                g.drawString(subText, startX.intValue(), config.getTextY() + fontHeight);
            }
        } else {
            if(null != config.getTextMaxWidth()) {
                drawTextInCenter(g, font, text, startX.intValue(), config.getTextY(), config.getTextMaxWidth());
            }else {
                g.drawString(text, startX.intValue(), config.getTextY());
            }
        }

        // 保存
        g.dispose();
        image.flush();
    }

    /**
     * 获取对应的文字所占有的长度
     */
    private static double getFontSize(Graphics2D g2d, Font font, String text) {
        // 设置大字体
        FontRenderContext context = g2d.getFontRenderContext();
        // 获取字体的像素范围对象
        Rectangle2D stringBounds = font.getStringBounds(text, context);
        return stringBounds.getWidth();
    }

    /**
     * 获取对应字体的文字的高度
     */
    private static double getFontHeight(Graphics2D g2d, Font font) {
        // 设置大字体
        FontRenderContext context = g2d.getFontRenderContext();
        // 获取字体的像素范围对象
        Rectangle2D stringBounds = font.getStringBounds("中", context);
        return stringBounds.getWidth();
    }

    private static Color getColor(QrcodeConfig config) {
        Color result = null;
        String[] array = StringUtils.split(config.getTextFontColorRGB(), ",");
        if (ArrayUtils.getLength(array) == 3) {
            try {
                result = new Color(toInt(array[0]), toInt(array[1]), toInt(array[2]));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (null == result) {
            result = Color.DARK_GRAY;
        }
        return result;
    }

    private static int toInt(String value) {
        return Integer.parseInt(value.trim());
    }

    /**
     * 设置 Graphics2D 属性  （抗锯齿）
     *
     * @param graphics2D graphics2D
     */
    private static void setGraphics2D(Graphics2D graphics2D) {
        graphics2D.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics2D.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_DEFAULT);
        Stroke s = new BasicStroke(1, BasicStroke.CAP_ROUND, BasicStroke.JOIN_MITER);
        graphics2D.setStroke(s);
    }

    private static BufferedImage createTextImage(QrcodeConfig config, String text) {
        int imageSize = config.getQrcodeSize() / 2;
        int fontSize = config.getTextFontSize();
        BufferedImage image = new BufferedImage(imageSize, imageSize,TYPE_INT_RGB);
        //创建一个背景透明的图片
        // 创建一个 Graphics
        Graphics2D g = image.createGraphics();
        g.setColor(Color.WHITE);//设置笔刷白色
        g.fillRect(0,0,imageSize,imageSize);
        setGraphics2D(g);
        // 设置 Graphics 的绘制颜色
        g.setColor(getColor(config));

        // 设置字体
        Font font = config.getFont();
        if (null == font) {
            font = new Font("微软雅黑", Font.PLAIN, fontSize);
        }
        g.setFont(font);
        g.drawString(text, 10, 10);
        drawTextInCenter(g, font, text, 0, 0, imageSize);
        // 保存
        g.dispose();
        image.flush();
        return image;
    }

    public static void drawTextInCenter(Graphics2D g , Font font , String text , int x , int y , int maxWidth) {
        FontMetrics fontMetrics = g.getFontMetrics(font);
        int textWidth = fontMetrics.stringWidth(text);
        if(textWidth > maxWidth) {
            int oneLineLetterCount = maxWidth / fontMetrics.charWidth(text.charAt(0));
            int oneLineHeight = fontMetrics.getHeight();
            int gap = 5;
            int i = 0;

            while(i < text.length()) {
                int startX = x;
                int endX = i + oneLineLetterCount;
                endX = endX > text.length() ? text.length() : endX;
                String line = text.substring(i, endX);
                int lineTextWidth = fontMetrics.stringWidth(line);
                if(lineTextWidth < maxWidth) {
                    startX += (maxWidth - lineTextWidth) / 2;
                }
                g.drawString(line, startX, y);
                i = endX;
                y += oneLineHeight + gap;
            }
        }else {
            int endx = (maxWidth - textWidth) / 2;
            g.drawString(text,x + endx, y);
        }
    }

    /**
     * 插入人像
     *
     * @param source   二维码文件缓冲流
     * @param src 嵌入的图片Image
     * @throws Exception
     */
    private static void insertImage(BufferedImage source, Image src) throws Exception {
        int width = src.getWidth(null);
        int height = src.getHeight(null);
        int parentWidth = source.getWidth();
        int parentHeight = source.getHeight();
        // 人像照片
        /*if (width > parentWidth || height > parentHeight) {
            if (width > parentWidth) {
                width = parentWidth;
            }
            if (height > parentHeight) {
                height = parentHeight;
            }
            Image image = src.getScaledInstance(width, height, Image.SCALE_SMOOTH);
            BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics g = tag.getGraphics();
            // 绘制缩小后的图
            g.drawImage(image, 0, 0, null);
            g.dispose();
            src = image;
        }*/
        // 插入人像照片
        Graphics2D graph = source.createGraphics();
        int x = (parentWidth - width) / 2;
        int y = (parentHeight - height) / 2;
        graph.drawImage(src, x, y, width, height, null);
        Shape shape = new RoundRectangle2D.Float(x, y, width, width, 6, 6);
        graph.setStroke(new BasicStroke(3f));
        graph.draw(shape);
        graph.dispose();
    }
}
