package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjlsbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjlsbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjlsbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjlsbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjlsbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjlsbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtZwcjlsbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-09 11:39:12
*/
@Mapper
public interface ZjtZwcjlsbConvert {

    ZjtZwcjlsbConvert INSTANCE = Mappers.getMapper(ZjtZwcjlsbConvert.class);

    ZjtZwcjlsbDTO convert(ZjtZwcjlsbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtZwcjlsbDO convertToDO(ZjtZwcjlsbDTO dto);

    ZjtZwcjlsbDTO convertToDTO(ZjtZwcjlsbPageReq req);

    ZjtZwcjlsbDTO convertToDTO(ZjtZwcjlsbCreateReq req);

    ZjtZwcjlsbDTO convertToDTO(ZjtZwcjlsbUpdateReq req);

    ZjtZwcjlsbPageResp convertToPageResp(ZjtZwcjlsbDTO dto);

    ZjtZwcjlsbViewResp convertToViewResp(ZjtZwcjlsbDTO dto);

    ZjtZwcjlsbCreateResp convertToCreateResp(ZjtZwcjlsbDTO dto);

}
