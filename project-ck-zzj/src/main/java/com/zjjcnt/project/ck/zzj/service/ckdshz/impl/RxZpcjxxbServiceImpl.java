package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.exception.UserErrorCode;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.RxZpcjxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.RxZpcjxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RxZpcjxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.RxZpcjxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RxZpcjxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-15 16:18:06
 */
@Service
public class RxZpcjxxbServiceImpl extends AbstractBaseServiceImpl<RxZpcjxxbMapper, RxZpcjxxbDO, RxZpcjxxbDTO> implements RxZpcjxxbService {

    RxZpcjxxbConvert convert = RxZpcjxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(RxZpcjxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(RxZpcjxxbDO::getSbid, dto.getSbid(), Objects.nonNull(dto.getSbid()));
        query.eq(RxZpcjxxbDO::getZjzl, dto.getZjzl(), Objects.nonNull(dto.getZjzl()));
        query.eq(RxZpcjxxbDO::getZjhm, dto.getZjhm(), Objects.nonNull(dto.getZjhm()));
        query.eq(RxZpcjxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(RxZpcjxxbDO::getGjdm, dto.getGjdm(), Objects.nonNull(dto.getGjdm()));
        query.eq(RxZpcjxxbDO::getGjmc, dto.getGjmc(), Objects.nonNull(dto.getGjmc()));
        query.eq(RxZpcjxxbDO::getCjyt, dto.getCjyt(), Objects.nonNull(dto.getCjyt()));
        query.eq(RxZpcjxxbDO::getYtzpid, dto.getYtzpid(), Objects.nonNull(dto.getYtzpid()));
        query.eq(RxZpcjxxbDO::getSfzzpid, dto.getSfzzpid(), Objects.nonNull(dto.getSfzzpid()));
        query.eq(RxZpcjxxbDO::getCrjzpid, dto.getCrjzpid(), Objects.nonNull(dto.getCrjzpid()));
        query.eq(RxZpcjxxbDO::getJszzpid, dto.getJszzpid(), Objects.nonNull(dto.getJszzpid()));
        query.eq(RxZpcjxxbDO::getQtzpid, dto.getQtzpid(), Objects.nonNull(dto.getQtzpid()));
        query.eq(RxZpcjxxbDO::getQtzplb, dto.getQtzplb(), Objects.nonNull(dto.getQtzplb()));
        query.eq(RxZpcjxxbDO::getQtzpmc, dto.getQtzpmc(), Objects.nonNull(dto.getQtzpmc()));
        query.eq(RxZpcjxxbDO::getPcs, dto.getPcs(), Objects.nonNull(dto.getPcs()));
        query.eq(RxZpcjxxbDO::getPcsmc, dto.getPcsmc(), Objects.nonNull(dto.getPcsmc()));
        query.eq(RxZpcjxxbDO::getCzyid, dto.getCzyid(), Objects.nonNull(dto.getCzyid()));
        query.eq(RxZpcjxxbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(RxZpcjxxbDO::getCzygmsfhm, ColumnUtils.encryptColumn(dto.getCzygmsfhm()), StringUtils.isNotEmpty(dto.getCzygmsfhm()));
        query.eq(RxZpcjxxbDO::getCzyip, dto.getCzyip(), Objects.nonNull(dto.getCzyip()));
        query.ge(RxZpcjxxbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(RxZpcjxxbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(RxZpcjxxbDO::getTbbz, dto.getTbbz(), Objects.nonNull(dto.getTbbz()));
        query.ge(RxZpcjxxbDO::getTbsj, dto.getTbsjStart(), StringUtils.isNotEmpty(dto.getTbsjStart()));
        query.le(RxZpcjxxbDO::getTbsj, dto.getTbsjEnd(), StringUtils.isNotEmpty(dto.getTbsjEnd()));
        query.eq(RxZpcjxxbDO::getBz, dto.getBz(), Objects.nonNull(dto.getBz()));
        query.eq(RxZpcjxxbDO::getDtbxt, dto.getDtbxt(), Objects.nonNull(dto.getDtbxt()));
        query.eq(RxZpcjxxbDO::getSfscck, dto.getSfscck(), StringUtils.isNotEmpty(dto.getSfscck()));
        query.eq(RxZpcjxxbDO::getSfscjj, dto.getSfscjj(), StringUtils.isNotEmpty(dto.getSfscjj()));
        query.eq(RxZpcjxxbDO::getSfsccrj, dto.getSfsccrj(), StringUtils.isNotEmpty(dto.getSfsccrj()));
        query.eq(RxZpcjxxbDO::getSfsccsj, dto.getSfsccsj(), StringUtils.isNotEmpty(dto.getSfsccsj()));
        query.eq(RxZpcjxxbDO::getXb, dto.getXb(), Objects.nonNull(dto.getXb()));
        query.eq(RxZpcjxxbDO::getCjly, dto.getCjly(), Objects.nonNull(dto.getCjly()));
        return query;
    }

    @Override
    public RxZpcjxxbDTO convertToDTO(RxZpcjxxbDO rxZpcjxxbDO) {
        return convert.convert(rxZpcjxxbDO);
    }

    @Override
    public RxZpcjxxbDO convertToDO(RxZpcjxxbDTO rxZpcjxxbDTO) {
        return convert.convertToDO(rxZpcjxxbDTO);
    }

    @Override
    public void validateZpcjxx(RxZpcjxxbDTO po) {
        //操作员公民身份号码
        if (StringUtils.isEmpty(po.getCzygmsfhm())) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "校验失败，缺少操作员公民身份号码数据");
        }
        //操作员姓名
        if (StringUtils.isEmpty(po.getCzyxm())) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "校验失败，缺少操作员姓名数据");
        }
        //操作员IP
        if (StringUtils.isEmpty(po.getCzyip())) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "校验失败，缺少操作员IP数据");
        }

//        //证件种类
//        if (po.getZjzl() == null || "".equals(po.getZjzl())) {
//            throw new ServiceException("校验失败，缺少证件种类数据");
//        }
        //证件号码
        if (StringUtils.isEmpty(po.getZjhm())) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "校验失败，缺少证件号码数据");
        }
        //证件姓名
        if (StringUtils.isEmpty(po.getXm())) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "校验失败，缺少证件姓名数据");
        }
        //国家代码
        if (StringUtils.isEmpty(po.getGjdm())) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "校验失败，缺少国家代码数据");
        }
        //国家名称
        if (StringUtils.isEmpty(po.getGjmc())) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "校验失败，缺少国家名称数据");
        }
    }
}
