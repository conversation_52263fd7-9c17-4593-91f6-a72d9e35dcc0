package com.zjjcnt.project.ck.zzj.file;

import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.core.file.FileObjectService;
import com.zjjcnt.project.ck.core.file.domain.FileMeta;
import com.zjjcnt.project.ck.core.file.domain.FileObject;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYztyZpSjDTO;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjYztyZpSjService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 自助机一照通用照片数据保存数据库service
 *
 * <AUTHOR>
 * @date 2025-01-14 16:48:00
 */
@RequiredArgsConstructor
@Service
public class ZzjYztyZpFileObjectToDbServiceImpl implements FileObjectService {

    private final ZzjYztyZpSjService zzjYztyZpSjService;


    @Override
    public byte[] getData(FileMeta fileMeta) {
        if (StringUtils.isEmpty(fileMeta.getFileLocation())) {
            return new byte[0];
        }
        ZzjYztyZpSjDTO zzjYztyZpSjDTO = zzjYztyZpSjService.findById(fileMeta.getFileLocation());
        return Objects.isNull(zzjYztyZpSjDTO) ? new byte[0] : zzjYztyZpSjDTO.getZpsj();
    }

    @Override
    public String save(FileObject fileInfoData, String bizName) {
        ZzjYztyZpSjDTO zzjYztyZpSjDTO = new ZzjYztyZpSjDTO();
        zzjYztyZpSjDTO.setZpsj(fileInfoData.getData());
        return String.valueOf(zzjYztyZpSjService.insert(zzjYztyZpSjDTO).getId());
    }

    @Override
    public void delete(FileMeta fileMeta) {
        zzjYztyZpSjService.deleteById(fileMeta.getFileLocation());
    }

    @Override
    public String getStorageType() {
        return SysadminConstants.FILE_STORAGE_TYPE_DB;
    }
}
