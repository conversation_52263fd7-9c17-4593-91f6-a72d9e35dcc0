package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import cn.hutool.core.util.IdcardUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.ZTaofanConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.ZTaofanDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.ZTaofanDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.ZTaofanMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.ZTaofanService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 逃犯表 service
 *
 * <AUTHOR>
 * @date 2024-11-22 10:56:00
 */
@Service
public class ZTaofanServiceImpl  extends AbstractBaseServiceImpl<ZTaofanMapper, ZTaofanDO, ZTaofanDTO> implements ZTaofanService {

    private final ZTaofanConvert convert = ZTaofanConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZTaofanDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZTaofanDO::getSfzh, dto.getSfzh(), StringUtils.isNotEmpty(dto.getSfzh()));
        return query;
    }

    @Override
    public ZTaofanDTO convertToDTO(ZTaofanDO zTaofanDO) {
        return convert.convert(zTaofanDO);
    }

    @Override
    public ZTaofanDO convertToDO(ZTaofanDTO zTaofanDTO) {
        return convert.convertToDO(zTaofanDTO);
    }

    @Override
    public List<ZTaofanDTO> listBySfzh(String sfzh) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZTaofanDO::getSfzh, sfzh).or(ZTaofanDO::getSfzh).eq(IdcardUtil.convert18To15(sfzh));
        return convertToDTO(list(query));
    }

}
