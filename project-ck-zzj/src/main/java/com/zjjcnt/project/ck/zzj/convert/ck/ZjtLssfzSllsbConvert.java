package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSllsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzSllsbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzSllsbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzSllsbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzSllsbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzSllsbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzSllsbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtLssfzSllsbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 临时身份证受理历史表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtLssfzSllsbConvert {

    ZjtLssfzSllsbConvert INSTANCE = Mappers.getMapper(ZjtLssfzSllsbConvert.class);

    ZjtLssfzSllsbDTO convert(ZjtLssfzSllsbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtLssfzSllsbDO convertToDO(ZjtLssfzSllsbDTO dto);

    ZjtLssfzSllsbDTO convertToDTO(ZjtLssfzSllsbPageReq req);

    ZjtLssfzSllsbDTO convertToDTO(ZjtLssfzSllsbCreateReq req);

    ZjtLssfzSllsbDTO convertToDTO(ZjtLssfzSllsbUpdateReq req);

    ZjtLssfzSllsbPageResp convertToPageResp(ZjtLssfzSllsbDTO dto);

    ZjtLssfzSllsbViewResp convertToViewResp(ZjtLssfzSllsbDTO dto);

    ZjtLssfzSllsbCreateResp convertToCreateResp(ZjtLssfzSllsbDTO dto);

}
