package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.RxZpwjbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RxZpwjbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-15 16:18:06
*/
@Mapper
public interface RxZpwjbConvert {

    RxZpwjbConvert INSTANCE = Mappers.getMapper(RxZpwjbConvert.class);

    RxZpwjbDTO convert(RxZpwjbDO entity);

    @InheritInverseConfiguration(name="convert")
    RxZpwjbDO convertToDO(RxZpwjbDTO dto);

}
