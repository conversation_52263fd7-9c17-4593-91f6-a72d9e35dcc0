package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjywWjSjConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjywWjSjDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjywWjSjDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.HjywWjSjMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjywWjSjService;
import org.springframework.stereotype.Service;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 11:35:02
 */
@Service
public class HjywWjSjServiceImpl extends AbstractBaseServiceImpl<HjywWjSjMapper, HjywWjSjDO, HjywWjSjDTO> implements HjywWjSjService {

    HjywWjSjConvert convert = HjywWjSjConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(HjywWjSjDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        return query;
    }

    @Override
    public HjywWjSjDTO convertToDTO(HjywWjSjDO hjywWjSjDO) {
        return convert.convert(hjywWjSjDO);
    }

    @Override
    public HjywWjSjDO convertToDO(HjywWjSjDTO hjywWjSjDTO) {
        return convert.convertToDO(hjywWjSjDTO);
    }
}
