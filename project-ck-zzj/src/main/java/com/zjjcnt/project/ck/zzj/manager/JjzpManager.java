package com.zjjcnt.project.ck.zzj.manager;

import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.jk.ws.CxfClientCache;
import com.zjjcnt.project.ck.sysadmin.service.impl.XtXtkzcsbServiceImpl;
import com.zjjcnt.project.ck.zzj.domain.CkdshzJjzpDataDto;
import com.zjjcnt.project.ck.zzj.domain.CkdshzJjzpJsrzpDto;
import com.zjjcnt.project.ck.zzj.domain.JjzpUploadRequest;
import com.zjjcnt.project.ck.zzj.domain.ZlpzUploadRes;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.RzZlpzFsrzbDTO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RzZlpzFsrzbService;
import com.zjjcnt.project.ck.zzj.util.EncodeUtils;
import com.zjjcnt.project.ck.zzj.util.SerializeUtils;
import com.zjjcnt.project.ck.zzj.web.response.VoJjzpRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.endpoint.Client;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 调用交警综合应用平台外挂系统请求服务
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class JjzpManager {

    private final XtXtkzcsbServiceImpl xtXtkzcsbService;
    private final RzZlpzFsrzbService rzZlpzFsrzbService;

    /**
     * 调用交警接口，上传照片
     */
    @Transactional(rollbackFor = Exception.class, noRollbackFor = ServiceException.class)
    public ZlpzUploadRes saveToJjZp(JjzpUploadRequest request) throws ServiceException {

        CustomUserDetails user = SecurityUtils.getCurrentUser();
        request.setYhxm(user.getName());
        request.setYhgmsfhm(user.getIdCard());
        request.setYhid(String.valueOf(user.getUserId()));
        request.setYhip(user.getRemoteAddress());
        request.setDwjgdm(user.getDeptCode());
        request.setDwmc(user.getDeptName());

        //调用交警接口上传照片
        VoJjzpRes vofh = sendJjzp(request);

        boolean success = true;

        RzZlpzFsrzbDTO rkxtRzZlpzFsrzb = new RzZlpzFsrzbDTO();
        String nowtime = ServerTimeUtils.getCurrentTime();
        rkxtRzZlpzFsrzb.setFssj(nowtime);
        if ("1".equals(vofh.getCode())) {
            rkxtRzZlpzFsrzb.setFhdm("9");
        } else {
            success = false;
            rkxtRzZlpzFsrzb.setFhdm("3");
        }
        rkxtRzZlpzFsrzb.setFhms(vofh.getMessage());
        rkxtRzZlpzFsrzb.setDtbxt("42");
        rkxtRzZlpzFsrzb.setGmsfhm(request.getGmsfhm());

        rkxtRzZlpzFsrzb.setCzyid(user.getUserId());
        rkxtRzZlpzFsrzb.setCzyxm(user.getName());
        rkxtRzZlpzFsrzb.setSbid(request.getSbid());
        rzZlpzFsrzbService.insert(rkxtRzZlpzFsrzb);

        if (success) {
            return ZlpzUploadRes.success();
        } else {
            return ZlpzUploadRes.fail(vofh.getMessage());
        }
    }

    private VoJjzpRes sendJjzp(JjzpUploadRequest request) throws ServiceException {
        VoJjzpRes jjzpResult = new VoJjzpRes();

        try {
            String url = xtXtkzcsbService.findBzByKzlb("1623");
            if (StringUtils.isBlank(url)) {
                throw new ServiceException(CkZzjErrorCode.JJ_API_ERROR, "交警照片上传地址未配置");
            }
            String method = "writeObjectOutNew";

            //组织上传照片参数
            CkdshzJjzpJsrzpDto ckdshzJjzpJsrzpDto = new CkdshzJjzpJsrzpDto();
            CkdshzJjzpDataDto data = new CkdshzJjzpDataDto();
            data.addFieldUTF8("zp", request.getZp());//照片
            data.addFieldUTF8("sfzmhm", request.getGmsfhm());//身份证号码
            data.addFieldUTF8("xb", request.getXb());//性别
            ckdshzJjzpJsrzpDto.setData(data);
            String requestXml = SerializeUtils.toXML(ckdshzJjzpJsrzpDto, "GBK", true);

            //接口提供者的业务类别代码，如机动车登记业务：01，驾驶证管理业务：02，违法处理：03，事故处理：04，交警队平台：10，剧毒品业务：06
            String xtlb = "02";
            //许可证序列号
            String jkxlh = xtXtkzcsbService.findBzByKzlb("1624");//1624
            //5位，由系统类别＋一位级别代码＋两位接口顺序号组成(参照监管系统)，C：支队、B:总队，A：部局。如01A01代表机动车部局端提供的接口。定义时查询类接口顺序号取50以内的数字，写入接口顺序号定义为50以上的数字。
            String jkid = "02C77";
            //场景编码
            String cjsqbh = "";//场景编号
            //单位机构代码
            String dwjgdm = request.getDwjgdm() + "000";
            //单位名称
            String dwmc = request.getDwmc();
            //使用用户标识
            String yhbz = request.getYhgmsfhm() == null ? request.getYhid() : request.getYhgmsfhm(); //

            //使用用户标识
            String yhxm = request.getYhxm();

            //终端ip
            String zdbs = request.getYhip();

            Client client = CxfClientCache.getClientInstance(url);

            Object[] result = client.invoke(method,
                    xtlb,// 02;
                    jkxlh,// = "7F1A090900041704081589CB869D8A878583898C86C4E3AA93D5A2C7A49D4BA6ABCAA1CDB3D2BB29";
                    jkid,// = "02C77";
                    cjsqbh,//= "";//场景编号
                    dwjgdm,// = "33";
                    dwmc,//= EncodeUtils.encode("浙江省公安厅"); //
                    yhbz,//= "14030319610302419X"; //
                    yhxm,//= EncodeUtils.encode("张三"); //
                    zdbs,//= "************"; //
                    requestXml// = EncodeUtils.encode(requestXML);
            );

            String response = result == null || result.length < 1 ? null : String.valueOf(result[0]);

//            log.debug("交警照片上传接口反馈结果：" + response);

            CkdshzJjzpJsrzpDto responseResult = SerializeUtils.parseXML(response, CkdshzJjzpJsrzpDto.class);

//            log.info("交警照片上传接口反馈code{}和message{}", EncodeUtils.decode(responseResult.getHead().getCode()), EncodeUtils.decode(responseResult.getHead().getMessage()));

            jjzpResult.setCode(EncodeUtils.decode(responseResult.getHead().getCode()));
            jjzpResult.setMessage(EncodeUtils.decode(responseResult.getHead().getMessage()));

        } catch (Exception e) {
            log.error("调用交警接口错误", e);
            throw new ServiceException(CkZzjErrorCode.JJ_API_ERROR, e.getMessage());
        }

        return jjzpResult;
    }

}
