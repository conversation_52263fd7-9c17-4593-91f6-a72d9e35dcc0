package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.jk.ws.ZalwcxqqHjgljbxxResultVo;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxCzrkjbxxbViewQgResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxCzrkjbxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxCzrkjbxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 常住人口基本信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface HjxxCzrkjbxxbConvert {

    HjxxCzrkjbxxbConvert INSTANCE = Mappers.getMapper(HjxxCzrkjbxxbConvert.class);

    HjxxCzrkjbxxbDTO convert(HjxxCzrkjbxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    HjxxCzrkjbxxbDO convertToDO(HjxxCzrkjbxxbDTO dto);

    HjxxCzrkjbxxbViewResp convertToViewResp(HjxxCzrkjbxxbDTO dto);

    @Mapping(source = "hjdzQhnxxdz", target = "hjdz")
    HjxxCzrkjbxxbViewQgResp convertToResp(ZalwcxqqHjgljbxxResultVo zalwcxqqHjgljbxxResultVo);

}
