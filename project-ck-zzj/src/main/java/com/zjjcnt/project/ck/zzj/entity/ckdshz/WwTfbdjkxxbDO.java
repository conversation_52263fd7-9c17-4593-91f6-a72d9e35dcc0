package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 逃犯比对接口信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.WwTfbdjkxxbDTO
 */
@Data
@Table("ww_tfbdjkxxb")
public class WwTfbdjkxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -264091886689902575L;

    /**
     * 接口ID
     */
    @Id(keyType = KeyType.Auto)
    private Long jkid;

    /**
     * 接口数据源
     */
    @Column(value = "jkjndi")
    private String jkjndi;

    /**
     * 查询语句
     */
    @Column(value = "cxyj")
    private String cxyj;

    /**
     * 启用标志
     */
    @Column(value = "qybz")
    private String qybz;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 返回提示
     */
    @Column(value = "fhts")
    private String fhts;


    @Override
    public Long getId() {
        return this.jkid;
    }

    @Override
    public void setId(Long id) {
        this.jkid = id;
    }
}
