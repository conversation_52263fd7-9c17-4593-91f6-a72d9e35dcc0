package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 指纹采集表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjbDTO
 */
@Crypto
@Data
@Table("zjt_zwcjb")
public class ZjtZwcjbDO implements IdEntity<String> {
    private static final long serialVersionUID = 1941595973164885611L;

    /**
     * 指纹图像ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String zwtxid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 指纹采集结果代码
     */
    @Column(value = "zwcjjgdm")
    private String zwcjjgdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "szyczkdm")
    private String szyczkdm;

    /**
     * 指纹一注册结果
     */
    @Column(value = "zwyzcjg")
    private String zwyzcjg;

    /**
     * 指纹一指位
     */
    @Column(value = "zwyzw")
    private String zwyzw;

    /**
     * 指纹一指纹图像文件编号
     */
    @Column(value = "zwytxwjbh")
    private String zwytxwjbh;

    /**
     * 指纹一图像质量值
     */
    @Column(value = "zwytxzlz")
    private BigDecimal zwytxzlz;

    /**
     * 指纹一指纹特征数据
     */
    @Column(value = "zwyzwtzsj")
    private byte[] zwyzwtzsj;

    /**
     * 指纹二注册结果
     */
    @Column(value = "zwezcjg")
    private String zwezcjg;

    /**
     * 指纹二指位
     */
    @Column(value = "zwezw")
    private String zwezw;

    /**
     * 指纹二指纹图像文件编号
     */
    @Column(value = "zwetxwjbh")
    private String zwetxwjbh;

    /**
     * 指纹二图像质量值
     */
    @Column(value = "zwetxzlz")
    private BigDecimal zwetxzlz;

    /**
     * 指纹二指纹特征数据
     */
    @Column(value = "zwezwtzsj")
    private byte[] zwezwtzsj;

    /**
     * 指纹前端系统注册号
     */
    @Column(value = "zwqdxtzch")
    private String zwqdxtzch;

    /**
     * 指纹采集器id
     */
    @Column(value = "zwcjqid")
    private String zwcjqid;

    /**
     * 指纹算法版本号
     */
    @Column(value = "zwsfbbh")
    private String zwsfbbh;

    /**
     * 指纹算法开发者代码
     */
    @Column(value = "zwsfkfzdm")
    private String zwsfkfzdm;

    /**
     * 操作人ID
     */
    @Column(value = "czrid")
    private String czrid;

    /**
     * 保存时间
     */
    @Column(value = "bcsj")
    private String bcsj;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     * 操作员单位代码
     */
    @Column(value = "czydwdm")
    private String czydwdm;

    /**
     * 操作员单位名称
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     * 上报时间
     */
    @Column(value = "sbsj")
    private String sbsj;

    /**
     * 受理号
     */
    @Column(value = "slh")
    private String slh;

    /**
     * 数据归属单位代码
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     * 采集时间
     */
    @Column(value = "cjsj")
    private String cjsj;


    @Override
    public String getId() {
        return this.zwtxid;
    }

    @Override
    public void setId(String id) {
        this.zwtxid = id;
    }
}
