package com.zjjcnt.project.ck.zzj.domain;

import lombok.Data;

@Data
public class JjzpUploadRequest {
    /**
     * 照片数据
     */
    private String zp;

    /**
     * 身份号码
     */
    private String gmsfhm;

    /**
     * 性别
     */
    private String xb;


    /**
     * 操作员身份号码
     */
    private String yhgmsfhm;

    /**
     * 操作员单位机构代码
     */
    private String dwjgdm;

    /**
     * 操作员单位机构名称
     */
    private String dwmc;

    /**
     * 操作员姓名
     */
    private String yhxm;

    /**
     * 操作员ip
     */
    private String yhip;

    /**
     * 操作员id
     */
    private String yhid;

    /**
     * 设备id
     */
    private Long sbid;
}
