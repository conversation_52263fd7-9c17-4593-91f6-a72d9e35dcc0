package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.WwJkcsbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwJkcsbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.WwJkcsbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.WwJkcsbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.WwJkcsbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 外围接口参数表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@Service
public class WwJkcsbServiceImpl extends AbstractBaseServiceImpl<WwJkcsbMapper, WwJkcsbDO, WwJkcsbDTO> implements WwJkcsbService {

    WwJkcsbConvert convert = WwJkcsbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(WwJkcsbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(WwJkcsbDO::getJklb, dto.getJklb(), StringUtils.isNotEmpty(dto.getJklb()));
        query.eq(WwJkcsbDO::getJklbmc, dto.getJklbmc(), StringUtils.isNotEmpty(dto.getJklbmc()));
        query.eq(WwJkcsbDO::getJklmc, dto.getJklmc(), StringUtils.isNotEmpty(dto.getJklmc()));
        query.eq(WwJkcsbDO::getJkcs, dto.getJkcs(), StringUtils.isNotEmpty(dto.getJkcs()));
        query.eq(WwJkcsbDO::getJglx, dto.getJglx(), StringUtils.isNotEmpty(dto.getJglx()));
        query.eq(WwJkcsbDO::getQybz, dto.getQybz(), StringUtils.isNotEmpty(dto.getQybz()));
        query.eq(WwJkcsbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        return query;
    }

    @Override
    public WwJkcsbDTO convertToDTO(WwJkcsbDO wwJkcsbDO) {
        return convert.convert(wwJkcsbDO);
    }

    @Override
    public WwJkcsbDO convertToDO(WwJkcsbDTO wwJkcsbDTO) {
        return convert.convertToDO(wwJkcsbDTO);
    }
}
