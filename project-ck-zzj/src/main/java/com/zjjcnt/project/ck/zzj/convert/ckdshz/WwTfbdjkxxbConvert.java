package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwTfbdjkxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwTfbdjkxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwTfbdjkxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwTfbdjkxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwTfbdjkxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwTfbdjkxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwTfbdjkxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.WwTfbdjkxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 逃犯比对接口信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface WwTfbdjkxxbConvert {

    WwTfbdjkxxbConvert INSTANCE = Mappers.getMapper(WwTfbdjkxxbConvert.class);

    WwTfbdjkxxbDTO convert(WwTfbdjkxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    WwTfbdjkxxbDO convertToDO(WwTfbdjkxxbDTO dto);

    WwTfbdjkxxbDTO convertToDTO(WwTfbdjkxxbPageReq req);

    WwTfbdjkxxbDTO convertToDTO(WwTfbdjkxxbCreateReq req);

    WwTfbdjkxxbDTO convertToDTO(WwTfbdjkxxbUpdateReq req);

    WwTfbdjkxxbPageResp convertToPageResp(WwTfbdjkxxbDTO dto);

    WwTfbdjkxxbViewResp convertToViewResp(WwTfbdjkxxbDTO dto);

    WwTfbdjkxxbCreateResp convertToCreateResp(WwTfbdjkxxbDTO dto);

}
