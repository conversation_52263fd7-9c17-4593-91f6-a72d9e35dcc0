package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.annotation.CacheControl;
import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjYztyZpConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYztyZpDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYztyZpCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYztyZpPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYztyZpCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYztyZpPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYztyZpViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjYztyZpService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 自助机一照通用照片前端控制器
 *
 * <AUTHOR>
 * @date 2025-01-14 16:13:45
 */
@Slf4j
@RequiredArgsConstructor
@Tag(name = "自助机一照通用照片")
@RestController
@RequestMapping("/zzjYztyZp")
public class ZzjYztyZpController extends AbstractCrudController<ZzjYztyZpDTO> {

    private final ZzjYztyZpService zzjYztyZpService;

    @Override
    protected IBaseService<ZzjYztyZpDTO> getService() {
        return zzjYztyZpService;
    }

    @GetMapping("page")
    @Operation(summary = "查询自助机一照通用照片")
    public CommonResult<PageResult<ZzjYztyZpPageResp>> page(ZzjYztyZpPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZzjYztyZpConvert.INSTANCE::convertToDTO, ZzjYztyZpConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看自助机一照通用照片详情")
    public CommonResult<ZzjYztyZpViewResp> view(Long id) {
        return super.view(id, ZzjYztyZpConvert.INSTANCE::convertToViewResp);
    }

    @PostMapping("create")
    @Operation(summary = "新增自助机一照通用照片")
    public CommonResult<ZzjYztyZpCreateResp> create(@Validated @RequestBody ZzjYztyZpCreateReq req) {
        return super.create(req, ZzjYztyZpConvert.INSTANCE::convertToDTO, ZzjYztyZpConvert.INSTANCE::convertToCreateResp);
    }

//    @PostMapping("update")
//    @Operation(summary = "编辑自助机一照通用照片")
//    public CommonResult<Boolean> update(@RequestBody ZzjYztyZpUpdateReq req) {
//        return super.update(req, ZzjYztyZpConvert.INSTANCE::convertToDTO);
//    }
//

    @DeleteMapping(value = "delete")
    @Operation(summary = "删除自助机一照通用照片")
    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
        return super.delete(id);
    }

    @PostMapping(value = "exportPhoto")
    @Operation(summary = "导出照片")
    public void delete(@RequestBody Long[] id, HttpServletResponse response) {
        ZzjYztyZpDTO query = new ZzjYztyZpDTO();
        query.setIdList(Arrays.asList(id));
        List<ZzjYztyZpDTO> list = zzjYztyZpService.list(query);

        String zipName = DateTimeUtils.now() + ".zip";

        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + zipName);
        response.setCharacterEncoding(StandardCharsets.UTF_8.displayName());

        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            for (ZzjYztyZpDTO zzjYztyZpDTO : list) {
                String fileName = zzjYztyZpDTO.getZjhm() + Constants.UNDER_LINE + zzjYztyZpDTO.getBcsj() + ".jpg";
                zipOutputStream.putNextEntry(new ZipEntry(fileName));
                byte[] imageData = zzjYztyZpService.getImageData(zzjYztyZpDTO.getId());
                try (InputStream inputStream = new ByteArrayInputStream(imageData)) {
                    byte[] buf = new byte[1024];
                    int len;
                    while ((len = inputStream.read(buf)) != -1) {
                        zipOutputStream.write(buf, 0, len);
                    }
                    zipOutputStream.closeEntry();
                }
            }
        } catch (IOException e) {
            log.error("导出一照通用照片错误", e);
        }
    }

    @CacheControl(maxAge = 60 * 60 * 24)
    @Operation(summary = "访问文件")
    @GetMapping("/imageBase64")
    public CommonResult<String> image(@RequestParam(value = "zpid") Long zpid) {
        byte[] imageData = zzjYztyZpService.getImageData(zpid);
        if (imageData == null) {
            return CommonResult.success("");
        }
        return CommonResult.success(Base64.getEncoder().encodeToString(imageData));
    }

//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(ZzjYztyZpExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出自助机一照通用照片")
//    public void export(ZzjYztyZpPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "自助机一照通用照片" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, ZzjYztyZpExp.class,
//                    ZzjYztyZpConvert.INSTANCE::convertToDTO, ZzjYztyZpConvert.INSTANCE::convertToExp, response);
//    }

}
