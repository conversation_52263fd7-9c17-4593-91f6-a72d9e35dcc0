package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.project.ck.core.file.CkFileManager;
import com.zjjcnt.project.ck.core.file.domain.DefaultFileObject;
import com.zjjcnt.project.ck.core.file.domain.FileMeta;
import com.zjjcnt.project.ck.core.file.domain.FileObject;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import com.mybatisflex.core.query.QueryWrapper;

import com.zjjcnt.project.ck.zzj.service.ck.ZjtRxxxbService;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtRxxxbDO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtRxxxbDTO;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtRxxxbConvert;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtRxxxbMapper;

import com.zjjcnt.common.core.utils.ColumnUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import java.util.Base64;

/**
 * 人像信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Slf4j
@Service
public class ZjtRxxxbServiceImpl extends ExBaseServiceImpl<ZjtRxxxbMapper, ZjtRxxxbDO, ZjtRxxxbDTO> implements ZjtRxxxbService {

    ZjtRxxxbConvert convert = ZjtRxxxbConvert.INSTANCE;

    @Autowired
    private CkFileManager fileManager;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtRxxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtRxxxbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtRxxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtRxxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtRxxxbDO::getZpwjbh, dto.getZpwjbh(), StringUtils.isNotEmpty(dto.getZpwjbh()));
        query.ge(ZjtRxxxbDO::getLrrq, dto.getLrrqStart(), StringUtils.isNotEmpty(dto.getLrrqStart()));
        query.le(ZjtRxxxbDO::getLrrq, dto.getLrrqEnd(), StringUtils.isNotEmpty(dto.getLrrqEnd()));
        query.eq(ZjtRxxxbDO::getCyzjdm, dto.getCyzjdm(), StringUtils.isNotEmpty(dto.getCyzjdm()));
        query.eq(ZjtRxxxbDO::getZjhm, dto.getZjhm(), StringUtils.isNotEmpty(dto.getZjhm()));
        query.eq(ZjtRxxxbDO::getWwx, dto.getWwx(), StringUtils.isNotEmpty(dto.getWwx()));
        query.eq(ZjtRxxxbDO::getWwm, dto.getWwm(), StringUtils.isNotEmpty(dto.getWwm()));
        query.eq(ZjtRxxxbDO::getXplx, dto.getXplx(), StringUtils.isNotEmpty(dto.getXplx()));
        query.eq(ZjtRxxxbDO::getYwlb, dto.getYwlb(), StringUtils.isNotEmpty(dto.getYwlb()));
        query.eq(ZjtRxxxbDO::getYwlsh, dto.getYwlsh(), StringUtils.isNotEmpty(dto.getYwlsh()));
        return query;
    }

    @Override
    public ZjtRxxxbDTO convertToDTO(ZjtRxxxbDO zjtRxxxbDO) {
        return convert.convert(zjtRxxxbDO);
    }

    @Override
    public ZjtRxxxbDO convertToDO(ZjtRxxxbDTO zjtRxxxbDTO) {
        return convert.convertToDO(zjtRxxxbDTO);
    }


    @Override
    protected void beforeInsert(ZjtRxxxbDTO entity) throws ServiceException {
        super.beforeInsert(entity);
        if (entity == null || StringUtils.isBlank(entity.getBase64zp())) {
            throw new ServiceException(500, "保存人像照片时照片不能为空.");
        }
        if (StringUtils.isBlank(entity.getLrrq())) {
            entity.setLrrq(ServerTimeUtils.getCurrentTime());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtRxxxbDTO insert(ZjtRxxxbDTO entity) throws ServiceException {
        ZjtRxxxbDTO result = super.insert(entity);
        try {
            byte[] data = Base64.getDecoder().decode(entity.getBase64zp());
            FileObject fileObject = new DefaultFileObject(data, MediaType.IMAGE_JPEG_VALUE, entity.getGmsfhm());
            String zpwjbh = fileManager.save(getBizTable(), result.getZpid(), fileObject);
            result.setZpwjbh(zpwjbh);
            update(result);
        } catch (Exception e) {
            String message = "保存人像照片发生错误：";
            log.error(message, e);
            throw new ServiceException(500, message + e.getMessage());
        }
        return result;
    }


    /**
     * 根据文件编号获取Base64照片
     * @return
     */
    @Override
    public String getBase64Zp(String zpwjbg) {
        if (StringUtils.isEmpty(zpwjbg)) {
            return null;
        }
        FileMeta fileMeta = fileManager.get(zpwjbg);
        if (fileMeta == null) {
            return null;
        }
        byte[] data = fileManager.getData(fileMeta);
        if (ArrayUtils.isEmpty(data)) {
            return null;
        }
        return Base64.getEncoder().encodeToString(data);
    }
}
