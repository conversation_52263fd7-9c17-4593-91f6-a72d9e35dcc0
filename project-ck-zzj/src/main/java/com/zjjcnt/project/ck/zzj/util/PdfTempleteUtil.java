package com.zjjcnt.project.ck.zzj.util;

import com.deepoove.poi.XWPFTemplate;
import com.lowagie.text.pdf.BaseFont;
import com.zjjcnt.common.core.exception.ServiceException;
import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Base64;
import java.util.Map;

/**
 * PDF工具类
 * 增加水印功能
 */
@Slf4j
public class PdfTempleteUtil {

    public static String getBase64(Map<String, Object> map, String pdftemplate) {
        return Base64.getEncoder().encodeToString(handleParams(map, pdftemplate));
    }

    public static byte[] getBytes(Map<String, Object> map, String pdftemplate) {
        return handleParams(map, pdftemplate);
    }

    public static byte[] handleParams(Map<String, Object> map, String pdftemplate) {
        try (InputStream templateInputStream = new ClassPathResource(pdftemplate).getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            wordToPdf(map, templateInputStream, outputStream);

            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("获取默认模版错误", e);
        }
        return new byte[]{};
    }

    public static void wordToPdf(Map<String, Object> paramMap, InputStream templateInputStream, OutputStream outputStream) {
        XWPFTemplate template = XWPFTemplate.compile(templateInputStream).render(paramMap);
        PdfOptions options = PdfOptions.create();
        options.fontProvider((familyName, encoding, size, style, color) -> {
            try {
                BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                com.lowagie.text.Font fontChinese = new com.lowagie.text.Font(bfChinese, size, style, color);
                if (familyName != null) {
                    fontChinese.setFamily(familyName);
                }
                return fontChinese;
            } catch (Exception e) {
                log.error("创建字体错误", e);
                throw new ServiceException(500, "创建字体错误");
            }
        });

        try {
            PdfConverter.getInstance().convert(template.getXWPFDocument(), outputStream, options);
        } catch (IOException e) {
            log.error("word输出错误", e);
            throw new ServiceException(500, "word输出错误");
        }

    }

}
