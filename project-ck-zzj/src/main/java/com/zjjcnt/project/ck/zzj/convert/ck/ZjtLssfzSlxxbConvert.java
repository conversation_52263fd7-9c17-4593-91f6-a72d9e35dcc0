package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzSlxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzSlxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzSlxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzSlxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzSlxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzSlxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtLssfzSlxxbDO;
import com.zjjcnt.project.ck.zzj.web.request.ZjtLssfzReq;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
* 临时身份证受理信息表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtLssfzSlxxbConvert {

    ZjtLssfzSlxxbConvert INSTANCE = Mappers.getMapper(ZjtLssfzSlxxbConvert.class);

    ZjtLssfzSlxxbDTO convert(ZjtLssfzSlxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtLssfzSlxxbDO convertToDO(ZjtLssfzSlxxbDTO dto);

    ZjtLssfzSlxxbDTO convertToDTO(ZjtLssfzSlxxbPageReq req);

    ZjtLssfzSlxxbDTO convertToDTO(ZjtLssfzSlxxbCreateReq req);

    @Mapping(ignore = true, target = "nbsfzid")
    @Mapping(ignore = true, target = "id")
    @Mapping(source = "slh", target = "jmsfzslh")
    ZjtLssfzSlxxbDTO convertToDTO(ZjtSlxxbDTO zjtSlxxbDTO);

    void convertToDTO(ZjtLssfzReq zjtLssfzReq, @MappingTarget ZjtLssfzSlxxbDTO dto);

    ZjtLssfzSlxxbDTO convertToDTO(ZjtLssfzSlxxbUpdateReq req);

    ZjtLssfzSlxxbPageResp convertToPageResp(ZjtLssfzSlxxbDTO dto);

    ZjtLssfzSlxxbViewResp convertToViewResp(ZjtLssfzSlxxbDTO dto);

    ZjtLssfzSlxxbCreateResp convertToCreateResp(ZjtLssfzSlxxbDTO dto);

}
