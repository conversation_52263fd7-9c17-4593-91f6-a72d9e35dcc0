package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxMlpxxxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxMlpxxxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxMlpxxxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.HjxxMlpxxxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxMlpxxxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 门（楼）牌详细信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@Service
public class HjxxMlpxxxxbServiceImpl extends AbstractBaseServiceImpl<HjxxMlpxxxxbMapper, HjxxMlpxxxxbDO, HjxxMlpxxxxbDTO> implements HjxxMlpxxxxbService {

    HjxxMlpxxxxbConvert convert = HjxxMlpxxxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(HjxxMlpxxxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(HjxxMlpxxxxbDO::getMlpid, dto.getMlpid(), Objects.nonNull(dto.getMlpid()));
        query.likeLeft(HjxxMlpxxxxbDO::getSsxq, dto.getSsxq(), StringUtils.isNotEmpty(dto.getSsxq()));
        query.eq(HjxxMlpxxxxbDO::getJlx, dto.getJlx(), Objects.nonNull(dto.getJlx()));
        query.eq(HjxxMlpxxxxbDO::getMlph, dto.getMlph(), Objects.nonNull(dto.getMlph()));
        query.eq(HjxxMlpxxxxbDO::getMlxz, dto.getMlxz(), Objects.nonNull(dto.getMlxz()));
        query.eq(HjxxMlpxxxxbDO::getPcs, dto.getPcs(), Objects.nonNull(dto.getPcs()));
        query.eq(HjxxMlpxxxxbDO::getZrq, dto.getZrq(), Objects.nonNull(dto.getZrq()));
        query.eq(HjxxMlpxxxxbDO::getXzjd, dto.getXzjd(), Objects.nonNull(dto.getXzjd()));
        query.eq(HjxxMlpxxxxbDO::getJcwh, dto.getJcwh(), Objects.nonNull(dto.getJcwh()));
        query.eq(HjxxMlpxxxxbDO::getJdlb, dto.getJdlb(), Objects.nonNull(dto.getJdlb()));
        query.eq(HjxxMlpxxxxbDO::getCdlb, dto.getCdlb(), Objects.nonNull(dto.getCdlb()));
        query.ge(HjxxMlpxxxxbDO::getJdsj, dto.getJdsjStart(), StringUtils.isNotEmpty(dto.getJdsjStart()));
        query.le(HjxxMlpxxxxbDO::getJdsj, dto.getJdsjEnd(), StringUtils.isNotEmpty(dto.getJdsjEnd()));
        query.ge(HjxxMlpxxxxbDO::getCdsj, dto.getCdsjStart(), StringUtils.isNotEmpty(dto.getCdsjStart()));
        query.le(HjxxMlpxxxxbDO::getCdsj, dto.getCdsjEnd(), StringUtils.isNotEmpty(dto.getCdsjEnd()));
        query.eq(HjxxMlpxxxxbDO::getCjhjywid, dto.getCjhjywid(), Objects.nonNull(dto.getCjhjywid()));
        query.eq(HjxxMlpxxxxbDO::getCchjywid, dto.getCchjywid(), Objects.nonNull(dto.getCchjywid()));
        query.eq(HjxxMlpxxxxbDO::getMlpzt, dto.getMlpzt(), Objects.nonNull(dto.getMlpzt()));
        query.eq(HjxxMlpxxxxbDO::getLxdbid, dto.getLxdbid(), Objects.nonNull(dto.getLxdbid()));
        query.eq(HjxxMlpxxxxbDO::getJlbz, dto.getJlbz(), Objects.nonNull(dto.getJlbz()));
        query.ge(HjxxMlpxxxxbDO::getQysj, dto.getQysjStart(), StringUtils.isNotEmpty(dto.getQysjStart()));
        query.le(HjxxMlpxxxxbDO::getQysj, dto.getQysjEnd(), StringUtils.isNotEmpty(dto.getQysjEnd()));
        query.ge(HjxxMlpxxxxbDO::getJssj, dto.getJssjStart(), StringUtils.isNotEmpty(dto.getJssjStart()));
        query.le(HjxxMlpxxxxbDO::getJssj, dto.getJssjEnd(), StringUtils.isNotEmpty(dto.getJssjEnd()));
        query.eq(HjxxMlpxxxxbDO::getPxh, dto.getPxh(), Objects.nonNull(dto.getPxh()));
        query.eq(HjxxMlpxxxxbDO::getTjyxzqh, dto.getTjyxzqh(), Objects.nonNull(dto.getTjyxzqh()));
        query.eq(HjxxMlpxxxxbDO::getCxsx, dto.getCxsx(), Objects.nonNull(dto.getCxsx()));
        query.eq(HjxxMlpxxxxbDO::getHjxz, dto.getHjxz(), Objects.nonNull(dto.getHjxz()));
        return query;
    }

    @Override
    public HjxxMlpxxxxbDTO convertToDTO(HjxxMlpxxxxbDO hjxxMlpxxxxbDO) {
        return convert.convert(hjxxMlpxxxxbDO);
    }

    @Override
    public HjxxMlpxxxxbDO convertToDO(HjxxMlpxxxxbDTO hjxxMlpxxxxbDTO) {
        return convert.convertToDO(hjxxMlpxxxxbDTO);
    }
}
