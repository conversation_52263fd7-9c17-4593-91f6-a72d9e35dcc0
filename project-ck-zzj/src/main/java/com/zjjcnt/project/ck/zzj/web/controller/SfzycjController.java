package com.zjjcnt.project.ck.zzj.web.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.project.ck.zzj.convert.SfzycjConvert;
import com.zjjcnt.project.ck.zzj.third.sfzycj.SfzYcjClient;
import com.zjjcnt.project.ck.zzj.third.sfzycj.resp.SfzycjFhxxResp;
import com.zjjcnt.project.ck.zzj.web.response.SfzycjxxResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 身份证预采集controller
 *
 * <AUTHOR>
 * @date 2025-05-09 16:15:00
 */
@RequiredArgsConstructor
@Tag(name = "身份证预采集")
@RestController
@RequestMapping("/sfzycj")
public class SfzycjController {

    private final SfzYcjClient sfzYcjClient;

    @GetMapping("sfzycjxx")
    @Operation(summary = "获取身份证预采集信息")
    public CommonResult<SfzycjxxResp> getSfzycjxx(@RequestParam String gmsfhm) {
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        SfzycjFhxxResp sfzycjxxForDzsfz = sfzYcjClient.getSfzycjxxForDzsfz(gmsfhm,
                LocalDateTimeUtil.format(startTime, DateTimeUtils.CHAR_DATETIME_PATTERN), "");
        return CommonResult.success(SfzycjConvert.INSTANCE.convertToSfzycjxxResp(sfzycjxxForDzsfz));
    }
}
