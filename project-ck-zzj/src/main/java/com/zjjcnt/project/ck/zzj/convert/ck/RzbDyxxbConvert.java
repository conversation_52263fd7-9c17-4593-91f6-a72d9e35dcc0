package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.RzbDyxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.RzbDyxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.RzbDyxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.RzbDyxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.RzbDyxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.RzbDyxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.RzbDyxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.RzbDyxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 打印信息表Convert
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Mapper
public interface RzbDyxxbConvert {

    RzbDyxxbConvert INSTANCE = Mappers.getMapper(RzbDyxxbConvert.class);

    RzbDyxxbDTO convert(RzbDyxxbDO entity);

    @InheritInverseConfiguration(name = "convert")
    RzbDyxxbDO convertToDO(RzbDyxxbDTO dto);

    RzbDyxxbDTO convertToDTO(RzbDyxxbPageReq req);

    RzbDyxxbDTO convertToDTO(RzbDyxxbCreateReq req);

    RzbDyxxbDTO convertToDTO(RzbDyxxbUpdateReq req);

    RzbDyxxbPageResp convertToPageResp(RzbDyxxbDTO dto);

    RzbDyxxbViewResp convertToViewResp(RzbDyxxbDTO dto);

    RzbDyxxbCreateResp convertToCreateResp(RzbDyxxbDTO dto);

}
