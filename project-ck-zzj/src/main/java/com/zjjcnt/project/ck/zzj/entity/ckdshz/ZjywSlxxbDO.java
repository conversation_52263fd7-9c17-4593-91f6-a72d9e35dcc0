package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 受理信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.ZjywSlxxbDTO
 */
@Crypto
@Data
@Table("zjyw_slxxb")
public class ZjywSlxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = 4494648572876329209L;

    /**
     * 内部受理ID
     */
    @Id(keyType = KeyType.Auto)
    private Long nbslid;

    /**
     * 受理号
     */
    @Column(value = "slh")
    private String slh;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private Long ryid;

    /**
     * 人员内部ID
     */
    @Column(value = "rynbid")
    private Long rynbid;

    /**
     * 照片ID
     */
    @Column(value = "zpid")
    private Long zpid;

    /**
     * 签发机关
     */
    @Column(value = "qfjg")
    private String qfjg;

    /**
     * 有效期限起始日期
     */
    @Column(value = "yxqxqsrq")
    private String yxqxqsrq;

    /**
     * 有效期限截止日期
     */
    @Column(value = "yxqxjzrq")
    private String yxqxjzrq;

    /**
     * 住址
     */
    @Column(value = "zz")
    private String zz;

    /**
     * 申领原因
     */
    @Column(value = "slyy")
    private String slyy;

    /**
     * 制证类型
     */
    @Column(value = "zzlx")
    private String zzlx;

    /**
     * 领证方式
     */
    @Column(value = "lqfs")
    private String lqfs;

    /**
     * 收费类型
     */
    @Column(value = "sflx")
    private String sflx;

    /**
     * 收费金额
     */
    @Column(value = "sfje")
    private Long sfje;

    /**
     * 数据包流水号
     */
    @Column(value = "sjblsh")
    private String sjblsh;

    /**
     * 受理状态
     */
    @Column(value = "slzt")
    private String slzt;

    /**
     * 证件业务ID
     */
    @Column(value = "zjywid")
    private Long zjywid;

    /**
     * 冲销标志
     */
    @Column(value = "cxbz")
    private String cxbz;

    /**
     * 冲销时间
     */
    @Column(value = "cxsj")
    private String cxsj;

    /**
     * 冲销人ID
     */
    @Column(value = "cxrid")
    private Long cxrid;

    /**
     * 冲销证件业务ID
     */
    @Column(value = "cxzjywid")
    private Long cxzjywid;

    /**
     * 同步标志
     */
    @Column(value = "tbbz")
    private Long tbbz;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 内部身份证ID
     */
    @Column(value = "nbsfzid")
    private Long nbsfzid;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 民族
     */
    @Column(value = "mz")
    private String mz;

    /**
     * 出生日期
     */
    @Column(value = "csrq")
    private String csrq;

    /**
     * 出生地省市县（区）
     */
    @Column(value = "csdssxq")
    private String csdssxq;

    /**
     * 门（楼）牌内部ID
     */
    @Column(value = "mlpnbid")
    private Long mlpnbid;

    /**
     * 省市县（区）
     */
    @Column(value = "ssxq")
    private String ssxq;

    /**
     * 街路巷
     */
    @Column(value = "jlx")
    private String jlx;

    /**
     * 门（楼）牌号
     */
    @Column(value = "mlph")
    private String mlph;

    /**
     * 门（楼）详址
     */
    @Column(value = "mlxz")
    private String mlxz;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 责任区
     */
    @Column(value = "zrq")
    private String zrq;

    /**
     * 乡镇（街道）
     */
    @Column(value = "xzjd")
    private String xzjd;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 排序号
     */
    @Column(value = "pxh")
    private String pxh;

    /**
     * 业务标志
     */
    @Column(value = "ywbz")
    private String ywbz;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private Long czyid;

    /**
     * 操作时间
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 单位代码
     */
    @Column(value = "dwdm")
    private String dwdm;

    /**
     * 收件人姓名
     */
    @Crypto
    @Column(value = "sjrxm")
    private String sjrxm;

    /**
     * 收件人联系电话
     */
    @Crypto
    @Column(value = "sjrlxdh")
    private String sjrlxdh;

    /**
     * 收件人邮编
     */
    @Column(value = "sjryb")
    private String sjryb;

    /**
     * 收件人通讯地址
     */
    @Column(value = "sjrtxdz")
    private String sjrtxdz;

    /**
     * 制证信息错误类别
     */
    @Column(value = "zzxxcwlb")
    private String zzxxcwlb;

    /**
     * 错误描述
     */
    @Column(value = "cwms")
    private String cwms;

    /**
     * 检验单位
     */
    @Column(value = "jydw")
    private String jydw;

    /**
     * 检验人姓名
     */
    @Crypto
    @Column(value = "jyrxm")
    private String jyrxm;

    /**
     * 检验日期
     */
    @Column(value = "jyrq")
    private String jyrq;

    /**
     * 处理单位
     */
    @Column(value = "cldw")
    private String cldw;

    /**
     * 处理情况
     */
    @Column(value = "clqk")
    private String clqk;

    /**
     * 处理日期
     */
    @Column(value = "clrq")
    private String clrq;

    /**
     * 质量回馈状态
     */
    @Column(value = "zlhkzt")
    private String zlhkzt;

    /**
     * 回馈时间
     */
    @Column(value = "hksj")
    private String hksj;

    /**
     *
     */
    @Column(value = "bwbha")
    private String bwbha;

    /**
     *
     */
    @Column(value = "bwbhb")
    private String bwbhb;

    /**
     * 审核日期
     */
    @Column(value = "shrq")
    private String shrq;

    /**
     * 省厅接收时间
     */
    @Column(value = "stjssj")
    private String stjssj;

    /**
     *
     */
    @Column(value = "bwbhd")
    private String bwbhd;

    /**
     * 分拣批次号
     */
    @Column(value = "fjpch")
    private String fjpch;

    /**
     *
     */
    @Column(value = "rlbdid")
    private Long rlbdid;

    /**
     * 人脸比对ID
     */
    @Column(value = "rlbdbz")
    private String rlbdbz;

    /**
     * 人脸比对标志
     */
    @Column(value = "rlbdsj")
    private String rlbdsj;

    /**
     * 人脸比对时间
     */
    @Column(value = "zwyzw")
    private String zwyzw;

    /**
     * 指纹一指位
     */
    @Column(value = "zwyzcjg")
    private String zwyzcjg;

    /**
     * 指纹一注册结果
     */
    @Column(value = "zwezw")
    private String zwezw;

    /**
     * 指纹二指位
     */
    @Column(value = "zwezcjg")
    private String zwezcjg;

    /**
     * 指纹二注册结果
     */
    @Column(value = "zwcjjgdm")
    private String zwcjjgdm;

    /**
     * 指纹采集结果代码
     */
    @Column(value = "szyczkdm")
    private String szyczkdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "sfzwzj")
    private String sfzwzj;

    /**
     *
     */
    @Column(value = "dztbbz")
    private Long dztbbz;

    /**
     * 对账同步标志
     */
    @Column(value = "dztbsj")
    private String dztbsj;

    /**
     * 对账同步时间
     */
    @Column(value = "dzsjbbh")
    private String dzsjbbh;

    /**
     * 对账数据包编号
     */
    @Column(value = "slfs")
    private String slfs;


    @Override
    public Long getId() {
        return this.nbslid;
    }

    @Override
    public void setId(Long id) {
        this.nbslid = id;
    }
}
