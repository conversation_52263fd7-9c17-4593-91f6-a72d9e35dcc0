package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzDyxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzDyxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzDyxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzDyxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzDyxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzDyxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzDyxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtLssfzDyxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 临时身份证打印信息表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtLssfzDyxxbConvert {

    ZjtLssfzDyxxbConvert INSTANCE = Mappers.getMapper(ZjtLssfzDyxxbConvert.class);

    ZjtLssfzDyxxbDTO convert(ZjtLssfzDyxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtLssfzDyxxbDO convertToDO(ZjtLssfzDyxxbDTO dto);

    ZjtLssfzDyxxbDTO convertToDTO(ZjtLssfzDyxxbPageReq req);

    ZjtLssfzDyxxbDTO convertToDTO(ZjtLssfzDyxxbCreateReq req);

    ZjtLssfzDyxxbDTO convertToDTO(ZjtLssfzDyxxbUpdateReq req);

    ZjtLssfzDyxxbPageResp convertToPageResp(ZjtLssfzDyxxbDTO dto);

    ZjtLssfzDyxxbViewResp convertToViewResp(ZjtLssfzDyxxbDTO dto);

    ZjtLssfzDyxxbCreateResp convertToCreateResp(ZjtLssfzDyxxbDTO dto);

}
