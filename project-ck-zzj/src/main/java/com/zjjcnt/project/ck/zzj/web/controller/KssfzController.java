package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.project.ck.zzj.manager.KssfzManager;
import com.zjjcnt.project.ck.zzj.web.request.*;
import com.zjjcnt.project.ck.zzj.web.response.KssfzBzzgscResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzJfjghqResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzSlxxUploadResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzWcnscbzzgscResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 跨省身份证受理Controller
 *
 * <AUTHOR>
 * @date 2024-10-15 16:34:00
 */
@RequiredArgsConstructor
@Tag(name = "跨省身份证受理接口")
@RestController
@RequestMapping("/kssfz")
public class KssfzController {

    private final KssfzManager kssfzManager;

    @Operation(summary = "办证资格审查")
    @GetMapping("bzzgsc")
    public CommonResult<KssfzBzzgscResp> getBzzgsc(@Validated KssfzBzzgscReq req) {
        KssfzBzzgscResp kssfzBzzgscResp = kssfzManager.queryBzzgsc(req);
        return CommonResult.success(kssfzBzzgscResp);
    }

    @Operation(summary = "未成年首申办证资格审查")
    @PostMapping("wcnscbzzgsc")
    public CommonResult<KssfzWcnscbzzgscResp> getWcnscbzzgsc(@Validated @RequestBody KssfzWcnscbzzgscReq req) {
        KssfzWcnscbzzgscResp kssfzWcnscbzzgscResp = kssfzManager.queryWcnscbzzgsc(req);
        return CommonResult.success(kssfzWcnscbzzgscResp);
    }

    @Operation(summary = "申领信息提交")
    @PostMapping("uploadSlxx")
    public CommonResult<KssfzSlxxUploadResp> uploadSlxx(@Validated @RequestBody KssfzSlxxUploadReq req) {
        KssfzSlxxUploadResp kssfzSlxxUploadResp = kssfzManager.uploadSlxx(req);
        return CommonResult.success(kssfzSlxxUploadResp);
    }

    @Operation(summary = "缴费结果获取")
    @GetMapping("jfjghq")
    public CommonResult<KssfzJfjghqResp> getJfjghq(@Validated KssfzJfjghqReq req) {
        KssfzJfjghqResp kssfzJfjghqResp = kssfzManager.getJfjg(req);
        return CommonResult.success(kssfzJfjghqResp);
    }

    @Operation(summary = "终端受理结束提交")
    @PostMapping("sljstj")
    public CommonResult<Boolean> getJfjghq(@Validated @RequestBody KssfzSljstjReq req) {
        return CommonResult.success(kssfzManager.finishSlxx(req));
    }

}
