package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 打印信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.RzbDyxxbDTO
 */
@Crypto
@Data
@Table("rzb_dyxxb")
public class RzbDyxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = 1229622802900006168L;

    /**
     * 指纹图像ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String dyid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 打印类别
     */
    @Column(value = "dylb")
    private String dylb;

    /**
     * 证件编号
     */
    @Column(value = "zjbh")
    private String zjbh;

    /**
     * 印制年份
     */
    @Column(value = "yznf")
    private String yznf;

    /**
     * 操作时间
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作人ID
     */
    @Column(value = "czrid")
    private String czrid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     * 操作员单位代码
     */
    @Column(value = "czydwdm")
    private String czydwdm;

    /**
     * 操作员单位名称
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     * 操作ip
     */
    @Column(value = "czip")
    private String czip;

    /**
     * 数据归属单位代码
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     * 业务受理号
     */
    @Column(value = "ywslh")
    private String ywslh;

    /**
     * 受理材料编号
     */
    @Column(value = "slclbh")
    private String slclbh;

    /**
     * 材料名称
     */
    @Column(value = "clmc")
    private String clmc;

    /**
     * 业务类型
     */
    @Column(value = "lcywlx")
    private String lcywlx;


    @Override
    public String getId() {
        return this.dyid;
    }

    @Override
    public void setId(String id) {
        this.dyid = id;
    }
}
