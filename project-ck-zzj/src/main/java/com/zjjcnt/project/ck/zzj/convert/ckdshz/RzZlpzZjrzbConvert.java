package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.RzZlpzZjrzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RzZlpzZjrzbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Convert
 *
 * <AUTHOR>
 * @date 2024-12-12 11:06:00
 */
@Mapper
public interface RzZlpzZjrzbConvert {

    RzZlpzZjrzbConvert INSTANCE = Mappers.getMapper(RzZlpzZjrzbConvert.class);

    RzZlpzZjrzbDTO convert(RzZlpzZjrzbDO entity);

    @InheritInverseConfiguration(name = "convert")
    RzZlpzZjrzbDO convertToDO(RzZlpzZjrzbDTO dto);
}