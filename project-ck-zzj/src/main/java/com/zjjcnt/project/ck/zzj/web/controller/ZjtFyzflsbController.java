package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtFyzflsbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtFyzflsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtFyzflsbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtFyzflsbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtFyzflsbViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtFyzflsbService;
import com.zjjcnt.project.ck.zzj.web.request.ZjtFyzflsbJkdhQrReq;
import com.zjjcnt.project.ck.zzj.web.response.ZjtFyzflsbJkdhQrRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 费用支付流水表前端控制器
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */

@Tag(name = "费用支付流水表")
@RestController
@RequestMapping("/zjtFyzflsb")
public class ZjtFyzflsbController extends AbstractCrudController<ZjtFyzflsbDTO> {

    @Autowired
    private ZjtFyzflsbService zjtFyzflsbService;

    @Override
    protected IBaseService getService() {
        return zjtFyzflsbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询费用支付流水表 N23003")
    public CommonResult<PageResult<ZjtFyzflsbPageResp>> page(ZjtFyzflsbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZjtFyzflsbConvert.INSTANCE::convertToDTO, ZjtFyzflsbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看费用支付流水表详情")
    public CommonResult<ZjtFyzflsbViewResp> view(@RequestParam(value = "id") String id) {
        return super.view(id, ZjtFyzflsbConvert.INSTANCE::convertToViewResp);
    }

//
//    @PostMapping("create")
//    @Operation(summary = "新增费用支付流水表")
//    public CommonResult<ZjtFyzflsbCreateResp> create(@RequestBody ZjtFyzflsbCreateReq req) {
//        return super.create(req, ZjtFyzflsbConvert.INSTANCE::convertToDTO, ZjtFyzflsbConvert.INSTANCE::convertToCreateResp);
//    }
//
//    @PostMapping("update")
//    @Operation(summary = "编辑费用支付流水表")
//    public CommonResult<Boolean> update(@RequestBody ZjtFyzflsbUpdateReq req) {
//        return super.update(req, ZjtFyzflsbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除费用支付流水表")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }

    @GetMapping("getEwmByJkdh")
    @Operation(summary = "根据交款单号获取二维码 F31002")
    public CommonResult<ZjtFyzflsbJkdhQrRes> getEwmByJkdh(@Validated ZjtFyzflsbJkdhQrReq req) {
        //20210325 增加缴款信息的生成时间阈值校验
        String base64qr = zjtFyzflsbService.queryJfewm(req.getJkdh());
        return CommonResult.success(new ZjtFyzflsbJkdhQrRes(req.getJkdh(), base64qr));
    }

}
