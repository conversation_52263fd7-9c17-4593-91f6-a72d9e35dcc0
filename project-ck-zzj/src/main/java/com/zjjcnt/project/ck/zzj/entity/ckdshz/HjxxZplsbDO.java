package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 照片临时表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 11:35:02
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxZplsbDTO
 */
@Crypto
@Data
@Table("hjxx_zplsb")
public class HjxxZplsbDO implements IdEntity<Long> {
    private static final long serialVersionUID = 410185418552237386L;

    /**
     *
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 + SID_HJXX_ZPLSB.nextval FROM dual")
    private Long zplsid;

    /**
     *
     */
    @Column(value = "slh")
    private String slh;

    /**
     *
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     *
     */
    @Column(value = "zpid")
    private Long zpid;

    /**
     *
     */
    @Column(value = "ryid")
    private Long ryid;

    /**
     *
     */
    @Column(value = "rynbid")
    private Long rynbid;

    /**
     *
     */
    @Column(value = "zp")
    private byte[] zp;

    /**
     *
     */
    @Column(value = "bcsj")
    private String bcsj;

    /**
     *
     */
    @Column(value = "czrid")
    private Long czrid;

    /**
     *
     */
    @Column(value = "jlbz")
    private String jlbz;

    /**
     *
     */
    @Column(value = "bdrid")
    private Long bdrid;

    /**
     *
     */
    @Column(value = "bdrdwdm")
    private String bdrdwdm;

    /**
     *
     */
    @Column(value = "bdsj")
    private String bdsj;


    @Override
    public Long getId() {
        return this.zplsid;
    }

    @Override
    public void setId(Long id) {
        this.zplsid = id;
    }
}
