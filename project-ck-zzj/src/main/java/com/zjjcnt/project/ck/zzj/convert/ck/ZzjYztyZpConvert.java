package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYztyZpDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYztyZpCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYztyZpPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjYztyZpUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYztyZpCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYztyZpPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjYztyZpViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjYztyZpDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZzjYztyZpExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 自助机一照通用照片Convert
*
* <AUTHOR>
* @date 2025-01-14 16:13:45
*/
@Mapper
public interface ZzjYztyZpConvert {

    ZzjYztyZpConvert INSTANCE = Mappers.getMapper(ZzjYztyZpConvert.class);

    ZzjYztyZpDTO convert(ZzjYztyZpDO entity);

    @InheritInverseConfiguration(name="convert")
    ZzjYztyZpDO convertToDO(ZzjYztyZpDTO dto);

    ZzjYztyZpDTO convertToDTO(ZzjYztyZpPageReq req);

    ZzjYztyZpDTO convertToDTO(ZzjYztyZpCreateReq req);

    ZzjYztyZpDTO convertToDTO(ZzjYztyZpUpdateReq req);

    ZzjYztyZpPageResp convertToPageResp(ZzjYztyZpDTO dto);

    ZzjYztyZpViewResp convertToViewResp(ZzjYztyZpDTO dto);

    ZzjYztyZpCreateResp convertToCreateResp(ZzjYztyZpDTO dto);

    ZzjYztyZpExp convertToExp(ZzjYztyZpDTO dto);

}
