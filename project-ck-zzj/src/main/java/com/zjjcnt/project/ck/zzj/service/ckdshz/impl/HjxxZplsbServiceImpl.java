package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.exception.UserErrorCode;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.common.util.image.ImageUtils;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxZplsbConvert;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxZpytbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxPzrzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxZplsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxZpytbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZplsbSaveReq;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxZplsbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.HjxxZplsbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxPzrzbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxZplsbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxZpytbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbxxbService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;
import java.util.Objects;

/**
 * 照片临时表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 11:35:02
 */
@RequiredArgsConstructor
@Service
public class HjxxZplsbServiceImpl extends AbstractBaseServiceImpl<HjxxZplsbMapper, HjxxZplsbDO, HjxxZplsbDTO> implements HjxxZplsbService {

    HjxxZplsbConvert convert = HjxxZplsbConvert.INSTANCE;

    private final HjxxPzrzbService hjxxPzrzbService;
    private final HjxxZpytbService hjxxZpytbService;
    private final XtZzsbxxbService xtZzsbxxbService;

    private final static int IMAGE_SIZE_2_KB = 2048;
    private final static int IMAGE_SIZE_40_KB = 40960;
    private final static int IMAGE_SIZE_2_MB = 2097152;

    @Override
    protected QueryWrapper genQueryWrapper(HjxxZplsbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(HjxxZplsbDO::getSlh, dto.getSlh(), StringUtils.isNotEmpty(dto.getSlh()));
        query.eq(HjxxZplsbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(HjxxZplsbDO::getZpid, dto.getZpid(), Objects.nonNull(dto.getZpid()));
        query.eq(HjxxZplsbDO::getRyid, dto.getRyid(), Objects.nonNull(dto.getRyid()));
        query.eq(HjxxZplsbDO::getRynbid, dto.getRynbid(), Objects.nonNull(dto.getRynbid()));
        query.eq(HjxxZplsbDO::getZp, dto.getZp(), Objects.nonNull(dto.getZp()));
        query.ge(HjxxZplsbDO::getBcsj, dto.getBcsjStart(), StringUtils.isNotEmpty(dto.getBcsjStart()));
        query.le(HjxxZplsbDO::getBcsj, dto.getBcsjEnd(), StringUtils.isNotEmpty(dto.getBcsjEnd()));
        query.eq(HjxxZplsbDO::getCzrid, dto.getCzrid(), Objects.nonNull(dto.getCzrid()));
        query.eq(HjxxZplsbDO::getJlbz, dto.getJlbz(), StringUtils.isNotEmpty(dto.getJlbz()));
        query.eq(HjxxZplsbDO::getBdrid, dto.getBdrid(), Objects.nonNull(dto.getBdrid()));
        query.eq(HjxxZplsbDO::getBdrdwdm, dto.getBdrdwdm(), StringUtils.isNotEmpty(dto.getBdrdwdm()));
        query.ge(HjxxZplsbDO::getBdsj, dto.getBdsjStart(), StringUtils.isNotEmpty(dto.getBdsjStart()));
        query.le(HjxxZplsbDO::getBdsj, dto.getBdsjEnd(), StringUtils.isNotEmpty(dto.getBdsjEnd()));
        return query;
    }

    @Override
    public HjxxZplsbDTO convertToDTO(HjxxZplsbDO hjxxZplsbDO) {
        return convert.convert(hjxxZplsbDO);
    }

    @Override
    public HjxxZplsbDO convertToDO(HjxxZplsbDTO hjxxZplsbDTO) {
        return convert.convertToDO(hjxxZplsbDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public HjxxZplsbDTO processSaveZplsb(HjxxZplsbSaveReq req) {
        Long zpytbid = 0L;
        HjxxZplsbDTO zplsb = new HjxxZplsbDTO();
        String now = ServerTimeUtils.getCurrentTime();

        //照片大小检测
        int irxzp = ImageUtils.imageSize(req.getBase64zp());
        if (irxzp < IMAGE_SIZE_2_KB) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "人像照片太小，需要大于2KB");
        }
        if (irxzp > IMAGE_SIZE_40_KB) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "人像照片不能超过40KB");
        }
        Integer irxzpyt = ImageUtils.imageSize(req.getBase64zpyt());
        if (irxzpyt > IMAGE_SIZE_2_MB) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "人像照片原图不能超过2MB");
        }

        //删除历史拍摄的临时照片
        HjxxZplsbDTO deleteDTO = new HjxxZplsbDTO();
        deleteDTO.setGmsfhm(req.getGmsfhm());
        delete(deleteDTO);

        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();

        //保存照片到临时表
        zplsb.setGmsfhm(req.getGmsfhm());
        zplsb.setZp(Base64.getDecoder().decode(req.getBase64zp()));
        zplsb.setBcsj(now);
        zplsb.setCzrid(currentUser.getUserId());
        zplsb = insert(zplsb);

        XtZzsbxxbDTO xtZzsbxxbDTO = xtZzsbxxbService.findById(req.getSbid());
        if (Objects.isNull(xtZzsbxxbDTO)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_NOT_FOUND);
        }

        if (StringUtils.isNotBlank(req.getBase64zpyt())) {
            HjxxZpytbDTO zpytbDTO = HjxxZpytbConvert.INSTANCE.convertToDTO(req);
            zpytbDTO.setZpytData(Base64.getDecoder().decode(req.getBase64zpyt()));
            zpytbDTO.setBcsj(now);
            zpytbDTO.setZpsbppxhdm(xtZzsbxxbDTO.getSblx());
            zpytbDTO.setZpsbppxh(xtZzsbxxbDTO.getSbxhmc());
            zpytbid = hjxxZpytbService.insert(zpytbDTO).getZpytbid();
        }

        //保存拍照日志信息，否则前台拍照日期查询功能使用不了
        HjxxPzrzbDTO pzrzb = new HjxxPzrzbDTO();
        pzrzb.setZplsid(zplsb.getZplsid());
        pzrzb.setGmsfhm(req.getGmsfhm());
        pzrzb.setBcsj(now);
        pzrzb.setIpdz(currentUser.getRemoteAddress());
        pzrzb.setYhid(currentUser.getUserId());
        pzrzb.setYhdlm(currentUser.getUsername());
        pzrzb.setYhxm(currentUser.getName());
        pzrzb.setYhdw(currentUser.getDeptCode());
        pzrzb.setZpcjlx(req.getZpcjlx());
        pzrzb.setZpsbppxhdm(xtZzsbxxbDTO.getSblx());
        pzrzb.setZpsbppxh(xtZzsbxxbDTO.getSbxhmc());
        pzrzb.setSbid(String.valueOf(xtZzsbxxbDTO.getId()));
        pzrzb.setZlpzsbdwbh(xtZzsbxxbDTO.getZlpzsbdwbh());
        if (zpytbid > 0L) {
            pzrzb.setZpytbid(String.valueOf(zpytbid));
        }
        hjxxPzrzbService.insert(pzrzb);

        return zplsb;
    }

    @Override
    public HjxxZplsbDTO getLastValidPhoto(String gmsfhm) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(HjxxZplsbDO::getGmsfhm, gmsfhm);
        // 目前是6个月内
        query.ge(HjxxZplsbDO::getBcsj, DateTimeUtils.addMonths(-6));
        query.orderBy(HjxxZplsbDO::getBcsj, false);
        query.limit(1);
        return find(query);
    }
}
