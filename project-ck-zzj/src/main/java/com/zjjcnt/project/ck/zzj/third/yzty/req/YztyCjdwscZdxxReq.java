package com.zjjcnt.project.ck.zzj.third.yzty.req;

import lombok.Data;

/**
 * 终端信息
 * <AUTHOR>
 */
@Data
public class YztyCjdwscZdxxReq {

    /**
     * 采集设备点位编号，上传点位的唯一值。
     */
    private String sbdwbh;

    /**
     * 政务服务窗口编号。
     */
    private String fwckbh;
    /**
     * 窗口名称。
     */
    private String fwckmc;

    /**
     * 设备型号，取设备点位编号中第13、14位XX。
     */
    private String sbxh;

    /**
     * 所属机构代码
     */
    private String sbdwdm;

    /**
     * 所属单位名称
     */
    private String sbdwmc;

    /**
     * 窗口详细地址。
     */
    private String sbdwdz;
    /**
     * 采集设备mac地址
     */
    private String macdz;

    /**
     * 是否固定点位 1位字符,代码;1:是；0:否
     */
    private String sfgddw;

    /**
     * 经度
     */
    private String jd;

    /**
     * 纬度
     */
    private String wd;

    /**
     * 是否有人值 1位字符,代码;1:是；0:否
     */
    private String sfyrzs;

    /**
     * 采集终端类型代码
     */
    private String cjzdlxdm;
    /**
     * 采集终端类型名称
     */
    private String cjzdlxmc;
    /**
     * 窗口类别代码 【窗口类别代码字典】
     */
    private String dwfwcklbdm;
    /**
     * 窗口类别名称
     */
    private String dwfwcklbmc;
    /**
     * 是否启用,点位是否在用。 1位字符,代码;1在用，2弃用
     */
    private String sfqy;
    /**
     * 启用日期 8位字符 yyymmdd
     */
    private String qyrq;
    /**
     * 注册日期： 8位字符 yyymmdd
     */
    private String zcrq;
    /**
     * 点位变动时间,启用及废用采集点位时间, 格式为yyyy-mm-dd hh24:mi:ss
     */
    private String dwbdsj;
    /**
     * 操作类型, 1位字符,代码;1新增,2修改
     */
    private String czlx;
    private String bz1;
    private String bz2;
    private String bz3;
}
