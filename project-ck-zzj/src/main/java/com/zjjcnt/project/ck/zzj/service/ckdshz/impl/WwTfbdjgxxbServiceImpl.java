package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.WwTfbdjgxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwTfbdjgxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.WwTfbdjgxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.WwTfbdjgxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.WwTfbdjgxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 逃犯比对结果信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@Service
public class WwTfbdjgxxbServiceImpl extends AbstractBaseServiceImpl<WwTfbdjgxxbMapper, WwTfbdjgxxbDO, WwTfbdjgxxbDTO> implements WwTfbdjgxxbService {

    WwTfbdjgxxbConvert convert = WwTfbdjgxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(WwTfbdjgxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(WwTfbdjgxxbDO::getPptj, dto.getPptj(), Objects.nonNull(dto.getPptj()));
        query.eq(WwTfbdjgxxbDO::getBdyj, dto.getBdyj(), Objects.nonNull(dto.getBdyj()));
        query.ge(WwTfbdjgxxbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(WwTfbdjgxxbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(WwTfbdjgxxbDO::getCzrid, dto.getCzrid(), Objects.nonNull(dto.getCzrid()));
        query.eq(WwTfbdjgxxbDO::getCzrip, dto.getCzrip(), Objects.nonNull(dto.getCzrip()));
        query.eq(WwTfbdjgxxbDO::getCzrdw, dto.getCzrdw(), Objects.nonNull(dto.getCzrdw()));
        query.eq(WwTfbdjgxxbDO::getCzrxm, ColumnUtils.encryptColumn(dto.getCzrxm()), StringUtils.isNotEmpty(dto.getCzrxm()));
        query.eq(WwTfbdjgxxbDO::getCzrdlm, dto.getCzrdlm(), Objects.nonNull(dto.getCzrdlm()));
        query.eq(WwTfbdjgxxbDO::getBdywbh, dto.getBdywbh(), Objects.nonNull(dto.getBdywbh()));
        query.eq(WwTfbdjgxxbDO::getShrid, dto.getShrid(), Objects.nonNull(dto.getShrid()));
        query.ge(WwTfbdjgxxbDO::getShsj, dto.getShsjStart(), StringUtils.isNotEmpty(dto.getShsjStart()));
        query.le(WwTfbdjgxxbDO::getShsj, dto.getShsjEnd(), StringUtils.isNotEmpty(dto.getShsjEnd()));
        query.eq(WwTfbdjgxxbDO::getCljg, dto.getCljg(), Objects.nonNull(dto.getCljg()));
        query.eq(WwTfbdjgxxbDO::getClsm, dto.getClsm(), Objects.nonNull(dto.getClsm()));
        query.eq(WwTfbdjgxxbDO::getClrid, dto.getClrid(), Objects.nonNull(dto.getClrid()));
        query.ge(WwTfbdjgxxbDO::getClsj, dto.getClsjStart(), StringUtils.isNotEmpty(dto.getClsjStart()));
        query.le(WwTfbdjgxxbDO::getClsj, dto.getClsjEnd(), StringUtils.isNotEmpty(dto.getClsjEnd()));
        query.eq(WwTfbdjgxxbDO::getClrip, dto.getClrip(), Objects.nonNull(dto.getClrip()));
        query.likeLeft(WwTfbdjgxxbDO::getSsxq, dto.getSsxq(), StringUtils.isNotEmpty(dto.getSsxq()));
        query.eq(WwTfbdjgxxbDO::getJlx, dto.getJlx(), Objects.nonNull(dto.getJlx()));
        query.eq(WwTfbdjgxxbDO::getMlph, dto.getMlph(), Objects.nonNull(dto.getMlph()));
        query.eq(WwTfbdjgxxbDO::getMlxz, dto.getMlxz(), Objects.nonNull(dto.getMlxz()));
        query.eq(WwTfbdjgxxbDO::getPcs, dto.getPcs(), Objects.nonNull(dto.getPcs()));
        query.eq(WwTfbdjgxxbDO::getZrq, dto.getZrq(), Objects.nonNull(dto.getZrq()));
        query.eq(WwTfbdjgxxbDO::getXzjd, dto.getXzjd(), Objects.nonNull(dto.getXzjd()));
        query.eq(WwTfbdjgxxbDO::getJcwh, dto.getJcwh(), Objects.nonNull(dto.getJcwh()));
        query.eq(WwTfbdjgxxbDO::getRynbid, dto.getRynbid(), Objects.nonNull(dto.getRynbid()));
        query.eq(WwTfbdjgxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(WwTfbdjgxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(WwTfbdjgxxbDO::getJlbz, dto.getJlbz(), Objects.nonNull(dto.getJlbz()));
        return query;
    }

    @Override
    public WwTfbdjgxxbDTO convertToDTO(WwTfbdjgxxbDO wwTfbdjgxxbDO) {
        return convert.convert(wwTfbdjgxxbDO);
    }

    @Override
    public WwTfbdjgxxbDO convertToDO(WwTfbdjgxxbDTO wwTfbdjgxxbDTO) {
        return convert.convertToDO(wwTfbdjgxxbDTO);
    }

    @Override
    public void disableTodayWclByGmsfhmAndCzrid(String gmsfhm, Long czrid) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(WwTfbdjgxxbDO::getPptj, gmsfhm);
        query.likeLeft(WwTfbdjgxxbDO::getCzsj, DateTimeUtils.today());
        query.eq(WwTfbdjgxxbDO::getCzrid, czrid);
        query.eq(WwTfbdjgxxbDO::getJlbz, Constants.YES);
        query.isNull(WwTfbdjgxxbDO::getCljg);

        WwTfbdjgxxbDO update = new WwTfbdjgxxbDO();
        update.setJlbz(Constants.NO);
        update(update, query);
    }
}
