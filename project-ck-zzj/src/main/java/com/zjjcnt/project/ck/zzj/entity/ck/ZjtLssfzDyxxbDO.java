package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 临时身份证打印信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzDyxxbDTO
 */
@Crypto
@Data
@Table("zjt_lssfz_dyxxb")
public class ZjtLssfzDyxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = -2891989595210333120L;

    /**
     * 临时打印ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String lsdyid;

    /**
     * 临时受理ID
     */
    @Column(value = "lsslid")
    private String lsslid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 临时居民身份证卡号
     */
    @Column(value = "lsjmsfzkh")
    private String lsjmsfzkh;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     *
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作员IP
     */
    @Column(value = "czyip")
    private String czyip;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;


    @Override
    public String getId() {
        return this.lsdyid;
    }

    @Override
    public void setId(String id) {
        this.lsdyid = id;
    }
}
