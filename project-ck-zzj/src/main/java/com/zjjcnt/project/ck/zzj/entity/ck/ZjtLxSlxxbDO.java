package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:39:12
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtLxSlxxbDTO
 */
@Crypto
@Data
@Table("zjt_lx_slxxb")
public class ZjtLxSlxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = -3915070270646326631L;

    /**
     * 内部受理id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String nbslid;

    /**
     *
     */
    @Column(value = "ywslh")
    private String ywslh;

    /**
     * 内部身份证id
     */
    @Column(value = "nbsfzid")
    private String nbsfzid;

    /**
     * 照片id
     */
    @Column(value = "zpid")
    private String zpid;

    /**
     * 受理号
     */
    @Column(value = "slh")
    private String slh;

    /**
     * 人员id
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     *
     */
    @Column(value = "rynbid")
    private String rynbid;

    /**
     * 签发机关
     */
    @Column(value = "qfjg")
    private String qfjg;

    /**
     * 有效期限起始日期
     */
    @Column(value = "yxqxqsrq")
    private String yxqxqsrq;

    /**
     * 有效期限截止日期
     */
    @Column(value = "yxqxjzrq")
    private String yxqxjzrq;

    /**
     * 住址
     */
    @Column(value = "zz")
    private String zz;

    /**
     * 申领原因
     */
    @Column(value = "slyy")
    private String slyy;

    /**
     * 制证类型
     */
    @Column(value = "zzlx")
    private String zzlx;

    /**
     * 领证方式
     */
    @Column(value = "lqfs")
    private String lqfs;

    /**
     * 收费类型
     */
    @Column(value = "sflx")
    private String sflx;

    /**
     * 收费金额
     */
    @Column(value = "sfje")
    private BigDecimal sfje;

    /**
     * 数据包流水号
     */
    @Column(value = "sjblsh")
    private String sjblsh;

    /**
     * 受理状态
     */
    @Column(value = "slzt")
    private String slzt;

    /**
     * 同步标志
     */
    @Column(value = "tbbz")
    private Integer tbbz;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 民族
     */
    @Column(value = "mz")
    private String mz;

    /**
     * 出生日期
     */
    @Column(value = "csrq")
    private String csrq;

    /**
     * 出生地省市县（区）
     */
    @Column(value = "csdssxq")
    private String csdssxq;

    /**
     *
     */
    @Column(value = "mlpnbid")
    private String mlpnbid;

    /**
     * 省市县（区）
     */
    @Column(value = "ssxq")
    private String ssxq;

    /**
     * 街路巷
     */
    @Column(value = "jlx")
    private String jlx;

    /**
     * 门（楼）牌号
     */
    @Column(value = "mlph")
    private String mlph;

    /**
     * 门（楼）详址
     */
    @Column(value = "mlxz")
    private String mlxz;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 责任区
     */
    @Column(value = "zrq")
    private String zrq;

    /**
     * 乡镇（街道）
     */
    @Column(value = "xzjd")
    private String xzjd;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 排序号
     */
    @Column(value = "pxh")
    private String pxh;

    /**
     * 业务标志
     */
    @Column(value = "ywbz")
    private String ywbz;

    /**
     * 操作员id
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     *
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 单位代码
     */
    @Column(value = "dwdm")
    private String dwdm;

    /**
     * 收件人姓名
     */
    @Crypto
    @Column(value = "sjrxm")
    private String sjrxm;

    /**
     * 收件人联系电话
     */
    @Crypto
    @Column(value = "sjrlxdh")
    private String sjrlxdh;

    /**
     * 收件人邮编
     */
    @Column(value = "sjryb")
    private String sjryb;

    /**
     *
     */
    @Column(value = "sjrssxq")
    private String sjrssxq;

    /**
     *
     */
    @Column(value = "sjrxz")
    private String sjrxz;

    /**
     * 收件人通讯地址
     */
    @Column(value = "sjrtxdz")
    private String sjrtxdz;

    /**
     * 制证信息错误类别
     */
    @Column(value = "zzxxcwlb")
    private String zzxxcwlb;

    /**
     * 错误描述
     */
    @Column(value = "cwms")
    private String cwms;

    /**
     * 检验单位
     */
    @Column(value = "jydw")
    private String jydw;

    /**
     * 检验人姓名
     */
    @Crypto
    @Column(value = "jyrxm")
    private String jyrxm;

    /**
     * 检验日期
     */
    @Column(value = "jyrq")
    private String jyrq;

    /**
     * 处理单位
     */
    @Column(value = "cldw")
    private String cldw;

    /**
     * 处理情况
     */
    @Column(value = "clqk")
    private String clqk;

    /**
     * 处理日期
     */
    @Column(value = "clrq")
    private String clrq;

    /**
     * 质量回馈状态
     */
    @Column(value = "zlhkzt")
    private String zlhkzt;

    /**
     * 回馈时间
     */
    @Column(value = "hksj")
    private String hksj;

    /**
     *
     */
    @Column(value = "bwbha")
    private String bwbha;

    /**
     *
     */
    @Column(value = "bwbhb")
    private String bwbhb;

    /**
     * 审核日期
     */
    @Column(value = "shrq")
    private String shrq;

    /**
     * 省厅接收时间
     */
    @Column(value = "stjssj")
    private String stjssj;

    /**
     *
     */
    @Column(value = "bwbhc")
    private String bwbhc;

    /**
     * 分拣批次号
     */
    @Column(value = "fjpch")
    private String fjpch;

    /**
     *
     */
    @Column(value = "rlbdid")
    private String rlbdid;

    /**
     * 人脸比对id
     */
    @Column(value = "rlbdbz")
    private String rlbdbz;

    /**
     * 人脸比对标志
     */
    @Column(value = "rlbdsj")
    private String rlbdsj;

    /**
     * 指纹一指位
     */
    @Column(value = "zwyzw")
    private String zwyzw;

    /**
     * 指纹一注册结果
     */
    @Column(value = "zwyzcjg")
    private String zwyzcjg;

    /**
     * 指纹二指位
     */
    @Column(value = "zwezw")
    private String zwezw;

    /**
     * 指纹二注册结果
     */
    @Column(value = "zwezcjg")
    private String zwezcjg;

    /**
     * 指纹采集结果代码
     */
    @Column(value = "zwcjjgdm")
    private String zwcjjgdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "szyczkdm")
    private String szyczkdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "sfzwzj")
    private String sfzwzj;

    /**
     *
     */
    @Column(value = "dztbbz")
    private BigDecimal dztbbz;

    /**
     * 对账同步标志
     */
    @Column(value = "dztbsj")
    private String dztbsj;

    /**
     * 对账同步时间
     */
    @Column(value = "dzsjbbh")
    private String dzsjbbh;

    /**
     * 对账数据包编号
     */
    @Column(value = "slfs")
    private String slfs;

    /**
     * 指纹图像临时id
     */
    @Column(value = "zwtxid")
    private String zwtxid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     *
     */
    @Column(value = "hjddzbm")
    private String hjddzbm;

    /**
     *
     */
    @Column(value = "lssfzslbz")
    private String lssfzslbz;

    /**
     *
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     *
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     *
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     *
     */
    @Column(value = "zjzdssxq")
    private String zjzdssxq;

    /**
     *
     */
    @Column(value = "zjzdxz")
    private String zjzdxz;

    /**
     * 领取日期
     */
    @Column(value = "lqrq")
    private String lqrq;

    /**
     * 领取人姓名
     */
    @Crypto
    @Column(value = "lqrxm")
    private String lqrxm;

    /**
     * 领取人身份号码
     */
    @Column(value = "lqrsfhm")
    private String lqrsfhm;

    /**
     * 领取人照片id
     */
    @Column(value = "lqrzpid")
    private String lqrzpid;

    /**
     * 证件到达日期
     */
    @Column(value = "zjddrq")
    private String zjddrq;

    /**
     *
     */
    @Column(value = "lzczrid")
    private String lzczrid;

    /**
     *
     */
    @Crypto
    @Column(value = "lzczrxm")
    private String lzczrxm;

    /**
     * 审核人姓名
     */
    @Crypto
    @Column(value = "shrxm")
    private String shrxm;

    /**
     * 审核单位
     */
    @Column(value = "shdw")
    private String shdw;

    /**
     * 审核情况
     */
    @Column(value = "shqk")
    private String shqk;

    /**
     * 签发日期
     */
    @Column(value = "qfrq")
    private String qfrq;

    /**
     * 签发人id
     */
    @Column(value = "qfrid")
    private String qfrid;

    /**
     * 审核人姓名
     */
    @Crypto
    @Column(value = "qfrxm")
    private String qfrxm;

    /**
     * 签发单位机构代码
     */
    @Column(value = "qfdwjgdm")
    private String qfdwjgdm;

    /**
     * 签发单位
     */
    @Column(value = "qfdw")
    private String qfdw;

    /**
     * 审核日期
     */
    @Column(value = "dsshrq")
    private String dsshrq;

    /**
     * 审核人姓名
     */
    @Crypto
    @Column(value = "dsshrxm")
    private String dsshrxm;

    /**
     * 审核单位
     */
    @Column(value = "dsshdw")
    private String dsshdw;

    /**
     * 审核情况
     */
    @Column(value = "dsshqk")
    private String dsshqk;

    /**
     *
     */
    @Column(value = "rwid")
    private String rwid;

    /**
     *
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     *
     */
    @Column(value = "sldfjsjgsdwdm")
    private String sldfjsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldfjsjgsdwmc")
    private String sldfjsjgsdwmc;

    /**
     *
     */
    @Column(value = "shdwdm")
    private String shdwdm;

    /**
     *
     */
    @Column(value = "cldwdm")
    private String cldwdm;

    /**
     *
     */
    @Column(value = "dsshdwdm")
    private String dsshdwdm;

    /**
     *
     */
    @Column(value = "shrid")
    private String shrid;

    /**
     *
     */
    @Column(value = "dsshrid")
    private String dsshrid;

    /**
     * 收费单据号
     */
    @Column(value = "sfdjh")
    private String sfdjh;

    /**
     * 任务执行日志编号
     */
    @Column(value = "rwzxrzbh")
    private String rwzxrzbh;

    /**
     * 任务调度时间
     */
    @Column(value = "rwddsj")
    private String rwddsj;

    /**
     *
     */
    @Column(value = "slyckrxsfbd")
    private String slyckrxsfbd;

    /**
     *
     */
    @Column(value = "rxbdkssj")
    private String rxbdkssj;

    /**
     *
     */
    @Column(value = "rxbdhs")
    private String rxbdhs;

    /**
     *
     */
    @Column(value = "rxbdxsd")
    private String rxbdxsd;

    /**
     *
     */
    @Column(value = "rxbdkbh")
    private String rxbdkbh;

    /**
     *
     */
    @Column(value = "rxbdjg")
    private String rxbdjg;

    /**
     *
     */
    @Column(value = "slylszwsfbd")
    private String slylszwsfbd;

    /**
     *
     */
    @Column(value = "zwybdjg")
    private String zwybdjg;

    /**
     *
     */
    @Column(value = "zwybdxsd")
    private String zwybdxsd;

    /**
     *
     */
    @Column(value = "zwebdjg")
    private String zwebdjg;

    /**
     *
     */
    @Column(value = "zwebdxsd")
    private String zwebdxsd;

    /**
     *
     */
    @Column(value = "lzszwsfhy")
    private String lzszwsfhy;

    /**
     *
     */
    @Column(value = "lzszwyhyjg")
    private String lzszwyhyjg;

    /**
     *
     */
    @Column(value = "lzszwyhyxsd")
    private String lzszwyhyxsd;

    /**
     *
     */
    @Column(value = "lzszwehyjg")
    private String lzszwehyjg;

    /**
     *
     */
    @Column(value = "lzszwehyxsd")
    private String lzszwehyxsd;

    /**
     *
     */
    @Column(value = "lzssfjhjz")
    private String lzssfjhjz;

    /**
     *
     */
    @Column(value = "slylszwbdsm")
    private String slylszwbdsm;

    /**
     *
     */
    @Column(value = "lzszwbdsm")
    private String lzszwbdsm;

    /**
     * 申请人_公民身份号码
     */
    @Crypto
    @Column(value = "sqrgmsfhm")
    private String sqrgmsfhm;

    /**
     * 申请人_姓名
     */
    @Crypto
    @Column(value = "sqrxm")
    private String sqrxm;

    /**
     *
     */
    @Crypto
    @Column(value = "sqrlxdh")
    private String sqrlxdh;

    /**
     * 旧证起始日期
     */
    @Column(value = "jzqsrq")
    private String jzqsrq;

    /**
     * 操作员联系电话
     */
    @Crypto
    @Column(value = "czylxdh")
    private String czylxdh;

    /**
     * 区县审核联系电话
     */
    @Crypto
    @Column(value = "shrlxdh")
    private String shrlxdh;

    /**
     * 地市审核联系电话
     */
    @Crypto
    @Column(value = "dsshrlxdh")
    private String dsshrlxdh;

    /**
     * 总分成金额
     */
    @Column(value = "zfcje")
    private BigDecimal zfcje;

    /**
     * 区县分成金额
     */
    @Column(value = "qxfcje")
    private BigDecimal qxfcje;

    /**
     * 地市分成金额
     */
    @Column(value = "dsfcje")
    private BigDecimal dsfcje;

    /**
     * 中心分成金额
     */
    @Column(value = "zxfcje")
    private BigDecimal zxfcje;

    /**
     * 邮政快递单号
     */
    @Column(value = "yzkddh")
    private String yzkddh;

    /**
     *
     */
    @Column(value = "spdz1")
    private String spdz1;

    /**
     * 是否满十八岁无照片
     */
    @Column(value = "sfmsbswzp")
    private String sfmsbswzp;

    /**
     * 互联网申请id
     */
    @Column(value = "hlwsqid")
    private String hlwsqid;

    /**
     * 第三方用户名称
     */
    @Column(value = "username")
    private String username;

    /**
     * 评价结果
     */
    @Column(value = "pjjg")
    private String pjjg;

    /**
     * 跑了几次评价
     */
    @Column(value = "pjpljc")
    private String pjpljc;

    /**
     * 评价时间
     */
    @Column(value = "pjsj")
    private String pjsj;

    /**
     * 服务对象
     */
    @Column(value = "fwdx")
    private String fwdx;

    /**
     * 是否待挂失证件0否1是
     */
    @Column(value = "sfdgszj")
    private String sfdgszj;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     *
     */
    @Column(value = "slshjdz")
    private String slshjdz;

    /**
     *
     */
    @Column(value = "ksywlsh")
    private String ksywlsh;

    /**
     *
     */
    @Column(value = "ksfsbz")
    private String ksfsbz;

    /**
     *
     */
    @Column(value = "ksfssj")
    private String ksfssj;

    /**
     * 身份证照片
     */
    @Column(value = "zp")
    private byte[] zp;

    /**
     * 申请人现场照片
     */
    @Column(value = "sqrzp")
    private byte[] sqrzp;

    /**
     * 指纹信息
     */
    @Column(value = "zwxx")
    private byte[] zwxx;

    /**
     * 签字信息
     */
    @Column(value = "qzxx")
    private byte[] qzxx;

    /**
     * 户口簿材料1
     */
    @Column(value = "hkb1")
    private byte[] hkb1;

    /**
     * 户口簿材料2
     */
    @Column(value = "hkb2")
    private byte[] hkb2;

    /**
     * 户口簿材料3
     */
    @Column(value = "hkb3")
    private byte[] hkb3;

    /**
     * 户口簿材料4
     */
    @Column(value = "hkb4")
    private byte[] hkb4;

    /**
     * 身份证材料1
     */
    @Column(value = "sfz1")
    private byte[] sfz1;

    /**
     * 身份证材料2
     */
    @Column(value = "sfz2")
    private byte[] sfz2;

    /**
     * 身份证材料3
     */
    @Column(value = "sfz3")
    private byte[] sfz3;

    /**
     * 身份证材料4
     */
    @Column(value = "sfz4")
    private byte[] sfz4;

    /**
     * 材料数量
     */
    @Column(value = "clsl")
    private Integer clsl;


    @Override
    public String getId() {
        return this.nbslid;
    }

    @Override
    public void setId(String id) {
        this.nbslid = id;
    }
}
