package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.core.convert.Base64ByteArrConverter;
import com.zjjcnt.project.ck.zzj.dto.ck.RxZpcjxxbLxDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.RxZpcjxxbLxCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.RxZpcjxxbLxCreateResp;
import com.zjjcnt.project.ck.zzj.entity.ck.RxZpcjxxbLxDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 离线照片采集信息表Convert
*
* <AUTHOR>
* @date 2024-06-21 09:30:28
*/
@Mapper(uses = Base64ByteArrConverter.class)
public interface RxZpcjxxbLxConvert {

    RxZpcjxxbLxConvert INSTANCE = Mappers.getMapper(RxZpcjxxbLxConvert.class);

    RxZpcjxxbLxDTO convert(RxZpcjxxbLxDO entity);

    @InheritInverseConfiguration(name="convert")
    RxZpcjxxbLxDO convertToDO(RxZpcjxxbLxDTO dto);

    @Mapping(target = "ytzp", source = "ytzpBase64")
    @Mapping(target = "sfzzp", source = "sfzzpBase64")
    @Mapping(target = "jszzp", source = "jszzpBase64")
    @Mapping(target = "crjzp", source = "crjzpBase64")
    RxZpcjxxbLxDTO convertToDTO(RxZpcjxxbLxCreateReq req);

    RxZpcjxxbLxCreateResp convertToCreateResp(RxZpcjxxbLxDTO dto);

}
