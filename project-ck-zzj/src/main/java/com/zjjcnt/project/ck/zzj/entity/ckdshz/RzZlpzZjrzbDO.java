package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 浙里拍照质检日志表
 * <AUTHOR>
 * @date 2024-12-12 10:21:37
 */
@Data
@Table("rz_zlpz_zjrzb")
public class RzZlpzZjrzbDO implements IdEntity<Long>  {

    /**
     * 日志ID
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 + SID_RZ_ZLPZ_FSRZB.nextval FROM dual")
    private Long rzid;

    /**
     * mp母片质检 crj出入境质检
     */
    private String zjlx;

    /**
     * 质检结果 0失败 1成功
     */
    private String zjjg;

    /**
     * 质检描述
     */
    private String zjms;

    /**
     * 操作时间
     */
    private String czsj;

    /**
     * 群众证件号码
     */
    private String gmsfhm;

    /**
     * 操作员用户id
     */
    private String czyid;

    @Override
    public Long getId() {
        return this.rzid;
    }

    @Override
    public void setId(Long id) {
        this.rzid = id;
    }
}
