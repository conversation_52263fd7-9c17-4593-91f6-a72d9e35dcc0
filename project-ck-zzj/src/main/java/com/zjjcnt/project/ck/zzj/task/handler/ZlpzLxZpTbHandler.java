package com.zjjcnt.project.ck.zzj.task.handler;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.dto.XtYhxxbDTO;
import com.zjjcnt.project.ck.sysadmin.service.XtYhxxbService;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.RxZpcjxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.RxZpcjxxbLxDO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RxZpcjxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.RxZpcjxxbLxMapper;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.RxZpcjxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RxZpwjbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbxxbService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 浙里拍照离线照片同步任务处理器
 *
 * <AUTHOR>
 * @date 2024-06-24 13:41:00
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ZlpzLxZpTbHandler {

    private final RxZpcjxxbLxMapper rxZpcjxxbLxMapper;
    private final RxZpcjxxbMapper rxZpcjxxbMapper;
    private final XtZzsbxxbService xtZzsbxxbService;
    private final RxZpwjbService rxZpwjbService;
    private final XtYhxxbService xtYhxxbService;

    public List<RxZpcjxxbLxDO> listDtbRxZpcjxxb() {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(RxZpcjxxbLxDO::getTbbz, CkZzjConstants.TBBZ_DTB)
                .limit(10);

        return rxZpcjxxbLxMapper.selectListByQuery(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void handOne(RxZpcjxxbLxDO rxZpcjxxbLxDO) {

        XtZzsbxxbDTO zzsbxxbDTO = xtZzsbxxbService.findByZlpzsbdwbh(rxZpcjxxbLxDO.getZlpzsbdwbh());

        if (Objects.isNull(zzsbxxbDTO)) {
            updateTbbzFail(rxZpcjxxbLxDO.getId(), "设备不存在，zlpzsbdwbh=" + rxZpcjxxbLxDO.getZlpzsbdwbh());
            return;
        }

        XtYhxxbDTO yhxxbDTO = xtYhxxbService.findByYhdlm(zzsbxxbDTO.getCkyhdlm());

        if (Objects.isNull(yhxxbDTO)) {
            updateTbbzFail(rxZpcjxxbLxDO.getId(), "用户不存在，dlm=" + zzsbxxbDTO.getCkyhdlm());
            return;
        }

        RxZpcjxxbDO rxZpcjxxbDO = RxZpcjxxbConvert.INSTANCE.convertToDO(rxZpcjxxbLxDO);

        rxZpcjxxbDO.setSbid(zzsbxxbDTO.getId());
        rxZpcjxxbDO.setCzyip(zzsbxxbDTO.getSbip());
        rxZpcjxxbDO.setCzygmsfhm(yhxxbDTO.getGmsfhm());
        rxZpcjxxbDO.setCzyid(yhxxbDTO.getYhid());
        rxZpcjxxbDO.setCzyxm(yhxxbDTO.getYhxm());
        rxZpcjxxbDO.setCjsj(ServerTimeUtils.getCurrentTime());
        rxZpcjxxbDO.setCjly("浙里拍照离线采集");

        String wjhz = "jpg";
        //保存各照片
        if (ArrayUtils.isNotEmpty(rxZpcjxxbLxDO.getYtzp())) {
            rxZpcjxxbDO.setYtzpid(rxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_YT, rxZpcjxxbDO.getZjhm(),
                    rxZpcjxxbDO.getXm(), rxZpcjxxbLxDO.getYtzp(), wjhz));
        }
        if (ArrayUtils.isNotEmpty(rxZpcjxxbLxDO.getSfzzp())) {
            rxZpcjxxbDO.setSfzzpid(rxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_SFZ, rxZpcjxxbDO.getZjhm(),
                    rxZpcjxxbDO.getXm(), rxZpcjxxbLxDO.getSfzzp(), wjhz));
        }
        if (ArrayUtils.isNotEmpty(rxZpcjxxbLxDO.getCrjzp())) {
            rxZpcjxxbDO.setCrjzpid(rxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_CRJ, rxZpcjxxbDO.getZjhm(),
                    rxZpcjxxbDO.getXm(), rxZpcjxxbLxDO.getCrjzp(), wjhz));
        }
        if (ArrayUtils.isNotEmpty(rxZpcjxxbLxDO.getJszzp())) {
            rxZpcjxxbDO.setJszzpid(rxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_JSZ, rxZpcjxxbDO.getZjhm(),
                    rxZpcjxxbDO.getXm(), rxZpcjxxbLxDO.getJszzp(), wjhz));
        }
        if (ArrayUtils.isNotEmpty(rxZpcjxxbLxDO.getQtzp())) {
            rxZpcjxxbDO.setQtzpid(rxZpwjbService.insertWj(CkZzjConstants.RXZP_LX_QT, rxZpcjxxbDO.getZjhm(),
                    rxZpcjxxbDO.getXm(), rxZpcjxxbLxDO.getQtzp(), wjhz));
        }
        rxZpcjxxbMapper.insertSelective(rxZpcjxxbDO);
        updateTbbzSuccess(rxZpcjxxbLxDO.getId(), rxZpcjxxbDO.getId());
    }


    private void updateTbbzFail(Long id, String msg) {

        RxZpcjxxbLxDO rxZpcjxxbLxDO = new RxZpcjxxbLxDO();
        rxZpcjxxbLxDO.setId(id);
        rxZpcjxxbLxDO.setTbbz(CkZzjConstants.TBBZ_TBSB);
        rxZpcjxxbLxDO.setTbsj(ServerTimeUtils.getCurrentTime());
        rxZpcjxxbLxDO.setTbxx(msg);

        rxZpcjxxbLxMapper.update(rxZpcjxxbLxDO);
    }

    private void updateTbbzSuccess(Long id, Long zpxxid) {

        RxZpcjxxbLxDO rxZpcjxxbLxDO = new RxZpcjxxbLxDO();
        rxZpcjxxbLxDO.setId(id);
        rxZpcjxxbLxDO.setTbbz(CkZzjConstants.TBBZ_TBCG);
        rxZpcjxxbLxDO.setTbsj(ServerTimeUtils.getCurrentTime());
        rxZpcjxxbLxDO.setZpxxid(zpxxid);

        rxZpcjxxbLxMapper.update(rxZpcjxxbLxDO);
    }
}
