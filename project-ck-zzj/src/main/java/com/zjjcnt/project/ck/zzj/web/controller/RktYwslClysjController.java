package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.sysadmin.dto.RktYwslClysjDTO;
import com.zjjcnt.project.ck.sysadmin.file.RktYwslClysjFileManager;
import com.zjjcnt.project.ck.sysadmin.service.RktYwslClysjService;
import com.zjjcnt.project.ck.zzj.convert.ck.RktYwslClysjConvert;
import com.zjjcnt.project.ck.zzj.web.request.RktYwslClysjSaveReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
* 业务受理材料元数据前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@RequiredArgsConstructor
@Tag(name = "业务受理材料元数据")
@RestController
@RequestMapping("/rktYwslClysj")
public class RktYwslClysjController extends AbstractCrudController<RktYwslClysjDTO> {

    private final RktYwslClysjService rktYwslClysjService;
    private final RktYwslClysjFileManager rktYwslClysjFileManager;

    @Override
    protected IBaseService<RktYwslClysjDTO> getService() {
        return rktYwslClysjService;
    }

    @PostMapping("upload")
    @Operation(summary = "新增业务受理材料元数据 F10102")
    public CommonResult<Boolean> upload(@Validated @RequestBody RktYwslClysjSaveReq req) {

        List<RktYwslClysjDTO> list = RktYwslClysjConvert.INSTANCE.convertToDTO(req.getList());

        for (RktYwslClysjDTO entity : list) {
            if (StringUtils.isBlank(entity.getWjlx())) {
                if ("9911".equals(entity.getCllxdm())) {
                    entity.setWjlx(MediaType.APPLICATION_PDF_VALUE);
                } else {
                    entity.setWjlx(MediaType.IMAGE_JPEG_VALUE);
                }
            }
//            //9934,9935需要增加水印，是否已签章 add 20230908
//            if ("[9934][9935]".indexOf(entity.getCllxdm()) > 0) {
//                if ("0".equals(entity.getSfyqz())) {
//                    String[] contents = new String[]{"浙里人口—户籍事项证明", entity.getSjgsdwdm() + ServerTimeUtils.getCurrentTime()};
//                    String pdfbase64 = PdfUtil.addWaterMark(entity.getBase64Data(), contents);
//                    entity.setBase64Data(pdfbase64);
//                }
//            }
        }
        rktYwslClysjFileManager.save(list);
        return CommonResult.success(true);
    }


//    @GetMapping("page")
//    @Operation(summary = "查询业务受理材料元数据")
//    public CommonResult<PageResult<RktYwslClysjPageResp>> page(RktYwslClysjPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, RktYwslClysjConvert.INSTANCE::convertToDTO, RktYwslClysjConvert.INSTANCE::convertToPageResp);
//    }
//
//    @GetMapping("view")
//    @Operation(summary = "查看业务受理材料元数据详情")
//    public CommonResult<RktYwslClysjViewResp> view(String id) {
//        return super.view(id, RktYwslClysjConvert.INSTANCE::convertToViewResp);
//    }
//
//    @PostMapping("create")
//    @Operation(summary = "新增业务受理材料元数据")
//    public CommonResult<RktYwslClysjCreateResp> create(@RequestBody RktYwslClysjCreateReq req) {
//        return super.create(req, RktYwslClysjConvert.INSTANCE::convertToDTO, RktYwslClysjConvert.INSTANCE::convertToCreateResp);
//    }
//
//    @PostMapping("update")
//    @Operation(summary = "编辑业务受理材料元数据")
//    public CommonResult<Boolean> update(@RequestBody RktYwslClysjUpdateReq req) {
//        return super.update(req, RktYwslClysjConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除业务受理材料元数据")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }
//
//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(RktYwslClysjExp.class);
//        return CommonResult.success(exportColumns);
//    }


}
