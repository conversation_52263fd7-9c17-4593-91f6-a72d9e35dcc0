package com.zjjcnt.project.ck.zzj.manager;

import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.zzj.convert.KssfzConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbGabjmxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbGabjmxxbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbxxbService;
import com.zjjcnt.project.ck.zzj.third.rop.ZdbzsbKssfzRopClient;
import com.zjjcnt.project.ck.zzj.third.rop.req.*;
import com.zjjcnt.project.ck.zzj.third.rop.resp.KssfzBzzgscRopResp;
import com.zjjcnt.project.ck.zzj.third.rop.resp.KssfzJfjghqRopResp;
import com.zjjcnt.project.ck.zzj.third.rop.resp.KssfzSlxxUploadRopResp;
import com.zjjcnt.project.ck.zzj.third.rop.resp.KssfzWcnscbzzgscRopResp;
import com.zjjcnt.project.ck.zzj.web.request.*;
import com.zjjcnt.project.ck.zzj.web.response.KssfzBzzgscResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzJfjghqResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzSlxxUploadResp;
import com.zjjcnt.project.ck.zzj.web.response.KssfzWcnscbzzgscResp;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 跨省身份证受理管理
 *
 * <AUTHOR>
 * @date 2024-10-15 16:41:00
 */
@RequiredArgsConstructor
@Service
public class KssfzManager {

    private final ZdbzsbKssfzRopClient zdbzsbKssfzRopClient;
    private final XtZzsbxxbService xtZzsbxxbService;
    private final XtZzsbGabjmxxbService zzsbGabjmxxbService;

    public KssfzBzzgscResp queryBzzgsc(KssfzBzzgscReq kssfzBzzgscReq) {
        KssfzBzzgscRopReq kssfzBzzgscRopReq = KssfzConvert.INSTANCE.convertBzzgscRopReq(kssfzBzzgscReq);

        checkSbxx(kssfzBzzgscReq.getSbid(), kssfzBzzgscRopReq);
        KssfzBzzgscRopResp kssfzBzzgscRopResp;
        try {
            kssfzBzzgscRopResp = zdbzsbKssfzRopClient.queryBzzgsc(kssfzBzzgscRopReq);
        } catch (ServiceException e) {
            if (Objects.equals(e.getCode(), CkZzjErrorCode.KSSFZ_ROP_API_BIZ_ERROR.getCode())) {
                kssfzBzzgscRopResp = new KssfzBzzgscRopResp();
                kssfzBzzgscRopResp.setBzzgpdbz(Constants.NO);
                kssfzBzzgscRopResp.setBzzgpdms(e.getMessage());
            } else {
                throw new ServiceException(e.getCode(), e.getMessage());
            }
        }
        return KssfzConvert.INSTANCE.convertBzzgscResp(kssfzBzzgscRopResp);
    }

    public KssfzWcnscbzzgscResp queryWcnscbzzgsc(KssfzWcnscbzzgscReq kssfzWcnscbzzgscReq) {
        KssfzWcnscbzzgscRopReq kssfzWcnscbzzgscRopReq = KssfzConvert.INSTANCE.convertWcnscbzzgscRopReq(kssfzWcnscbzzgscReq);

        XtZzsbxxbDTO xtZzsbxxbDTO = checkSbxx(kssfzWcnscbzzgscReq.getSbid(), kssfzWcnscbzzgscRopReq);
        kssfzWcnscbzzgscRopReq.setSldSjgsdwdm(xtZzsbxxbDTO.getDwjgdm());
        kssfzWcnscbzzgscRopReq.setSldSjgsdwmc(xtZzsbxxbDTO.getDwjgmc());

        XtZzsbGabjmxxbDTO xtZzsbGabjmxxbDTO = zzsbGabjmxxbService.findBySbid(kssfzWcnscbzzgscReq.getSbid());
        if (Objects.isNull(xtZzsbGabjmxxbDTO)) {
            throw new ServiceException(CkZzjErrorCode.KSSFZ_ROP_API_BIZ_ERROR, "设备加密信息不存在");
        }
        kssfzWcnscbzzgscRopReq.setDeviceid(xtZzsbGabjmxxbDTO.getDeviceID());

        KssfzWcnscbzzgscRopResp kssfzWcnscbzzgscRopResp = zdbzsbKssfzRopClient.queryWcnscbzzgsc(kssfzWcnscbzzgscRopReq);
       return KssfzConvert.INSTANCE.convertWcnscbzzgscResp(kssfzWcnscbzzgscRopResp);
    }

    public KssfzSlxxUploadResp uploadSlxx(KssfzSlxxUploadReq kssfzSlxxUploadReq) {

        KssfzSlxxUploadRopReq kssfzSlxxUploadRopReq = KssfzConvert.INSTANCE.convertSlxxUploadReq(kssfzSlxxUploadReq);

        checkSbxx(kssfzSlxxUploadReq.getSbid(), kssfzSlxxUploadRopReq);

        List<KssfzClxxUploadRopReq> clxxUploadReqList = KssfzConvert.INSTANCE
                .convertClxxUploadReq(kssfzSlxxUploadReq.getClxxList());

        KssfzSlxxUploadRopResp kssfzxxtj = zdbzsbKssfzRopClient.processZdbzsbKssfzxxtj(kssfzSlxxUploadRopReq,
                clxxUploadReqList);
        return KssfzConvert.INSTANCE.convertSlxxUploadResp(kssfzxxtj);
    }

    public KssfzJfjghqResp getJfjg(KssfzJfjghqReq kssfzJfjghqReq) {
        KssfzJfjghqRopReq kssfzJfjghqRopReq = KssfzConvert.INSTANCE.convertJfjghqRopReq(kssfzJfjghqReq);

        checkSbxx(kssfzJfjghqReq.getSbid(), kssfzJfjghqRopReq);

        KssfzJfjghqRopResp kssfzJfjghqRopResp = zdbzsbKssfzRopClient.queryZdbzsbJfjghq(kssfzJfjghqRopReq);
        return KssfzConvert.INSTANCE.convertJfjghqResp(kssfzJfjghqRopResp);
    }

    public boolean finishSlxx(KssfzSljstjReq kssfzSljstjReq) {
        KssfzSljstjRopReq kssfzSljstjRopReq = KssfzConvert.INSTANCE.convertSljstjRopReq(kssfzSljstjReq);

        checkSbxx(kssfzSljstjReq.getSbid(), kssfzSljstjRopReq);

        List<KssfzClxxUploadRopReq> clxxUploadReqList = KssfzConvert.INSTANCE
                .convertClxxUploadReq(kssfzSljstjReq.getClxxList());

        return zdbzsbKssfzRopClient.processZdbzsbSljstj(kssfzSljstjRopReq, clxxUploadReqList);
    }

    private XtZzsbxxbDTO checkSbxx(Long sbid, IZdbzsbKssfzRopReq zdbzsbKssfzRopReq) {

        XtZzsbxxbDTO xtZzsbxxbDTO = xtZzsbxxbService.findById(sbid);

        if (Objects.isNull(xtZzsbxxbDTO)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_NOT_FOUND);
        }

        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        if (!StringUtils.equals(xtZzsbxxbDTO.getCkyhdlm(), currentUser.getUsername())) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_ACCOUNT_NOT_MATCH);
        }

        if (Constants.NO.equals(xtZzsbxxbDTO.getSbsfvpn()) && Constants.YES.equals(xtZzsbxxbDTO.getSfjyip())
                && !StringUtils.equals(xtZzsbxxbDTO.getSbip(), SecurityUtils.getRemoteAddress()))  {
            throw new ServiceException(CkZzjErrorCode.DEVICE_IP_NOT_MATCH);
        }

        zdbzsbKssfzRopReq.setYhdlm(xtZzsbxxbDTO.getCkyhdlm());
        zdbzsbKssfzRopReq.setSbip(xtZzsbxxbDTO.getSbip());
        zdbzsbKssfzRopReq.setLicense(xtZzsbxxbDTO.getLicense());

        return xtZzsbxxbDTO;
    }

}
