package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtZwcjsbmxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjsbmxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjsbmxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbmxbPageResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjsbmxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 指纹采集设备门限表前端控制器
 *
 * <AUTHOR>
 * @date 2024-05-10 11:08:52
 */

@Tag(name = "指纹采集设备门限表")
@RestController
@RequestMapping("/zjtZwcjsbmxb")
public class ZjtZwcjsbmxbController extends AbstractCrudController<ZjtZwcjsbmxbDTO> {
    @Autowired
    private ZjtZwcjsbmxbService zjtZwcjsbmxbService;

    @Override
    protected IBaseService getService() {
        return zjtZwcjsbmxbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询指纹采集设备门限表 F23941")
    public CommonResult<PageResult<ZjtZwcjsbmxbPageResp>> page(ZjtZwcjsbmxbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZjtZwcjsbmxbConvert.INSTANCE::convertToDTO, ZjtZwcjsbmxbConvert.INSTANCE::convertToPageResp);
    }
}
