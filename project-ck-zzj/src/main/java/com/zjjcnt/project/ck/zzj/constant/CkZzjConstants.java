package com.zjjcnt.project.ck.zzj.constant;

/**
 * ck常量
 *
 * <AUTHOR>
 * @date 2024-04-10 09:26:00
 */
public final class CkZzjConstants {

    public static final String APP_KEY_CZRKZDYH = "czrkzdyh";

    /**
     * 冲销标志
     */
    public static final String CXBZ_0 = "0";

    /**
     * 记录状态 1-有效 2-无效
     */
    public static final String JLBZ_YX = "1";
    public static final String JLBZ_WX = "2";

    /**
     * 人员状态 0正常 1死亡 2迁出 3服兵役 4出国境定居 6失踪 7删除 8注销 9其他
     */
    public static final String RYZT_ZC = "0";
    public static final String RYZT_SW = "1";
    public static final String RYZT_QC = "2";
    public static final String RYZT_FBY = "3";
    public static final String RYZT_CGDJ = "4";
    public static final String RYZT_SZ = "6";
    public static final String RYZT_SC = "7";
    public static final String RYZT_ZX = "8";
    public static final String RYZT_QT = "9";

    /**
     * 公安部人员状态
     * 1	死亡
     * 2	失踪
     * 3	迁出(离开)本地
     * 4	服兵役
     * 5	出国境定居
     * 6	消除重复人口
     * 7	转为本地户籍人口
     * 9	其他
     */
    public static final String GAB_RYZT_SW = "1";
    public static final String GAB_RYZT_SZ = "2";
    public static final String GAB_RYZT_QC = "3";
    public static final String GAB_RYZT_FBY = "4";
    public static final String GAB_RYZT_CGDJ = "5";
    public static final String GAB_RYZT_XCCFRK = "6";
    public static final String GAB_RYZT_ZWBDHJ = "7";
    public static final String GAB_RYZT_QT = "9";

    /**
     * 户籍数据来源 1-公安部 2-浙江
     */
    public static final String HJSJLY_GAB = "1";
    public static final String HJSJLY_ZJ = "2";


    /**
     * 材料来源类型 1-申请材料
     */
    public static final String CLLYLX_SQCL = "1";

    /**
     * 公安部人员接口查询状态  0全部，1未注销，2已注销
     */
    public static final String GAB_RKXX_CX_ZT_QB = "0";


    /**
     * 功能编号-人员信息获取
     */
    public final static String GNBH_HJ_RYXXHQ = "F1001";
    /**
     * 功能编号-人员信息获取(查询)
     */
    public final static String GNBH_HJ_RYXXHQ_CX = "F1201";


    public final static String WJSJDZLX_OSS = "OSS";


    /**
     * 设备公司  101-宏田智能
     */
    public final static String SBGS_HTZN = "101";

    /**
     * 同步标志 0-不同步 1-待同步 3-同步失败 9-同步成功
     */
    public final static String TBBZ_BTB = "0";
    public final static String TBBZ_DTB = "1";
    public final static String TBBZ_TBSB = "3";
    public final static String TBBZ_TBCG = "9";

    /**
     * 人像照片类型 10-身份证 20-出入境 30-驾驶证 80-原图 90-其他
     */
    public final static String RXZP_LX_SFZ = "10";
    public final static String RXZP_LX_CRJ = "20";
    public final static String RXZP_LX_JSZ = "30";
    public final static String RXZP_LX_YT = "80";
    public final static String RXZP_LX_QT = "90";

    /**
     * 是否上传标志 3-要上传 0-不上传 1-上传成功
     */
    public final static String SFSCBZ_YSC = "3";
    public final static String SFSCBZ_BSC = "0";
    public final static String SFSCBZ_SCCG = "1";

    /**
     * 程序类型
     */
    public final static String CXLX_ZZJ = "01";

    /**
     * 采集终端操作类型 1-新增 2-修改 3-注销
     */
    public final static String CJZD_CZLX_XZ = "1";
    public final static String CJZD_CZLX_XG = "2";
    public final static String CJZD_CZLX_ZX = "3";

    /**
     * 采集终端是否启用 1-在用 2-弃用
     */
    public final static String CJZD_SFQY_ZY = "1";
    public final static String CJZD_SFQY_QY = "2";

    /**
     * 同步系统 00-长三角 06-绍兴 03-温州
     */
    public final static String TBXT_CSJ = "00";
    public final static String TBXT_SX = "06";

    /**
     * 户口冻结状态 1-冻结 2-锁定
     */
    public static final String DJZT_DJ = "1";
    public static final String DJZT_SD = "2";

    /**
     * 浙里拍照出入境系统名称
     */
    public final static String ZLPZ_CRJ_XTMC_DEFAULT = "ZLPZ-DL-HTB-02";

    /**
     * 指纹注册结果代码 1-注册成功
     */
    public static final String ZWZCJGDM_ZCCG= "1";

    /**
     * 证件种类 10-身份证
     */
    public static final String ZJZL_SFZ= "10";

    /**
     * 国家代码 156-中国
     */
    public static final String GJDM_ZG= "156";

    /**
     * 国家名称 中国
     */
    public static final String GJMC_ZG= "中国";

    /**
     * 采集用途
     */
    public static final String CJYT_BZSY= "办证使用";

    /**
     * 质检类型 mp母片质检 crj出入境质检
     */
    public static final String ZJLX_MP= "mp";
    public static final String ZJLX_CRJ= "crj";

    /**
     * 出入境质检type 质检-zj，打标-qxdb
     */
    public static final String CRJZJ_TYPE_ZJ = "zj";
    public static final String CRJZJ_TYPE_QXDB = "qxdb";

    /**
     * 非现金支付来源 1业务生成，2手动添加，3政务网添加
     */
    public final static String SF_SFXXLY_1_YWSC = "1";
    public final static String SF_SFXXLY_2_SDTJ = "2";
    public final static String SF_SFXXLY_3_ZWWTJ = "3";

    /**
     * 非现金支付状态 0-预申报 1-有效 5-重办 6-作废 7-手动直接作废
     */
    public final static String SF_FXJZT_0_YSB = "0";
    public final static String SF_FXJZT_1_YX = "1";
    public final static String SF_FXJZT_5_CB = "5";
    public final static String SF_FXJZT_6_ZF = "6";
    public final static String SF_FXJZT_7_SDZJZF = "7";


    public final static String INVOKE_SYS_XC_ZZJ = "xc_zzj";
    public final static String INVOKE_SYS_XC_ZZJ_ZZSBZC = "xc_zzj_zzsbzc";

    /**
     * 公安部未成年申领身份证资格审查获取加密信息接口入参格式
     */
    public static String DOWNLOAD_JAR_PARAM_XML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
            "<CONDITIONS><CONDITION result=\"\">" +
            "<DEVICEID OPERATION=\"=\">" +
            "{{DEVICEID}}" +
            "</DEVICEID>" +
            "</CONDITION></CONDITIONS>";

    /**
     * 协同类型代码
     * 40：居民身份证首申业务跨省通办
     * 41: 居民身份证补换领业务跨省通办
     */
    public static String KSXT_XTHJDM_SFZSS = "40";
    public static String KSXT_XTHJDM_SFZBHL = "41";

    /**
     * 申领原因
     * 11：未成年首申
     */
    public static String SLYY_WCNSS = "11";

    private CkZzjConstants() {
    }
}
