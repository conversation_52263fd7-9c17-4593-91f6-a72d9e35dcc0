package com.zjjcnt.project.ck.zzj.third.rop.req;

import lombok.Data;

/**
 * 受理信息上传
 *
 * <AUTHOR>
 * @date 2024-10-15 11:17:00
 */
@Data
public class KssfzSlxxUploadRopReq implements IZdbzsbKssfzRopReq {

    /**
     * 设备秘钥
     */
    private String license;

    /**
     * 设备IP地址
     */
    private String sbip;

    /**
     * 用户登录名
     */
    private String yhdlm;


    /**
     * 人像信息
     * 办证照片id
     */
    private String bzzpid;


    /**
     * 办证人信息
     * 公民身份号码
     */
    private String gmsfhm;
    /**
     * 姓名
     */
    private String xm;

    /**
     * 住址
     */
    private String zz;

    /**
     * 紧急联系人姓名
     */
    private String jjlxrxm;

    /**
     * 紧急联系人电话
     */
    private String jjlxrdh;

    /**
     * 紧急联系人与受理人关系
     */
    private String jjlxryslrgx;

    /**
     * 领取方式
     */
    private String lqfs;
    /**
     * 收件人姓名
     */
    private String sjrxm;
    /**
     * 收件人联系电话
     */
    private String sjrlxdh;
    /**
     * 收件人邮编
     */
    private String sjryb;
    /**
     * 收件人省市县区
     */
    private String sjrssxq;
    /**
     * 收件人详址
     */
    private String sjrxz;
    /**
     * 收件人通讯地址
     */
    private String sjrtxdz;

    /**
     * 现居住地省市县区代码
     */
    private String xzzssxqdm;

    /**
     * 现居住地区划内详细地址
     */
    private String xzzqhnxxdz;

    /**
     * 受理原因
     */
    private String slyy;


    /**
     * 指纹信息
     * 指纹采集结果
     */
    private String zwcjjgdm;
    /**
     * 手指异常状况代码
     */
    private String szyczkdm;
    /**
     * 指纹采集器标识号
     */
    private String zwcjqbsh;
    /**
     * 指纹算法版本号
     */
    private String zwsfbbh;
    /**
     * 指纹算法开发者代码
     */
    private String zwsfkfzdm;

    /**
     * 指纹一注册结果
     */
    private String zwyzcjg;
    /**
     * 指纹一指位
     */
    private String zwyzw;
    /**
     * 指纹一图像质量值
     */
    private String zwytxzlz;
    /**
     * 指纹一指纹特征数据
     */
    private String zwyzwtzsj;
    /**
     * 指纹一指纹图像数据
     */
    private String zwyzwtxsj;

    /**
     * 指纹二注册结果
     */
    private String zwezcjg;
    /**
     * 指纹二指位
     */
    private String zwezw;
    /**
     * 指纹二图像质量值
     */
    private String zwetxzlz;
    /**
     * 指纹二指纹特征数据
     */
    private String zwezwtzsj;
    /**
     * 指纹二指纹图像数据
     */
    private String zwezwtxsj;

}
