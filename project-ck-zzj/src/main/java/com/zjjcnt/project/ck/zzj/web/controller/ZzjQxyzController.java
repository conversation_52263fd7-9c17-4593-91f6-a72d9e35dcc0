package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.project.ck.zzj.manager.ZzjQxyzManager;
import com.zjjcnt.project.ck.zzj.web.request.GlyWithZwxxReq;
import com.zjjcnt.project.ck.zzj.web.response.GlyWithZwxxRes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "自助机鉴权controller表")
@RestController
@RequestMapping("/zzjQxyz")
public class ZzjQxyzController {

    @Autowired
    private ZzjQxyzManager zzjQxyzManager;

    @PostMapping("listGlyxxWithZw")
    @Operation(summary = "获取有指纹信息的管理员列表")
    public CommonResult<List<GlyWithZwxxRes>> listGlyxxWithZw(@RequestBody GlyWithZwxxReq req) {
        return CommonResult.success(zzjQxyzManager.listGlyWithZw(req.getDwdm()));
    }

    @GetMapping("password")
    @Operation(summary = "根据登录账号获取zzj鉴权密码")
    public CommonResult<String> password(@RequestParam(value = "account") String account) {
        // account赘余，后期更改为每个账户配置独立的密码
        return CommonResult.success("987654");
    }
}
