package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.common.security.context.UserSecurityContextHolder;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.DwUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtLssfzSlxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtLssfzSlxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzSlxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtLssfzSlxxbViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtLssfzSlxxbService;
import com.zjjcnt.project.ck.zzj.web.request.ZjtLssfzReq;
import com.zjjcnt.project.ck.zzj.web.request.ZjtLssfzShReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 临时身份证受理信息表前端控制器
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */

@Tag(name = "临时身份证受理信息表")
@RestController
@RequestMapping("/zjtLssfzSlxxb")
public class ZjtLssfzSlxxbController extends AbstractCrudController<ZjtLssfzSlxxbDTO> {

    @Autowired
    private ZjtLssfzSlxxbService zjtLssfzSlxxbService;

    @Override
    protected IBaseService getService() {
        return zjtLssfzSlxxbService;
    }

    //    @GetMapping("page")
//    @Operation(summary = "查询临时身份证受理信息表")
//    public CommonResult<PageResult<ZjtLssfzSlxxbPageResp>> page(ZjtLssfzSlxxbPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, ZjtLssfzSlxxbConvert.INSTANCE::convertToDTO, ZjtLssfzSlxxbConvert.INSTANCE::convertToPageResp);
//    }

    @GetMapping("view")
    @Operation(summary = "查看临时身份证受理信息表详情 N27001")
    public CommonResult<ZjtLssfzSlxxbViewResp> view(String id) {
        return super.view(id, ZjtLssfzSlxxbConvert.INSTANCE::convertToViewResp);
    }

    @PostMapping("create")
    @Operation(summary = "新增临时身份证受理信息表 F27001")
    public CommonResult<ZjtLssfzSlxxbCreateResp> create(@RequestBody ZjtLssfzReq req) {
        String sldsjgsdwdm = req.getSldsjgsdwdm();
        String sldsjgsdwmc = req.getSldsjgsdwmc();
        if(StringUtils.isEmpty(sldsjgsdwdm)){
            CustomUserDetails user = UserSecurityContextHolder.getCurrentUser();
            sldsjgsdwdm = user.getDeptCode();
            sldsjgsdwmc = user.getDeptName();
        }
        //受理地数据归属代码通过前台传入，因为受理地可能为办证中心而不是户籍数据归属单位
        if (StringUtils.isEmpty(sldsjgsdwdm) || StringUtils.isEmpty(sldsjgsdwmc)) {
            throw new ServiceException(500, "未指定受理地数据归属单位或名称");
        }
        sldsjgsdwdm = DwUtils.expandSjgsdw(sldsjgsdwdm);

        ZjtLssfzSlxxbDTO zjtLssfzSlxxbDTO = zjtLssfzSlxxbService.lssfzSl(req, sldsjgsdwdm, sldsjgsdwmc,
                req.getSldfjsjgsdwdm(), req.getSldfjsjgsdwmc());

        return CommonResult.success(ZjtLssfzSlxxbConvert.INSTANCE.convertToCreateResp(zjtLssfzSlxxbDTO));
    }

    @PostMapping("audit")
    @Operation(summary = "临时身份证审核 F27006")
    public CommonResult<ZjtLssfzSlxxbViewResp> create(@RequestBody ZjtLssfzShReq req) {

        ZjtLssfzSlxxbDTO zjtLssfzSlxxbDTO = zjtLssfzSlxxbService.lssfzSh(req);
        return CommonResult.success(ZjtLssfzSlxxbConvert.INSTANCE.convertToViewResp(zjtLssfzSlxxbDTO));
    }

    @PostMapping("update")
    @Operation(summary = "编辑临时身份证受理信息表 F27007")
    public CommonResult<Boolean> update(@RequestBody ZjtLssfzSlxxbUpdateReq req) {
        return super.update(req, ZjtLssfzSlxxbConvert.INSTANCE::convertToDTO);
    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除临时身份证受理信息表")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }

}
