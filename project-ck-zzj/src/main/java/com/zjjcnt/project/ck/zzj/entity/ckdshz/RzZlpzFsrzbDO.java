package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-09-23 16:21:37
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.RzZlpzFsrzbDTO
 */
@Data
@Table("rz_zlpz_fsrzb")
public class RzZlpzFsrzbDO implements IdEntity<Long> {
    private static final long serialVersionUID = 5411156088224690246L;

    /**
     * 日志ID
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 + SID_RZ_ZLPZ_FSRZB.nextval FROM dual")
    private Long rzid;

    /**
     * 照片信息ID
     */
    @Column(value = "zpxxid")
    private Long zpxxid;

    /**
     * 设备ID
     */
    @Column(value = "sbid")
    private Long sbid;

    /**
     * 发送时间
     */
    @Column(value = "fssj")
    private String fssj;

    /**
     * 返回代码
     */
    @Column(value = "fhdm")
    private String fhdm;

    /**
     * 返回描述
     */
    @Column(value = "fhms")
    private String fhms;

    /**
     * 待同步系统 00浙里拍照3+1 30一照通用1+1
     */
    @Column(value = "dtbxt")
    private String dtbxt;

    /**
     * 公民身份号码
     */
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 操作员用户表id
     */
    @Column(value = "czyid")
    private Long czyid;

    /**
     * 操作员姓名
     */
    @Column(value = "czyxm")
    private String czyxm;

    @Override
    public Long getId() {
        return this.rzid;
    }

    @Override
    public void setId(Long id) {
        this.rzid = id;
    }
}
