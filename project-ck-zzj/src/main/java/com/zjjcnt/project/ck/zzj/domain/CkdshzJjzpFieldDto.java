package com.zjjcnt.project.ck.zzj.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;

import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlValue;

public class CkdshzJjzpFieldDto {
    private String name;

    private String value;

    public CkdshzJjzpFieldDto() {

    }

    @JsonIgnore
    @XmlTransient
    public String getName() {
        return name;
    }
    @XmlValue
    public String getValue() {
        return value;
    }

    public CkdshzJjzpFieldDto(String name, String value) {
        if (StringUtils.isEmpty(name)) {
            throw new RuntimeException("数据列名不能为空.");
        }
//        this.name = name.toUpperCase();
        this.name = name;
        this.value = value;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
