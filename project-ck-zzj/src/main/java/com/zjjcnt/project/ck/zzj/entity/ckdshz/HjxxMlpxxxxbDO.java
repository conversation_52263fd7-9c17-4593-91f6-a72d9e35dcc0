package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 门（楼）牌详细信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxMlpxxxxbDTO
 */
@Data
@Table("hjxx_mlpxxxxb")
public class HjxxMlpxxxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = 2756843499449106100L;

    /**
     * 门（楼）牌内部ID
     */
    @Id(keyType = KeyType.Auto)
    private Long mlpnbid;

    /**
     * 门（楼）牌ID
     */
    @Column(value = "mlpid")
    private Long mlpid;

    /**
     * 省市县（区）
     */
    @Column(value = "ssxq")
    private String ssxq;

    /**
     * 街路巷
     */
    @Column(value = "jlx")
    private String jlx;

    /**
     * 门（楼）牌号
     */
    @Column(value = "mlph")
    private String mlph;

    /**
     * 门（楼）详址
     */
    @Column(value = "mlxz")
    private String mlxz;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 责任区
     */
    @Column(value = "zrq")
    private String zrq;

    /**
     * 乡镇（街道）
     */
    @Column(value = "xzjd")
    private String xzjd;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 建地类别
     */
    @Column(value = "jdlb")
    private String jdlb;

    /**
     * 撤地类别
     */
    @Column(value = "cdlb")
    private String cdlb;

    /**
     * 建地时间
     */
    @Column(value = "jdsj")
    private String jdsj;

    /**
     * 撤地时间
     */
    @Column(value = "cdsj")
    private String cdsj;

    /**
     * 创建户籍业务ID
     */
    @Column(value = "cjhjywid")
    private Long cjhjywid;

    /**
     * 撤除户籍业务ID
     */
    @Column(value = "cchjywid")
    private Long cchjywid;

    /**
     * 门（楼）牌状态
     */
    @Column(value = "mlpzt")
    private String mlpzt;

    /**
     * 离线DBID
     */
    @Column(value = "lxdbid")
    private Long lxdbid;

    /**
     * 记录标志
     */
    @Column(value = "jlbz")
    private String jlbz;

    /**
     * 起用时间
     */
    @Column(value = "qysj")
    private String qysj;

    /**
     * 结束时间
     */
    @Column(value = "jssj")
    private String jssj;

    /**
     * 排序号
     */
    @Column(value = "pxh")
    private String pxh;

    /**
     * 统计用行政区划
     */
    @Column(value = "tjyxzqh")
    private String tjyxzqh;

    /**
     * 城乡属性
     */
    @Column(value = "cxsx")
    private String cxsx;

    /**
     *
     */
    @Column(value = "hjxz")
    private String hjxz;


    @Override
    public Long getId() {
        return this.mlpnbid;
    }

    @Override
    public void setId(Long id) {
        this.mlpnbid = id;
    }
}
