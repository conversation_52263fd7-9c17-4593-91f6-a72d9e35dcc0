package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.CkhlwHjsqJbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.CkhlwHjsqJbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.CkhlwHjsqJbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.CkhlwHjsqJbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.CkhlwHjsqJbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Service
public class CkhlwHjsqJbServiceImpl extends AbstractBaseServiceImpl<CkhlwHjsqJbMapper, CkhlwHjsqJbDO, CkhlwHjsqJbDTO> implements CkhlwHjsqJbService {

    CkhlwHjsqJbConvert convert = CkhlwHjsqJbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(CkhlwHjsqJbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(CkhlwHjsqJbDO::getSqlx, dto.getSqlx(), StringUtils.isNotEmpty(dto.getSqlx()));
        query.eq(CkhlwHjsqJbDO::getYwbm, dto.getYwbm(), StringUtils.isNotEmpty(dto.getYwbm()));
        query.eq(CkhlwHjsqJbDO::getSjly, dto.getSjly(), StringUtils.isNotEmpty(dto.getSjly()));
        query.eq(CkhlwHjsqJbDO::getProjectid, dto.getProjectid(), StringUtils.isNotEmpty(dto.getProjectid()));
        query.eq(CkhlwHjsqJbDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(CkhlwHjsqJbDO::getSqrgmsfhm, ColumnUtils.encryptColumn(dto.getSqrgmsfhm()), StringUtils.isNotEmpty(dto.getSqrgmsfhm()));
        query.eq(CkhlwHjsqJbDO::getSqrxm, ColumnUtils.encryptColumn(dto.getSqrxm()), StringUtils.isNotEmpty(dto.getSqrxm()));
        query.eq(CkhlwHjsqJbDO::getSqrlxdh, ColumnUtils.encryptColumn(dto.getSqrlxdh()), StringUtils.isNotEmpty(dto.getSqrlxdh()));
        query.eq(CkhlwHjsqJbDO::getSqrdz, dto.getSqrdz(), StringUtils.isNotEmpty(dto.getSqrdz()));
        query.eq(CkhlwHjsqJbDO::getBlfs, dto.getBlfs(), StringUtils.isNotEmpty(dto.getBlfs()));
        query.eq(CkhlwHjsqJbDO::getSjrxm, ColumnUtils.encryptColumn(dto.getSjrxm()), StringUtils.isNotEmpty(dto.getSjrxm()));
        query.eq(CkhlwHjsqJbDO::getSjrlxdh, ColumnUtils.encryptColumn(dto.getSjrlxdh()), StringUtils.isNotEmpty(dto.getSjrlxdh()));
        query.likeLeft(CkhlwHjsqJbDO::getSjssxq, dto.getSjssxq(), StringUtils.isNotEmpty(dto.getSjssxq()));
        query.eq(CkhlwHjsqJbDO::getSjdz, dto.getSjdz(), StringUtils.isNotEmpty(dto.getSjdz()));
        query.eq(CkhlwHjsqJbDO::getJjlxrxm, ColumnUtils.encryptColumn(dto.getJjlxrxm()), StringUtils.isNotEmpty(dto.getJjlxrxm()));
        query.eq(CkhlwHjsqJbDO::getJjlxrgmsfhm, ColumnUtils.encryptColumn(dto.getJjlxrgmsfhm()), StringUtils.isNotEmpty(dto.getJjlxrgmsfhm()));
        query.eq(CkhlwHjsqJbDO::getJjlxrlxdh, ColumnUtils.encryptColumn(dto.getJjlxrlxdh()), StringUtils.isNotEmpty(dto.getJjlxrlxdh()));
        query.eq(CkhlwHjsqJbDO::getSqrsl, dto.getSqrsl(), Objects.nonNull(dto.getSqrsl()));
        query.eq(CkhlwHjsqJbDO::getSqzt, dto.getSqzt(), StringUtils.isNotEmpty(dto.getSqzt()));
        query.eq(CkhlwHjsqJbDO::getShbz, dto.getShbz(), StringUtils.isNotEmpty(dto.getShbz()));
        query.eq(CkhlwHjsqJbDO::getShjg, dto.getShjg(), StringUtils.isNotEmpty(dto.getShjg()));
        query.eq(CkhlwHjsqJbDO::getShrid, dto.getShrid(), StringUtils.isNotEmpty(dto.getShrid()));
        query.eq(CkhlwHjsqJbDO::getShrxm, ColumnUtils.encryptColumn(dto.getShrxm()), StringUtils.isNotEmpty(dto.getShrxm()));
        query.eq(CkhlwHjsqJbDO::getShrip, dto.getShrip(), StringUtils.isNotEmpty(dto.getShrip()));
        query.ge(CkhlwHjsqJbDO::getShsj, dto.getShsjStart(), StringUtils.isNotEmpty(dto.getShsjStart()));
        query.le(CkhlwHjsqJbDO::getShsj, dto.getShsjEnd(), StringUtils.isNotEmpty(dto.getShsjEnd()));
        query.eq(CkhlwHjsqJbDO::getYxbz, dto.getYxbz(), StringUtils.isNotEmpty(dto.getYxbz()));
        query.eq(CkhlwHjsqJbDO::getYwtable, dto.getYwtable(), StringUtils.isNotEmpty(dto.getYwtable()));
        query.eq(CkhlwHjsqJbDO::getBustype, dto.getBustype(), StringUtils.isNotEmpty(dto.getBustype()));
        query.eq(CkhlwHjsqJbDO::getRelbusid, dto.getRelbusid(), StringUtils.isNotEmpty(dto.getRelbusid()));
        query.eq(CkhlwHjsqJbDO::getApplyfrom, dto.getApplyfrom(), StringUtils.isNotEmpty(dto.getApplyfrom()));
        query.eq(CkhlwHjsqJbDO::getQrjg, dto.getQrjg(), StringUtils.isNotEmpty(dto.getQrjg()));
        query.eq(CkhlwHjsqJbDO::getQrms, dto.getQrms(), StringUtils.isNotEmpty(dto.getQrms()));
        query.eq(CkhlwHjsqJbDO::getSpywslh, dto.getSpywslh(), StringUtils.isNotEmpty(dto.getSpywslh()));
        query.eq(CkhlwHjsqJbDO::getUsername, dto.getUsername(), StringUtils.isNotEmpty(dto.getUsername()));
        query.ge(CkhlwHjsqJbDO::getYyblsj, dto.getYyblsjStart(), StringUtils.isNotEmpty(dto.getYyblsjStart()));
        query.le(CkhlwHjsqJbDO::getYyblsj, dto.getYyblsjEnd(), StringUtils.isNotEmpty(dto.getYyblsjEnd()));
        query.eq(CkhlwHjsqJbDO::getHallid, dto.getHallid(), StringUtils.isNotEmpty(dto.getHallid()));
        query.eq(CkhlwHjsqJbDO::getHallname, dto.getHallname(), StringUtils.isNotEmpty(dto.getHallname()));
        query.eq(CkhlwHjsqJbDO::getJkdh, dto.getJkdh(), StringUtils.isNotEmpty(dto.getJkdh()));
        query.eq(CkhlwHjsqJbDO::getTbcxbz, dto.getTbcxbz(), StringUtils.isNotEmpty(dto.getTbcxbz()));
        query.eq(CkhlwHjsqJbDO::getHalluserid, dto.getHalluserid(), StringUtils.isNotEmpty(dto.getHalluserid()));
        query.eq(CkhlwHjsqJbDO::getHallusername, dto.getHallusername(), StringUtils.isNotEmpty(dto.getHallusername()));
        query.ge(CkhlwHjsqJbDO::getSqsj, dto.getSqsjStart(), StringUtils.isNotEmpty(dto.getSqsjStart()));
        query.le(CkhlwHjsqJbDO::getSqsj, dto.getSqsjEnd(), StringUtils.isNotEmpty(dto.getSqsjEnd()));
        query.ge(CkhlwHjsqJbDO::getSlsj, dto.getSlsjStart(), StringUtils.isNotEmpty(dto.getSlsjStart()));
        query.le(CkhlwHjsqJbDO::getSlsj, dto.getSlsjEnd(), StringUtils.isNotEmpty(dto.getSlsjEnd()));
        query.ge(CkhlwHjsqJbDO::getTbcxkssj, dto.getTbcxkssjStart(), StringUtils.isNotEmpty(dto.getTbcxkssjStart()));
        query.le(CkhlwHjsqJbDO::getTbcxkssj, dto.getTbcxkssjEnd(), StringUtils.isNotEmpty(dto.getTbcxkssjEnd()));
        query.ge(CkhlwHjsqJbDO::getTbcxjssj, dto.getTbcxjssjStart(), StringUtils.isNotEmpty(dto.getTbcxjssjStart()));
        query.le(CkhlwHjsqJbDO::getTbcxjssj, dto.getTbcxjssjEnd(), StringUtils.isNotEmpty(dto.getTbcxjssjEnd()));
        query.ge(CkhlwHjsqJbDO::getBjsj, dto.getBjsjStart(), StringUtils.isNotEmpty(dto.getBjsjStart()));
        query.le(CkhlwHjsqJbDO::getBjsj, dto.getBjsjEnd(), StringUtils.isNotEmpty(dto.getBjsjEnd()));
        return query;
    }

    @Override
    public CkhlwHjsqJbDTO convertToDTO(CkhlwHjsqJbDO ckhlwHjsqJbDO) {
        return convert.convert(ckhlwHjsqJbDO);
    }

    @Override
    public CkhlwHjsqJbDO convertToDO(CkhlwHjsqJbDTO ckhlwHjsqJbDTO) {
        return convert.convertToDO(ckhlwHjsqJbDTO);
    }
}
