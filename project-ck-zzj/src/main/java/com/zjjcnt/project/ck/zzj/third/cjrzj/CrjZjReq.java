package com.zjjcnt.project.ck.zzj.third.cjrzj;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 出入境质检请求
 *
 * <AUTHOR>
 * @date 2024-09-20 11:15:00
 */
@Data
public class CrjZjReq {

    /**
     * 算法模块	1:公安部一所3:雄帝SDK
     */
    private String module;

    /**
     * 处理方式
     * 1: 裁切
     * 2: 检测
     * 3: 裁切和检测
     * 4: 检测后裁剪+检测
     * 8: 图像调整
     */
    private Integer mode;

    /**
     * 裁切选项	1:裁切2:背景清理4:调整面部颜色
     * 可以是以上选项的叠加。
     * 例如：
     * 7：表示裁切+清理背景+ 调整面部颜色
     * 注意：
     * 处理方式为检测，即mode为2时。只检测情况下，此字段无效默认填0。
     */
    private String optAutoAd;

    /**
     * 照片规范文件名
     * 服务端文件，提前配置在服务端程序根目录。
     */
    @JsonProperty("flt_file")
    private String fltFile;

    /**
     * 照片base64数据
     * 需要包括base64头
     * data:image/jpeg;base64,
     */
    @JsonProperty("image_base64")
    private String imageBase64;

    /**
     * 照片文件名
     */
    @JsonProperty("image_filename")
    private String imageFilename;

    /**
     * flt版本 默认空为最新
     */
    private String version;

    /**
     * 当处理方式mode取值8有效。扩展支持图像调整算法，内容为json格式。其他情况穿空字符串
     */
    private String content;


}