package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbxxbSaveReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.XtZzsbxxbDO;
import com.zjjcnt.project.ck.zzj.exp.ckdshz.XtZzsbxxbExp;
import com.zjjcnt.project.ck.zzj.third.yzty.req.YztyCjdwsc;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 自助设备信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 11:19:21
*/
@Mapper
public interface XtZzsbxxbConvert {

    XtZzsbxxbConvert INSTANCE = Mappers.getMapper(XtZzsbxxbConvert.class);

    XtZzsbxxbDTO convert(XtZzsbxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    XtZzsbxxbDO convertToDO(XtZzsbxxbDTO dto);

    XtZzsbxxbDTO convertToDTO(XtZzsbxxbPageReq req);

    XtZzsbxxbDTO convertToDTO(XtZzsbxxbSaveReq req);

    YztyCjdwsc convertToYztyCjdwsc(XtZzsbxxbDTO req);

    XtZzsbxxbDTO convertToDTO(XtZzsbxxbCreateReq req);

    XtZzsbxxbDTO convertToDTO(XtZzsbxxbUpdateReq req);

    XtZzsbxxbPageResp convertToPageResp(XtZzsbxxbDTO dto);

    XtZzsbxxbViewResp convertToViewResp(XtZzsbxxbDTO dto);

    XtZzsbxxbCreateResp convertToCreateResp(XtZzsbxxbDTO dto);

    XtZzsbxxbExp convertToExp(XtZzsbxxbDTO dto);

}
