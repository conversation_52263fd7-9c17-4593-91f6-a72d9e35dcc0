package com.zjjcnt.project.ck.zzj.constant;

/**
 * 常口自助机字典常量
 *
 * <AUTHOR>
 * @date 2024-05-10 09:40:00
 */
public final class CkZzjDictTypeConstants {

    /**
     * 收费业务类型
     */
    public static final String CKSFYWLX = "cksfywlx";


    public static final String DM_ZWXXSTR = "DM_ZWXX"; //指纹信息
    public static final String DM_ZWXX = "5101"; //指纹信息
    public static final String DM_ZWCJJGSTR = "DM_ZWCJJG"; //指纹信息
    public static final String DM_ZWCJJG = "5104"; //指纹信息
    public static final String DM_SLYYSTR = "DM_SLYY"; //申领原因
    public static final String DM_SLYY = "7802"; //申领原因
    public static final String DM_MZ = "8001"; //民族
    public static final String DM_MZSTR = "DM_MZ"; //民族
    public static final String DM_XBSTR = "DM_XB"; //性别
    public static final String DM_XB = "8003"; //性别

    public static final String DM_EDZZZLXSTR = "DM_EDZZZLX"; //二代证制证类型
    public static final String DM_EDZZZLX = "5008"; //二代证制证类型
    public static final String DM_EDZLZFSSTR = "DM_EDZLZFS"; //二代证领证方式
    public static final String DM_EDZLZFS = "5009"; //二代证领证方式

    public static final String DM_DWXXB = "DM_DWXXB"; //单位信息表
    public static final String DM_XZQHB = "DM_XZQHB"; //行政区划

    /**
     * 使用状态
     */
    public static final String SYZTDM = "syztdm";

    /**
     * 有效标志
     */
    public static final String DM_YXBZ = "yxbz";

    /**
     * 设备生产销售公司
     */
    public static final String DM_SBXSGS = "1280";

    /**
     * 照片设备品牌型号
     */
    public static final String DM_ZPSBPPXH = "1259";

    /**
     * 拍照服务窗口类别代码
     */
    public static final String DM_PZFWCKLBDM = "1287";


    private CkZzjDictTypeConstants() {
    }
}
