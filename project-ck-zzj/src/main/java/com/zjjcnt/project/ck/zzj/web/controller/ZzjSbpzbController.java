package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.domain.TextValue;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjSbpzbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjSbpzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjSbpzbBatchSaveReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjSbpzbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjSbpzbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjSbpzbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.*;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjSbpzbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自助设备配置表前端控制器
 *
 * <AUTHOR>
 * @date 2024-07-19 14:06:40
 */
@Tag(name = "自助设备配置表")
@RestController
@RequestMapping("/zzjSbpzb")
public class ZzjSbpzbController extends AbstractCrudController<ZzjSbpzbDTO> {

    @Autowired
    private ZzjSbpzbService zzjSbpzbService;

    @Override
    protected IBaseService<ZzjSbpzbDTO> getService() {
        return zzjSbpzbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询自助设备配置表")
    public CommonResult<PageResult<ZzjSbpzbPageResp>> page(ZzjSbpzbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZzjSbpzbConvert.INSTANCE::convertToDTO, ZzjSbpzbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看自助设备配置表详情")
    public CommonResult<ZzjSbpzbViewResp> view(Long id) {
        return super.view(id, ZzjSbpzbConvert.INSTANCE::convertToViewResp);
    }

    @PostMapping("create")
    @Operation(summary = "新增自助设备配置表")
    public CommonResult<ZzjSbpzbCreateResp> create(@Validated @RequestBody ZzjSbpzbCreateReq req) {
        return super.create(req, ZzjSbpzbConvert.INSTANCE::convertToDTO, ZzjSbpzbConvert.INSTANCE::convertToCreateResp);
    }

    @PostMapping("update")
    @Operation(summary = "编辑自助设备配置表")
    public CommonResult<Boolean> update(@Validated @RequestBody ZzjSbpzbUpdateReq req) {
        return super.update(req, ZzjSbpzbConvert.INSTANCE::convertToDTO);
    }

    @DeleteMapping(value = "delete")
    @Operation(summary = "删除自助设备配置表")
    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
        return super.delete(id);
    }

    @GetMapping("list")
    @Operation(summary = "查询自助设备配置列表")
    public CommonResult<List<ZzjSbpzbListResp>> List() {
        ZzjSbpzbDTO zzjSbpzbDTO = new ZzjSbpzbDTO();
        zzjSbpzbDTO.setYxbz(Constants.YES);
        List<ZzjSbpzbDTO> sbpzbList = zzjSbpzbService.list(zzjSbpzbDTO);
        return CommonResult.success(ZzjSbpzbConvert.INSTANCE.convertToListResp(sbpzbList));
    }

    @PostMapping("batchSave")
    @Operation(summary = "用户设备配置批量保存")
    public CommonResult<Boolean> batchSave(@Validated @RequestBody ZzjSbpzbBatchSaveReq zzjYhsbpzbBatchSaveReq) {
        zzjSbpzbService.batchSave(ZzjSbpzbConvert.INSTANCE.convertToDTO(zzjYhsbpzbBatchSaveReq.getSbpzbList()));
        return CommonResult.success(true);
    }

    @GetMapping("cameraParam")
    @Operation(summary = "获取相机参数")
    public CommonResult<List<TextValue>> getCameraParam(@RequestParam("type") String type) {
        String prefix = "cameraParam." + type + Constants.DOT;
        List<ZzjSbpzbDTO> sbpzbDTOList = zzjSbpzbService.listByPrefix(prefix);
        List<TextValue> result = sbpzbDTOList.stream().map(zzjSbpzbDTO -> {
            String text = StringUtils.removeStart(zzjSbpzbDTO.getPzbm(), prefix);
            return new TextValue(text, zzjSbpzbDTO.getMrz());
        }).collect(Collectors.toList());
        return CommonResult.success(result);
    }

    @GetMapping("cameraParamTemplate")
    @Operation(summary = "获取相机参数模板")
    public CommonResult<CameraParamTemplateResp> getCameraParamTemplate() {
        String prefix = "cameraParam.template.";

        List<ZzjSbpzbDTO> sbpzbDTOList = zzjSbpzbService.listByPrefix(prefix);

        Map<String, Map<String, String>> map = new HashMap<>();
        sbpzbDTOList.forEach(zzjSbpzbDTO -> {
            String[] split = StringUtils.removeStart(zzjSbpzbDTO.getPzbm(), prefix).split("\\.");
            String type = split[0];
            String key = split[1];
            map.putIfAbsent(type, new HashMap<>());
            map.get(type).put(key, zzjSbpzbDTO.getMrz());
        });

        return CommonResult.success(ZzjSbpzbConvert.INSTANCE.convertToCameraParamTemplateResp(map));
    }


//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(ZzjSbpzbExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出自助设备配置表")
//    public void export(ZzjSbpzbPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "自助设备配置表" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, ZzjSbpzbExp.class,
//                    ZzjSbpzbConvert.INSTANCE::convertToDTO, ZzjSbpzbConvert.INSTANCE::convertToExp, response);
//    }

}
