package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwYwjkdzbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwYwjkdzbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwYwjkdzbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwYwjkdzbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwYwjkdzbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwYwjkdzbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwYwjkdzbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.WwYwjkdzbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 业务接口对照表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface WwYwjkdzbConvert {

    WwYwjkdzbConvert INSTANCE = Mappers.getMapper(WwYwjkdzbConvert.class);

    WwYwjkdzbDTO convert(WwYwjkdzbDO entity);

    @InheritInverseConfiguration(name="convert")
    WwYwjkdzbDO convertToDO(WwYwjkdzbDTO dto);

    WwYwjkdzbDTO convertToDTO(WwYwjkdzbPageReq req);

    WwYwjkdzbDTO convertToDTO(WwYwjkdzbCreateReq req);

    WwYwjkdzbDTO convertToDTO(WwYwjkdzbUpdateReq req);

    WwYwjkdzbPageResp convertToPageResp(WwYwjkdzbDTO dto);

    WwYwjkdzbViewResp convertToViewResp(WwYwjkdzbDTO dto);

    WwYwjkdzbCreateResp convertToCreateResp(WwYwjkdzbDTO dto);

}
