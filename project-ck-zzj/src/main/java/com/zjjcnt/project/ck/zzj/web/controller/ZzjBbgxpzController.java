package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.domain.PageParam;
import com.zjjcnt.common.core.domain.PageResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjBbgxpzConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjBbgxpzDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjBbgxpzCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjBbgxpzPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZzjBbgxpzUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjBbgxpzCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjBbgxpzLatestVersionResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjBbgxpzPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZzjBbgxpzViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjBbgxpzService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
* 自助机版本更新配置前端控制器
*
* <AUTHOR>
* @date 2024-07-30 15:27:57
*/

@Tag(name = "自助机版本更新配置")
@RestController
@RequestMapping("/zzjBbgxpz")
public class ZzjBbgxpzController extends AbstractCrudController<ZzjBbgxpzDTO> {

    @Autowired
    private ZzjBbgxpzService zzjBbgxpzService;

    @Override
    protected IBaseService getService() {
        return zzjBbgxpzService;
    }

    @GetMapping("page")
    @Operation(summary = "查询自助机版本更新配置")
    public CommonResult<PageResult<ZzjBbgxpzPageResp>> page(ZzjBbgxpzPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, ZzjBbgxpzConvert.INSTANCE::convertToDTO, ZzjBbgxpzConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看自助机版本更新配置详情")
    public CommonResult<ZzjBbgxpzViewResp> view(Long id) {
        return super.view(id, ZzjBbgxpzConvert.INSTANCE::convertToViewResp);
    }

    @PostMapping("create")
    @Operation(summary = "新增自助机版本更新配置")
    public CommonResult<ZzjBbgxpzCreateResp> create(@Validated @RequestBody ZzjBbgxpzCreateReq req) {
        return super.create(req, ZzjBbgxpzConvert.INSTANCE::convertToDTO, ZzjBbgxpzConvert.INSTANCE::convertToCreateResp);
    }

    @PostMapping("update")
    @Operation(summary = "编辑自助机版本更新配置")
    public CommonResult<Boolean> update(@Validated @RequestBody ZzjBbgxpzUpdateReq req) {
        return super.update(req, ZzjBbgxpzConvert.INSTANCE::convertToDTO);
    }

    @DeleteMapping(value = "delete")
    @Operation(summary = "删除自助机版本更新配置")
    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
        return super.delete(id);
    }

    @GetMapping("latestVersion")
    @Operation(summary = "获取最新版本")
    public CommonResult<ZzjBbgxpzLatestVersionResp> getLatestVersion(@RequestParam(defaultValue = "01", value = "cxlx")
                                                                         String cxlx) {
        ZzjBbgxpzDTO latestVersion = zzjBbgxpzService.findLatestVersion(cxlx);
        ZzjBbgxpzDTO lowestVersion = zzjBbgxpzService.findLowestVersion(cxlx);
        ZzjBbgxpzLatestVersionResp resp = ZzjBbgxpzConvert.INSTANCE.convertToLatestVersionResp(latestVersion);

        if (Objects.nonNull(lowestVersion)) {
            resp.setZdbbh(lowestVersion.getBbh());
        }

        return CommonResult.success(resp);
    }

//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(ZzjBbgxpzExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出自助机版本更新配置")
//    public void export(ZzjBbgxpzPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "自助机版本更新配置" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, ZzjBbgxpzExp.class,
//                    ZzjBbgxpzConvert.INSTANCE::convertToDTO, ZzjBbgxpzConvert.INSTANCE::convertToExp, response);
//    }

}
