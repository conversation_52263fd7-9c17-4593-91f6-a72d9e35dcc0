package com.zjjcnt.project.ck.zzj.service.ck.impl;

import org.springframework.stereotype.Service;
import com.mybatisflex.core.query.QueryWrapper;

import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.service.ck.RzbJkdyrzbService;
import com.zjjcnt.project.ck.zzj.entity.ck.RzbJkdyrzbDO;
import com.zjjcnt.project.ck.zzj.dto.ck.RzbJkdyrzbDTO;
import com.zjjcnt.project.ck.zzj.convert.ck.RzbJkdyrzbConvert;
import com.zjjcnt.project.ck.zzj.mapper.ck.RzbJkdyrzbMapper;

import com.zjjcnt.common.core.utils.ColumnUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.*;

/**
 * 第三方接口调用日志表ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-05-09 13:40:14
 */
@Service
public class RzbJkdyrzbServiceImpl extends AbstractBaseServiceImpl<RzbJkdyrzbMapper, RzbJkdyrzbDO, RzbJkdyrzbDTO> implements RzbJkdyrzbService {

    RzbJkdyrzbConvert convert = RzbJkdyrzbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(RzbJkdyrzbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(RzbJkdyrzbDO::getJkbh, dto.getJkbh(), StringUtils.isNotEmpty(dto.getJkbh()));
        query.eq(RzbJkdyrzbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(RzbJkdyrzbDO::getKssj, dto.getKssj(), StringUtils.isNotEmpty(dto.getKssj()));
        query.eq(RzbJkdyrzbDO::getJssj, dto.getJssj(), StringUtils.isNotEmpty(dto.getJssj()));
        query.eq(RzbJkdyrzbDO::getFhjg, dto.getFhjg(), StringUtils.isNotEmpty(dto.getFhjg()));
        query.eq(RzbJkdyrzbDO::getFhms, dto.getFhms(), Objects.nonNull(dto.getFhms()));
        query.eq(RzbJkdyrzbDO::getFhjls, dto.getFhjls(), StringUtils.isNotEmpty(dto.getFhjls()));
        query.ge(RzbJkdyrzbDO::getRksj, dto.getRksjStart(), StringUtils.isNotEmpty(dto.getRksjStart()));
        query.le(RzbJkdyrzbDO::getRksj, dto.getRksjEnd(), StringUtils.isNotEmpty(dto.getRksjEnd()));
        return query;
    }

    @Override
    public RzbJkdyrzbDTO convertToDTO(RzbJkdyrzbDO rzbJkdyrzbDO) {
        return convert.convert(rzbJkdyrzbDO);
    }

    @Override
    public RzbJkdyrzbDO convertToDO(RzbJkdyrzbDTO rzbJkdyrzbDTO) {
        return convert.convertToDO(rzbJkdyrzbDTO);
    }
}
