package com.zjjcnt.project.ck.zzj.third.sfzycj;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.dto.XtXtkzcsbDTO;
import com.zjjcnt.project.ck.sysadmin.service.XtXtkzcsbService;
import com.zjjcnt.project.ck.zzj.dto.ck.RzbJkdyrzbDTO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.service.ck.RzbJkdyrzbService;
import com.zjjcnt.project.ck.zzj.third.sfzycj.req.SfzycjReq;
import com.zjjcnt.project.ck.zzj.third.sfzycj.resp.SfzycjFhxxResp;
import com.zjjcnt.project.ck.zzj.third.sfzycj.resp.SfzycjResultResp;
import com.zjjcnt.project.ck.zzj.third.sfzycj.resp.SfzycjTokenResp;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.io.HttpClientConnectionManager;
import org.apache.hc.client5.http.ssl.DefaultClientTlsStrategy;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.ssl.SSLContexts;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * 身份证预采集
 *
 * <AUTHOR>
 * @date 2025-05-08 16:03:00
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SfzYcjClient {

    private static final String SM4_KEY = "LmN8oP4qR6sT@uV1";//对方提供的加解密KEY
    private static final String KZLB_SFZYCJ = "1652";

    private final XtXtkzcsbService xtkzcsbService;
    private final RzbJkdyrzbService rzbJkdyrzbService;
    private RestTemplate restTemplate;

    private volatile String token;

    @PostConstruct
    public void init() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, TrustAllStrategy.INSTANCE).build();
        DefaultClientTlsStrategy clientTlsStrategy = new DefaultClientTlsStrategy(sslContext, NoopHostnameVerifier.INSTANCE);

        HttpClientConnectionManager clientConnectionManager = PoolingHttpClientConnectionManagerBuilder.create()
                .setTlsSocketStrategy(clientTlsStrategy).build();

        CloseableHttpClient httpClient =  HttpClients.custom()
                .setConnectionManager(clientConnectionManager)
                .build();

        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(10000);
        httpRequestFactory.setConnectTimeout(10000);
        httpRequestFactory.setReadTimeout(30000);

        httpRequestFactory.setHttpClient(httpClient);

        restTemplate = new RestTemplate(httpRequestFactory);
    }

    /**
     * 调用电子身份证项目的服务
     * 根据创建时间排序后取一条
     *
     * @param gmsfhm 公民身份号码
     * @param startTime 开始时间 yyyy-MM-dd HH:mm:ss
     * @param endTime 结束时间 yyyy-MM-dd HH:mm:ss
     * @return 身份证预采集信息
     */
    public SfzycjFhxxResp getSfzycjxxForDzsfz(String gmsfhm, String startTime, String endTime) {

        RzbJkdyrzbDTO rzb = new RzbJkdyrzbDTO();
        rzb.setJkbh("sfzycjxxhq");
        rzb.setGmsfhm(gmsfhm);
        rzb.setKssj(startTime);
        rzb.setJssj(endTime);
        rzb.setRksj(ServerTimeUtils.getCurrentTime());

        try {
            //调用接口获取身份证预采集信息
            SfzycjResultResp result = getSfzycjxx(gmsfhm, startTime, endTime);

            rzb.setFhjg(result.getCode());
            rzb.setFhms(result.getMsg());
            rzb.setFhjls(result.getData() == null ? "0" : String.valueOf(result.getData().size()));

            if ("0".equals(result.getCode())) {
                //调用成功
                if (!CollectionUtils.isEmpty(result.getData())) {
                    List<SfzycjFhxxResp> lists = result.getData();
                    lists.sort((t1, t2) -> t2.getCreateTime().compareTo(t1.getCreateTime()));
                    SfzycjFhxxResp sfzycjFhxxResp = lists.getFirst();
                    //解密相关数据
                    sfzycjFhxxResp.decryptWzxx(SM4_KEY);
                    return sfzycjFhxxResp;
                }
            } else {
                throw new ServiceException(CkZzjErrorCode.SFZYCJ_API_ERROR, "调用身份证预采集信息获取接口异常:" + result.getMsg());
            }
        } finally {
            rzbJkdyrzbService.insert(rzb);
        }
        return null;
    }

    /**
     * 调用电子身份证项目的服务，获取身份证预采集信息
     * <a href="https://41.190.20.94:443/artemis/api/v1/ck/queryCkInfo">...</a>
     */
    private SfzycjResultResp getSfzycjxx(String gmsfhm, String startTime, String endTime) {
        XtXtkzcsbDTO pokzcs = xtkzcsbService.findByKzlb(KZLB_SFZYCJ);
        if (pokzcs == null) {
            throw new ServiceException(CkZzjErrorCode.SFZYCJ_API_ERROR, "身份证预采集，获取系统控制参数1652异常");
        }

        String serviceUrl = pokzcs.getBz() + "/api/v1/ck/queryCkInfo";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("access_token", getToken());

        SfzycjReq req = new SfzycjReq();
        SymmetricCrypto sm4 = SmUtil.sm4(SM4_KEY.getBytes(StandardCharsets.UTF_8));
        req.setIdCard(sm4.encryptHex(gmsfhm));
        req.setStartTime(startTime);
        req.setEndTime(endTime);

        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.toJsonString(req), headers);

        String result = restTemplate.postForObject(serviceUrl, entity, String.class);
        log.debug("身份证预采集信息获取，海康服务，返回结果：" + result);

        SfzycjResultResp resp = JsonUtils.parseObject(result, SfzycjResultResp.class);

        //如果token为空，或者token超时了，则更新token
        if ("0x02401004".equals(resp.getCode()) || resp.getMsg().contains("token expired")
                || "0x0240100".equals(resp.getCode()) || resp.getMsg().contains("Token or AppKey is null")) {
            refreshToken();
            return getSfzycjxx(gmsfhm, startTime, endTime);
        }

        return resp;
    }

    private String getToken() {
        if (token == null) {
            synchronized (this) {
                if (token == null) {
                    return refreshToken();
                }
            }
        }
        return token;
    }

    /**
     * 调用电子身份证项目的服务，获取接口token
     * <a href="https://41.190.20.94:443/artemis/oauth/token">...</a>
     */
    private String refreshToken() {
        XtXtkzcsbDTO pokzcs = xtkzcsbService.findByKzlb(KZLB_SFZYCJ);
        if (pokzcs == null) {
            throw new ServiceException(CkZzjErrorCode.SFZYCJ_API_ERROR, "身份证预采集，获取系统控制参数1652异常");
        }

        String serviceUrl = "https://127.0.0.1:17446/oauth/token";
//        String serviceUrl = pokzcs.getBz() + "/oauth/token";

        //组织数据
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("client_id", pokzcs.getKzz());
        params.add("client_secret", pokzcs.getMrz());

        String result = restTemplate.postForObject(serviceUrl, params, String.class);

        log.debug("身份证预采集token获取，海康服务，返回结果：" + result);

        if (StringUtils.isBlank(result)) {
            throw new ServiceException(CkZzjErrorCode.SFZYCJ_API_ERROR, "调用身份证预采集获取token接口异常");
        } else {
            SfzycjTokenResp vo = JsonUtils.parseObject(result, SfzycjTokenResp.class);
            token = vo.getAccessToken();
            return token;
        }
    }


}
