package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 自助机版本更新配置DO
 *
 * <AUTHOR>
 * @date 2024-07-30 15:27:57
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZzjBbgxpzDTO
 */
@Data
@Table("zzj_bbgxpz")
public class ZzjBbgxpzDO implements IdEntity<Long> {
    private static final long serialVersionUID = -3015798028004918963L;

    /**
     * id
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
    private Long id;

    /**
     * 版本号
     */
    @Column(value = "bbh")
    private String bbh;

    /**
     * 版本号格式化
     */
    @Column(value = "bbhgsh")
    private String bbhgsh;

    /**
     * 构建批次
     */
    @Column(value = "gjpc")
    private String gjpc;

    /**
     * 程序类型 01-自助机
     */
    @Column(value = "cxlx")
    private String cxlx;

    /**
     * 是否强制更新
     */
    @Column(value = "sfqzgx")
    private String sfqzgx;

    /**
     * 更新内容
     */
    @Column(value = "gxnr")
    private String gxnr;

    /**
     * 有效标志
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 修改时间
     */
    @Column(value = "xgsj")
    private String xgsj;


    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
