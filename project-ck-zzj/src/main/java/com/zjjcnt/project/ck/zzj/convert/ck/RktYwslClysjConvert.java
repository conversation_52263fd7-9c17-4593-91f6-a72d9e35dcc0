package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.sysadmin.dto.RktYwslClysjDTO;
import com.zjjcnt.project.ck.zzj.web.request.RktYwslClysjReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 业务受理材料元数据Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface RktYwslClysjConvert {

    RktYwslClysjConvert INSTANCE = Mappers.getMapper(RktYwslClysjConvert.class);

    RktYwslClysjDTO convertToDTO(RktYwslClysjReq req);

    List<RktYwslClysjDTO> convertToDTO(List<RktYwslClysjReq> req);

}
