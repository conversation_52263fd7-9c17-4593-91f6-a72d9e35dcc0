package com.zjjcnt.project.ck.zzj.third.crj.req;

import lombok.Data;

/**
 * 保存预制照片请求
 *
 * <AUTHOR>
 * @date 2024-08-09 13:54:00
 */
@Data
public class CrjSavePaperPhotoReq {

    /**
     * 非空， 相片编号
     */
    private String xpbh;

    /**
     * 非空， 相片内容. 应传递 BASE64 编码后的字符串
     */
    private String xpnr;

    /**
     * 相片系统名称
     */
    private String xtmc;

    /**
     * 母片相片内容. 应传递 BASE64 编码后的字符串
     */
    private String xpall;

    /**
     * 是否人工强制通过（0否1是）。
     * 默认为0，如质检不通过，人工判断符合照片要求，即可上传1
     */
    private String sfrgqztg;

    /**
     *  新增字段，拍摄日期 yyyyMMdd
     */
    private String psrq;

    /**
     * 质检状态 1已质检
     */
    private String status;
    /**
     * 采集终端编码
     */
    private String cjzdbm;

}

