package com.zjjcnt.project.ck.zzj.third.rop.resp;

import lombok.Data;

/**
 * 办证资格审查resp
 *
 * <AUTHOR>
 * @date 2024-10-14 11:31:00
 */
@Data
public class KssfzBzzgscRopResp {

    /**
     * 办证资格判断标志
     * 0不允许
     * 1允许
     */
    private String bzzgpdbz;

    /**
     * 办证资格判断描述
     */
    private String bzzgpdms;

    /**
     * 性别代码
     */
    private String xbdm;

    /**
     * 民族代码
     */
    private String mzdm;

    /**
     * 户籍地省市县区
     */
    private String hjdssxq;

    /**
     * 户籍地详址
     */
    private String hjdxz;

    /**
     * 人员相片
     */
    private String ryxp;

    /**
     * 持有证件有效期开始时间
     */
    private String cyzjyxqkssj;

    /**
     * 持有证件有效期结束时间
     */
    private String cyzjyxqjssj;

    /**
     * 持有证件相片
     */
    private String cyzjxp;

    /**
     * 持有证件指纹一指位代码
     */
    private String cyzjzwyzwdm;

    /**
     * 持有证件指纹一特征数据
     */
    private String cyzjzwytzsj;

    /**
     * 持有证件指纹二指位代码
     */
    private String cyzjzwezwdm;

    /**
     * 持有证件指纹二特征数据
     */
    private String cyzjzwetzsj;

    /**
     * 办证照片
     */
    private String bzzp;

    /**
     * 办证照片id
     */
    private String bzzpid;
}
