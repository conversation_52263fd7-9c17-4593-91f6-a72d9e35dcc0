package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 外围接口参数表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.WwJkcsbDTO
 */
@Data
@Table("ww_jkcsb")
public class WwJkcsbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -8443954788519816368L;

    /**
     * 参数ID
     */
    @Id(keyType = KeyType.Auto)
    private Long csid;

    /**
     * 接口类别
     */
    @Column(value = "jklb")
    private String jklb;

    /**
     * 接口类别名称
     */
    @Column(value = "jklbmc")
    private String jklbmc;

    /**
     * 接口类名称
     */
    @Column(value = "jklmc")
    private String jklmc;

    /**
     * 接口参数
     */
    @Column(value = "jkcs")
    private String jkcs;

    /**
     * 结果类型
     */
    @Column(value = "jglx")
    private String jglx;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;

    /**
     * 启用标志
     */
    @Column(value = "qybz")
    private String qybz;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;


    @Override
    public Long getId() {
        return this.csid;
    }

    @Override
    public void setId(Long id) {
        this.csid = id;
    }
}
