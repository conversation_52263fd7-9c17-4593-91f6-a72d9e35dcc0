package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtLssfzDyxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzDyxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtLssfzDyxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtLssfzDyxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtLssfzDyxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 临时身份证打印信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Service
public class ZjtLssfzDyxxbServiceImpl extends AbstractBaseServiceImpl<ZjtLssfzDyxxbMapper, ZjtLssfzDyxxbDO, ZjtLssfzDyxxbDTO> implements ZjtLssfzDyxxbService {

    ZjtLssfzDyxxbConvert convert = ZjtLssfzDyxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtLssfzDyxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtLssfzDyxxbDO::getLsslid, dto.getLsslid(), StringUtils.isNotEmpty(dto.getLsslid()));
        query.eq(ZjtLssfzDyxxbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtLssfzDyxxbDO::getLsjmsfzkh, dto.getLsjmsfzkh(), StringUtils.isNotEmpty(dto.getLsjmsfzkh()));
        query.eq(ZjtLssfzDyxxbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.ge(ZjtLssfzDyxxbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtLssfzDyxxbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtLssfzDyxxbDO::getCzyip, dto.getCzyip(), StringUtils.isNotEmpty(dto.getCzyip()));
        query.eq(ZjtLssfzDyxxbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        return query;
    }

    @Override
    public ZjtLssfzDyxxbDTO convertToDTO(ZjtLssfzDyxxbDO zjtLssfzDyxxbDO) {
        return convert.convert(zjtLssfzDyxxbDO);
    }

    @Override
    public ZjtLssfzDyxxbDO convertToDO(ZjtLssfzDyxxbDTO zjtLssfzDyxxbDTO) {
        return convert.convertToDO(zjtLssfzDyxxbDTO);
    }
}
