package com.zjjcnt.project.ck.zzj.third.rop.req;

import lombok.Data;

/**
 * 受理结束提交req
 *
 * <AUTHOR>
 * @date 2024-10-15 16:07:00
 */
@Data
public class KssfzSljstjRopReq implements IZdbzsbKssfzRopReq {

    /**
     * 设备秘钥
     */
    private String license;

    /**
     * 用户登录名
     */
    private String yhdlm;

    /**
     * 设备ip
     */
    private String sbip;

    /**
     * 身份证受理id
     */
    private String zjslid;

    /**
     * 操作状态 0作废1办理结束
     */
    private String czzt;
}
