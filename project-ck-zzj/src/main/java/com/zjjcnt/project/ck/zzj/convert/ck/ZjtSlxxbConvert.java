package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSlxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSlxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSlxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSlxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtSlxxbDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZjtSlxxbExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 居民身份证受理信息表Convert
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/
@Mapper
public interface ZjtSlxxbConvert {

    ZjtSlxxbConvert INSTANCE = Mappers.getMapper(ZjtSlxxbConvert.class);

    ZjtSlxxbDTO convert(ZjtSlxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtSlxxbDO convertToDO(ZjtSlxxbDTO dto);

    ZjtSlxxbDTO convertToDTO(ZjtSlxxbPageReq req);

    ZjtSlxxbDTO convertToDTO(ZjtSlxxbCreateReq req);

    ZjtSlxxbDTO convertToDTO(ZjtSlxxbUpdateReq req);

    ZjtSlxxbPageResp convertToPageResp(ZjtSlxxbDTO dto);

    ZjtSlxxbViewResp convertToViewResp(ZjtSlxxbDTO dto);

    ZjtSlxxbCreateResp convertToCreateResp(ZjtSlxxbDTO dto);

    ZjtSlxxbExp convertToExp(ZjtSlxxbDTO dto);

}
