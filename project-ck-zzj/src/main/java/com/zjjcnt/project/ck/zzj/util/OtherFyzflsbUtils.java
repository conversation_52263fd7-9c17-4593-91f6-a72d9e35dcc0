package com.zjjcnt.project.ck.zzj.util;

import com.zjjcnt.common.core.dict.Dictionary;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.utils.ApplicationContextHelper;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.sysadmin.manager.ZjSlhGenerator;
import com.zjjcnt.project.ck.core.util.DwUtils;
import com.zjjcnt.project.ck.zzj.domain.CkFyzflsbJkdhGenerateInfo;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;


//二维码模板相关配置：FWT_XT_XTPZ表

/**
 * 费用支付流水表相关工具
 **/
public class OtherFyzflsbUtils {


    public static final String OTHER_FYZFLSB_QR_TEMPLATE = "other.fyzflsb.qr.template";
    public static final String OTHER_FYZFLSB_QR_WIDTH = "other.fyzflsb.qr.width";
    public static final String OTHER_FYZFLSB_QR_HEIGHT = "other.fyzflsb.qr.height";
    public static final String OTHER_FYZFLSB_JKDH_LYQDBH = "other.fyzflsb.jkdh.lyqdbh";

    public static final String CK_FYZFLSB_JKDH_RANDOM_LENGTH = "ck.fyzflsb.jkdh.random.length";//缴款单随机数位数


    /**
     * 获取缴款单来源渠道编号
     */
    public static String getJkdlyqdbh(String sfywlx) {
        return Dictionary.getValue(SysadminConstants.FWT_XT_XTPZ, buildLyqdbhKey(sfywlx));
    }

//    public static String getJkdlyqdbh(ZjtFyzflsb zjtFyzflsb) {
//        if (StringUtils.isNotEmpty(zjtFyzflsb.getJkdlyqdbh())) {
//            return zjtFyzflsb.getJkdlyqdbh();
//        } else {
//            return getJkdlyqdbh(zjtFyzflsb.getSfywlx());
//        }
//    }


    /**
     * 获取缴款单号
     * sldsjgsdwdm 受理地数据归属单位代码
     * sfywlx 收费业务类型
     *
     */
    public static CkFyzflsbJkdhGenerateInfo generateNewJkdh(String sldsjgsdwdm, String sfywlx) {
        //获取缴款单来源渠道编号
        String jkdlyqdbh = getJkdlyqdbh(sfywlx);
        if (StringUtils.isEmpty(jkdlyqdbh)) {
            throw new ServiceException(500, "收费业务类型["+sfywlx+"]缴款单来源渠道编号未在FWT_XT_XTPZ设置.");
        }
        ZjSlhGenerator zjSlhGenerator = ApplicationContextHelper.getBean(ZjSlhGenerator.class);
        //缴款单派出所顺序号
        String jkdhSxh = zjSlhGenerator.generateNewJkdh(DwUtils.expandSjgsdw(sldsjgsdwdm));
        if (StringUtils.isEmpty(jkdhSxh)) {
            throw new ServiceException(500, "获取缴款单号派出所顺序号出错");
        }

        //增加流水号的随机码，避免流水号被猜出造成隐私泄露
        String jdkSxhRandom = builderJkdhRandomStr();
        if (StringUtils.isNotEmpty(jdkSxhRandom)) {
            jkdhSxh = jkdhSxh + jdkSxhRandom;
        }

        CkFyzflsbJkdhGenerateInfo info = new CkFyzflsbJkdhGenerateInfo();
        info.setSldsjgsdwdm(sldsjgsdwdm);
        info.setJkdlyqdbh(jkdlyqdbh);
        info.setJkdhsxh(jkdhSxh);
        info.setSfywlx(sfywlx);
        info.setJkdh(jkdlyqdbh + sfywlx + jkdhSxh);
        return info ;
    }

    /**
     * 生成指定位数的随机码
     */
    private static String builderJkdhRandomStr() {
        //生成4位随机码
//        String randomLength = PropertiesUtils.get(CK_FYZFLSB_JKDH_RANDOM_LENGTH);
        String randomLength = Dictionary.getValue(SysadminConstants.FWT_XT_XTPZ, CK_FYZFLSB_JKDH_RANDOM_LENGTH,
                "4");
        if (StringUtils.isNotEmpty(randomLength)) {
            int randomLengthInt = Integer.parseInt(randomLength);
            if (randomLengthInt > 0) {
                return RandomStringUtils.secure().nextNumeric(randomLengthInt);
            }
        }
        return "";
    }
//
//
//    /**
//     * 根据缴款单号生成二维码
//     */
//
//    public static String generateJkdhBase64QR(String sfywlx,String jkdh) throws WriterException, IOException {
//        String fileType  = "png" ;
////        String url = "";//;PropertiesUtils.get(CK_FYZFLSB_QR_URL);
////        String width = "400";//PropertiesUtils.get(CK_FYZFLSB_QR_WIDTH,"100");
////        String height = "400";//PropertiesUtils.get(CK_FYZFLSB_QR_HEIGHT,"100");
//        String content = buildQrContent(sfywlx,jkdh);
//        String width = PropertiesUtils.get(buildQRWidthKey(sfywlx), "200");
//        String height = PropertiesUtils.get(buildQRHeightKey(sfywlx), "200");
//        ByteArrayOutputStream baos = new ByteArrayOutputStream();
//
//        try {
//            BufferedImage bufferedImage = QRCodeUtils.encode(content, Integer.parseInt(width), Integer.parseInt(height), 1);//0：不留白
//            ImageIO.write(bufferedImage, fileType, baos);
//        } finally {
//            IOUtils.closeQuietly(baos);
//        }
//
////        FileUtils.writeByteArrayToFile(new File("d:/abc.png"),baos.toByteArray());
//        return CkCommonUtils.encodeBase64(baos.toByteArray());
//    }
//
//    public static String buildQrContent(String sfywlx,String jkdh) {
//        String template = PropertiesUtils.get(buildTemplateKey(sfywlx));
//        if (StringUtils.isEmpty(template)) {
//            throw new ServiceException("收费业务类型["+sfywlx+"]二维码内容模板未在FWT_XT_XTPZ配置");
//        }
//        return template.replaceAll("\\{jkdh\\}", jkdh);
//    }
//
//
//    /**
//     * 根据sfywlx走不同的系统配置
//     **/
//    private static String buildTemplateKey(String sfywlx) {
//        if (sfywlx.startsWith("0")) {
//            return CK_FYZFLSB_QR_TEMPLATE;
//        } else {
//            return OTHER_FYZFLSB_QR_TEMPLATE + "_" + sfywlx.substring(0,1);
//        }
//    }
//
    private static String buildLyqdbhKey(String sfywlx) {
        if (sfywlx.startsWith("0")) {
            return CkFyzflsbUtils.CK_FYZFLSB_JKDH_LYQDBH;
        } else {
            return OTHER_FYZFLSB_JKDH_LYQDBH + "_" + sfywlx.charAt(0);
        }
    }
//
//    private static String buildQRWidthKey(String sfywlx) {
//        if (sfywlx.startsWith("0")) {
//            return CK_FYZFLSB_QR_WIDTH;
//        } else {
//            return OTHER_FYZFLSB_QR_WIDTH + "_" + sfywlx.substring(0,1);
//        }
//    }
//
//    private static String buildQRHeightKey(String sfywlx) {
//        if (sfywlx.startsWith("0")) {
//            return CK_FYZFLSB_QR_HEIGHT;
//        } else {
//            return OTHER_FYZFLSB_QR_HEIGHT + "_" + sfywlx.substring(0,1);
//        }
//    }



}
