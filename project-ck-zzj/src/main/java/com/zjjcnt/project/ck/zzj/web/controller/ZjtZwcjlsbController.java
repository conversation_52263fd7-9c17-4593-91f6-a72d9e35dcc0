package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtZwcjlsbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjlsbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjlsbCreateResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjlsbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Base64;

/**
* 前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:39:12
*/

@Tag(name = "指纹采集临时表")
@RestController
@RequestMapping("/zjtZwcjlsb")
public class ZjtZwcjlsbController extends AbstractCrudController<ZjtZwcjlsbDTO> {

    @Autowired
    private ZjtZwcjlsbService zjtZwcjlsbService;

    @Override
    protected IBaseService getService() {
        return zjtZwcjlsbService;
    }

//    @GetMapping("page")
//    @Operation(summary = "查询")
//    public CommonResult<PageResult<ZjtZwcjlsbPageResp>> page(ZjtZwcjlsbPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, ZjtZwcjlsbConvert.INSTANCE::convertToDTO, ZjtZwcjlsbConvert.INSTANCE::convertToPageResp);
//    }
//
//    @GetMapping("view")
//    @Operation(summary = "查看详情")
//    public CommonResult<ZjtZwcjlsbViewResp> view(String id) {
//        return super.view(id, ZjtZwcjlsbConvert.INSTANCE::convertToViewResp);
//    }

    @PostMapping("create")
    @Operation(summary = "新增 F23920")
    public CommonResult<ZjtZwcjlsbCreateResp> create(@RequestBody ZjtZwcjlsbCreateReq req) {
        if (StringUtils.isNotBlank(req.getBase64zwytxsj())){
            req.setZwytxsj(Base64.getDecoder().decode(req.getBase64zwytxsj()));
        }
        if (StringUtils.isNotBlank(req.getBase64zwyzwtzsj())){
            req.setZwyzwtzsj(Base64.getDecoder().decode(req.getBase64zwyzwtzsj()));
        }
        if (StringUtils.isNotBlank(req.getBase64zwetxsj())){
            req.setZwetxsj(Base64.getDecoder().decode(req.getBase64zwetxsj()));
        }
        if (StringUtils.isNotBlank(req.getBase64zwezwtzsj())){
            req.setZwezwtzsj(Base64.getDecoder().decode(req.getBase64zwezwtzsj()));
        }
        return super.create(req, ZjtZwcjlsbConvert.INSTANCE::convertToDTO, ZjtZwcjlsbConvert.INSTANCE::convertToCreateResp);
    }

//    @PostMapping("update")
//    @Operation(summary = "编辑")
//    public CommonResult<Boolean> update(@RequestBody ZjtZwcjlsbUpdateReq req) {
//        return super.update(req, ZjtZwcjlsbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }
//

}
