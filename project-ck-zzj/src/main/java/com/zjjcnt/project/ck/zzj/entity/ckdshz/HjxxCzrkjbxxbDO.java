package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 常住人口基本信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO
 */
@Crypto
@Data
@Table("hjxx_czrkjbxxb")
public class HjxxCzrkjbxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = 1648855223287915891L;

    /**
     * 人员内部ID
     */
    @Id(keyType = KeyType.Auto)
    private Long rynbid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private Long ryid;

    /**
     * 户号内部ID
     */
    @Column(value = "hhnbid")
    private Long hhnbid;

    /**
     * 门（楼）牌内部ID
     */
    @Column(value = "mlpnbid")
    private Long mlpnbid;

    /**
     * 照片ID
     */
    @Column(value = "zpid")
    private Long zpid;

    /**
     * 内部身份证ID
     */
    @Column(value = "nbsfzid")
    private Long nbsfzid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 签发机关
     */
    @Column(value = "qfjg")
    private String qfjg;

    /**
     * 有效期限起始日期
     */
    @Column(value = "yxqxqsrq")
    private String yxqxqsrq;

    /**
     * 有效期限截止日期
     */
    @Column(value = "yxqxjzrq")
    private String yxqxjzrq;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 曾用名
     */
    @Column(value = "cym")
    private String cym;

    /**
     * 姓名拼音
     */
    @Crypto
    @Column(value = "xmpy")
    private String xmpy;

    /**
     * 曾用名拼音
     */
    @Column(value = "cympy")
    private String cympy;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 民族
     */
    @Column(value = "mz")
    private String mz;

    /**
     * 出生日期
     */
    @Column(value = "csrq")
    private String csrq;

    /**
     * 出生时间
     */
    @Column(value = "cssj")
    private String cssj;

    /**
     * 出生地国家（地区）
     */
    @Column(value = "csdgjdq")
    private String csdgjdq;

    /**
     * 出生地省市县（区）
     */
    @Column(value = "csdssxq")
    private String csdssxq;

    /**
     * 出生地详址
     */
    @Column(value = "csdxz")
    private String csdxz;

    /**
     * 电话号码
     */
    @Column(value = "dhhm")
    private String dhhm;

    /**
     * 监护人一姓名
     */
    @Crypto
    @Column(value = "jhryxm")
    private String jhryxm;

    /**
     * 监护人一公民身份号码
     */
    @Crypto
    @Column(value = "jhrygmsfhm")
    private String jhrygmsfhm;

    /**
     * 监护人一监护关系
     */
    @Column(value = "jhryjhgx")
    private String jhryjhgx;

    /**
     * 监护人二姓名
     */
    @Crypto
    @Column(value = "jhrexm")
    private String jhrexm;

    /**
     * 监护人二公民身份号码
     */
    @Crypto
    @Column(value = "jhregmsfhm")
    private String jhregmsfhm;

    /**
     * 监护人二监护关系
     */
    @Column(value = "jhrejhgx")
    private String jhrejhgx;

    /**
     * 父亲姓名
     */
    @Crypto
    @Column(value = "fqxm")
    private String fqxm;

    /**
     * 父亲公民身份号码
     */
    @Crypto
    @Column(value = "fqgmsfhm")
    private String fqgmsfhm;

    /**
     * 母亲姓名
     */
    @Crypto
    @Column(value = "mqxm")
    private String mqxm;

    /**
     * 母亲公民身份号码
     */
    @Crypto
    @Column(value = "mqgmsfhm")
    private String mqgmsfhm;

    /**
     * 配偶姓名
     */
    @Crypto
    @Column(value = "poxm")
    private String poxm;

    /**
     * 配偶公民身份号码
     */
    @Crypto
    @Column(value = "pogmsfhm")
    private String pogmsfhm;

    /**
     * 籍贯国家（地区）
     */
    @Column(value = "jggjdq")
    private String jggjdq;

    /**
     * 籍贯省市县（区）
     */
    @Column(value = "jgssxq")
    private String jgssxq;

    /**
     * 宗教信仰
     */
    @Column(value = "zjxy")
    private String zjxy;

    /**
     * 文化程度
     */
    @Column(value = "whcd")
    private String whcd;

    /**
     * 婚姻状况
     */
    @Column(value = "hyzk")
    private String hyzk;

    /**
     * 兵役状况
     */
    @Column(value = "byzk")
    private String byzk;

    /**
     * 身高
     */
    @Column(value = "sg")
    private String sg;

    /**
     * 血型
     */
    @Column(value = "xx")
    private String xx;

    /**
     * 职业
     */
    @Column(value = "zy")
    private String zy;

    /**
     * 职业类别
     */
    @Column(value = "zylb")
    private String zylb;

    /**
     * 服务处所
     */
    @Column(value = "fwcs")
    private String fwcs;

    /**
     * 信息级别
     */
    @Column(value = "xxjb")
    private String xxjb;

    /**
     * 何时迁来
     */
    @Column(value = "hsql")
    private String hsql;

    /**
     * 何因迁来
     */
    @Column(value = "hyql")
    private String hyql;

    /**
     * 何国家（地区）迁来
     */
    @Column(value = "hgjdqql")
    private String hgjdqql;

    /**
     * 何省市县（区）迁来
     */
    @Column(value = "hssxqql")
    private String hssxqql;

    /**
     * 何详址迁来
     */
    @Column(value = "hxzql")
    private String hxzql;

    /**
     * 何时来本址
     */
    @Column(value = "hslbz")
    private String hslbz;

    /**
     * 何因来本址
     */
    @Column(value = "hylbz")
    private String hylbz;

    /**
     * 何国家（地区）来本址
     */
    @Column(value = "hgjdqlbz")
    private String hgjdqlbz;

    /**
     * 何省市县（区）来本址
     */
    @Column(value = "hsssqlbz")
    private String hsssqlbz;

    /**
     * 何详址来本址
     */
    @Column(value = "hxzlbz")
    private String hxzlbz;

    /**
     * 死亡日期
     */
    @Column(value = "swrq")
    private String swrq;

    /**
     * 死亡注销类别
     */
    @Column(value = "swzxlb")
    private String swzxlb;

    /**
     * 死亡注销日期
     */
    @Column(value = "swzxrq")
    private String swzxrq;

    /**
     * 迁出日期
     */
    @Column(value = "qcrq")
    private String qcrq;

    /**
     * 迁出注销类别
     */
    @Column(value = "qczxlb")
    private String qczxlb;

    /**
     * 迁往地国家（地区）
     */
    @Column(value = "qwdgjdq")
    private String qwdgjdq;

    /**
     * 迁往地省市县（区）
     */
    @Column(value = "qwdssxq")
    private String qwdssxq;

    /**
     * 迁往地详址
     */
    @Column(value = "qwdxz")
    private String qwdxz;

    /**
     * 出生证明编号
     */
    @Column(value = "cszmbh")
    private String cszmbh;

    /**
     * 出生证签发日期
     */
    @Column(value = "cszqfrq")
    private String cszqfrq;

    /**
     * 行业类别
     */
    @Column(value = "hylb")
    private String hylb;

    /**
     * 其他省市县（区）
     */
    @Column(value = "qtssxq")
    private String qtssxq;

    /**
     * 其他住址
     */
    @Column(value = "qtzz")
    private String qtzz;

    /**
     * 人员类别
     */
    @Column(value = "rylb")
    private String rylb;

    /**
     * 户别
     */
    @Column(value = "hb")
    private String hb;

    /**
     * 与户主关系
     */
    @Column(value = "yhzgx")
    private String yhzgx;

    /**
     * 人员状态
     */
    @Column(value = "ryzt")
    private String ryzt;

    /**
     * 人员锁定状态
     */
    @Column(value = "rysdzt")
    private String rysdzt;

    /**
     * 离线DBID
     */
    @Column(value = "lxdbid")
    private Long lxdbid;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;

    /**
     * 记录标志
     */
    @Column(value = "jlbz")
    private String jlbz;

    /**
     * 业务内容
     */
    @Column(value = "ywnr")
    private String ywnr;

    /**
     * 创建户籍业务ID
     */
    @Column(value = "cjhjywid")
    private Long cjhjywid;

    /**
     * 撤除户籍业务ID
     */
    @Column(value = "cchjywid")
    private Long cchjywid;

    /**
     * 起用时间
     */
    @Column(value = "qysj")
    private String qysj;

    /**
     * 结束时间
     */
    @Column(value = "jssj")
    private String jssj;

    /**
     * 冲销标志
     */
    @Column(value = "cxbz")
    private String cxbz;

    /**
     * 证件类别
     */
    @Column(value = "zjlb")
    private String zjlb;

    /**
     * 街路巷
     */
    @Column(value = "jlx")
    private String jlx;

    /**
     * 门（楼）牌号
     */
    @Column(value = "mlph")
    private String mlph;

    /**
     * 门（楼）详址
     */
    @Column(value = "mlxz")
    private String mlxz;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 责任区
     */
    @Column(value = "zrq")
    private String zrq;

    /**
     * 乡镇（街道）
     */
    @Column(value = "xzjd")
    private String xzjd;

    /**
     * 居（村）委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 排序号
     */
    @Column(value = "pxh")
    private String pxh;

    /**
     * 门（楼）牌ID
     */
    @Column(value = "mlpid")
    private Long mlpid;

    /**
     * 省市县（区）
     */
    @Column(value = "ssxq")
    private String ssxq;

    /**
     * 户号
     */
    @Column(value = "hh")
    private String hh;

    /**
     * 户类型
     */
    @Column(value = "hlx")
    private String hlx;

    /**
     * 户号ID
     */
    @Column(value = "hhid")
    private Long hhid;

    /**
     * 变动范围
     */
    @Column(value = "bdfw")
    private String bdfw;

    /**
     * 信息启用时间
     */
    @Column(value = "xxqysj")
    private String xxqysj;

    /**
     * 电话号码2
     */
    @Column(value = "dhhm2")
    private String dhhm2;

    /**
     * 现住地采集状态
     */
    @Column(value = "xzdcjzt")
    private String xzdcjzt;

    /**
     * 指纹一指位
     */
    @Column(value = "zwyzw")
    private String zwyzw;

    /**
     * 指纹一注册结果
     */
    @Column(value = "zwyzcjg")
    private String zwyzcjg;

    /**
     * 指纹二指位
     */
    @Column(value = "zwezw")
    private String zwezw;

    /**
     * 指纹二注册结果
     */
    @Column(value = "zwezcjg")
    private String zwezcjg;

    /**
     * 指纹采集结果代码
     */
    @Column(value = "zwcjjgdm")
    private String zwcjjgdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "szyczkdm")
    private String szyczkdm;

    /**
     * 姓
     */
    @Column(value = "x")
    private String x;

    /**
     * 名
     */
    @Column(value = "m")
    private String m;

    /**
     * 籍贯详址
     */
    @Column(value = "jgxz")
    private String jgxz;

    /**
     * 监护人一证件类别
     */
    @Column(value = "jhrycyzjdm")
    private String jhrycyzjdm;

    /**
     * 监护人一证件号码
     */
    @Column(value = "jhryzjhm")
    private String jhryzjhm;

    /**
     * 监护人一外文姓
     */
    @Column(value = "jhrywwx")
    private String jhrywwx;

    /**
     * 监护人一外文名
     */
    @Column(value = "jhrywwm")
    private String jhrywwm;

    /**
     * 监护人一联系电话
     */
    @Crypto
    @Column(value = "jhrylxdh")
    private String jhrylxdh;

    /**
     * 监护人二证件类别
     */
    @Column(value = "jhrecyzjdm")
    private String jhrecyzjdm;

    /**
     * 监护人二证件号码
     */
    @Column(value = "jhrezjhm")
    private String jhrezjhm;

    /**
     * 监护人二外文姓
     */
    @Column(value = "jhrewwx")
    private String jhrewwx;

    /**
     * 监护人二外文名
     */
    @Column(value = "jhrewwm")
    private String jhrewwm;

    /**
     * 监护人二联系电话
     */
    @Crypto
    @Column(value = "jhrelxdh")
    private String jhrelxdh;

    /**
     * 父亲证件类别
     */
    @Column(value = "fqcyzjdm")
    private String fqcyzjdm;

    /**
     * 父亲证件号码
     */
    @Column(value = "fqzjhm")
    private String fqzjhm;

    /**
     * 父亲外文姓
     */
    @Column(value = "fqwwx")
    private String fqwwx;

    /**
     * 父亲外文名
     */
    @Column(value = "fqwwm")
    private String fqwwm;

    /**
     * 母亲证件类别
     */
    @Column(value = "mqcyzjdm")
    private String mqcyzjdm;

    /**
     * 母亲证件号码
     */
    @Column(value = "mqzjhm")
    private String mqzjhm;

    /**
     * 母亲外文姓
     */
    @Column(value = "mqwwx")
    private String mqwwx;

    /**
     * 母亲外文名
     */
    @Column(value = "mqwwm")
    private String mqwwm;

    /**
     * 配偶证件类别
     */
    @Column(value = "pocyzjdm")
    private String pocyzjdm;

    /**
     * 配偶证件号码
     */
    @Column(value = "pozjhm")
    private String pozjhm;

    /**
     * 配偶外文姓
     */
    @Column(value = "powwx")
    private String powwx;

    /**
     * 配偶外文名
     */
    @Column(value = "powwm")
    private String powwm;

    /**
     * 从业状况单位代码
     */
    @Column(value = "cyzkdwbm")
    private String cyzkdwbm;

    /**
     * 从业状况单位名称
     */
    @Column(value = "cyzkdwmc")
    private String cyzkdwmc;

    /**
     * 何迁移流动原因迁来
     */
    @Column(value = "hqyldyy")
    private String hqyldyy;

    /**
     * 死亡原因
     */
    @Column(value = "swyy")
    private String swyy;

    /**
     * 迁出迁移（流动）原因
     */
    @Column(value = "qcqyldyy")
    private String qcqyldyy;

    /**
     * 注销时间
     */
    @Column(value = "zxsj")
    private String zxsj;

    /**
     * 更新时间
     */
    @Column(value = "gxsj")
    private String gxsj;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sjgsdwdm")
    private String sjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sjgsdwmc")
    private String sjgsdwmc;

    /**
     * 户籍地地址编码
     */
    @Column(value = "hjddzbm")
    private String hjddzbm;

    /**
     * 户籍地省市县（区）
     */
    @Column(value = "hjdssxq")
    private String hjdssxq;

    /**
     * 户籍地详细地址
     */
    @Column(value = "hjdxxdz")
    private String hjdxxdz;

    /**
     * 户籍地人户一致标识
     */
    @Column(value = "hjdrhyzbs")
    private String hjdrhyzbs;

    /**
     * 居住地地址编码
     */
    @Column(value = "jzddzbm")
    private String jzddzbm;

    /**
     * 居住地省市县（区）
     */
    @Column(value = "jzdssxq")
    private String jzdssxq;

    /**
     * 居住地详细地址
     */
    @Column(value = "jzdxxdz")
    private String jzdxxdz;

    /**
     * 证件地址
     */
    @Column(value = "zjdz")
    private String zjdz;

    /**
     * 何迁移流动原因迁来本址
     */
    @Column(value = "hqyldyylbz")
    private String hqyldyylbz;

    /**
     * 冻结时间
     */
    @Column(value = "djsj")
    private String djsj;

    /**
     * 冻结原因
     */
    @Column(value = "djyy")
    private String djyy;

    /**
     * 解除冻结时间
     */
    @Column(value = "jcdjsj")
    private String jcdjsj;

    /**
     * 户口冻结ID
     */
    @Column(value = "hkdjid")
    private Long hkdjid;

    /**
     * 冻结状态
     */
    @Column(value = "djzt")
    private String djzt;

    /**
     * 统计用行政区划
     */
    @Column(value = "tjyxzqh")
    private String tjyxzqh;

    /**
     * 城乡属性
     */
    @Column(value = "cxsx")
    private String cxsx;

//    /**
//     * 电子常表库ID
//     */
//    @Column(value = "dzcbkid")
//    private Long dzcbkid;

//    /**
//     * 电子常表采集时间
//     */
//    @Column(value = "dzcbcjsj")
//    private String dzcbcjsj;


    @Override
    public Long getId() {
        return this.rynbid;
    }

    @Override
    public void setId(Long id) {
        this.rynbid = id;
    }
}
