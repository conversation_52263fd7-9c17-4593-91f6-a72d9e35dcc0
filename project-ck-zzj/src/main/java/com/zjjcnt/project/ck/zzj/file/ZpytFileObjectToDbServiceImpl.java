package com.zjjcnt.project.ck.zzj.file;

import com.zjjcnt.project.ck.core.file.FileObjectService;
import com.zjjcnt.project.ck.core.file.domain.FileMeta;
import com.zjjcnt.project.ck.core.file.domain.FileObject;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjywWjSjDTO;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjywWjSjService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Created by hubin on 20210726
 */
@RequiredArgsConstructor
@Service
public class ZpytFileObjectToDbServiceImpl implements FileObjectService {

    private final HjywWjSjService hjywWjSjService;

    @Override
    public byte[] getData(FileMeta fileMeta) {
        if(StringUtils.isEmpty(fileMeta.getFileLocation())){
            return new byte[0];
        }
        HjywWjSjDTO wjSjDTO = hjywWjSjService.findById(fileMeta.getFileLocation());
        return wjSjDTO == null ? new byte[0] : wjSjDTO.getWjsj();
    }

    @Override
    public String save(FileObject fileInfoData, String bizName) {
        HjywWjSjDTO entity = new HjywWjSjDTO();
        entity.setWjsj(fileInfoData.getData());
        return hjywWjSjService.insert(entity).getWjsjbh();
    }

    @Override
    public void delete(FileMeta fileMeta) {
        hjywWjSjService.deleteById(fileMeta.getFileLocation());
    }

    @Override
    public String getStorageType() {
        return SysadminConstants.FILE_STORAGE_TYPE_DB;
    }
}
