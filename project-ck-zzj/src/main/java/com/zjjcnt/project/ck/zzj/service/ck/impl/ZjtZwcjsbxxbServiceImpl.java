package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtZwcjsbxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjsbxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtZwcjsbxxbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtZwcjsbxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjsbxxbService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 13:35:50
 */
@Service
@RequiredArgsConstructor
public class ZjtZwcjsbxxbServiceImpl extends AbstractBaseServiceImpl<ZjtZwcjsbxxbMapper, ZjtZwcjsbxxbDO, ZjtZwcjsbxxbDTO> implements ZjtZwcjsbxxbService {

    ZjtZwcjsbxxbConvert convert = ZjtZwcjsbxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtZwcjsbxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtZwcjsbxxbDO::getZwcjqbsh, dto.getZwcjqbsh(), StringUtils.isNotEmpty(dto.getZwcjqbsh()));
        query.eq(ZjtZwcjsbxxbDO::getZwqdxtzch, dto.getZwqdxtzch(), StringUtils.isNotEmpty(dto.getZwqdxtzch()));
        query.eq(ZjtZwcjsbxxbDO::getSbmc, dto.getSbmc(), Objects.nonNull(dto.getSbmc()));
        query.eq(ZjtZwcjsbxxbDO::getPpxh, dto.getPpxh(), Objects.nonNull(dto.getPpxh()));
        query.eq(ZjtZwcjsbxxbDO::getDjsj, dto.getDjsj(), StringUtils.isNotEmpty(dto.getDjsj()));
        query.eq(ZjtZwcjsbxxbDO::getSbdwgajgjgdm, dto.getSbdwgajgjgdm(), StringUtils.isNotEmpty(dto.getSbdwgajgjgdm()));
        query.eq(ZjtZwcjsbxxbDO::getSbdwgajgmc, dto.getSbdwgajgmc(), Objects.nonNull(dto.getSbdwgajgmc()));
        query.eq(ZjtZwcjsbxxbDO::getSyztdm, dto.getSyztdm(), StringUtils.isNotEmpty(dto.getSyztdm()));
        query.eq(ZjtZwcjsbxxbDO::getQyrq, dto.getQyrq(), StringUtils.isNotEmpty(dto.getQyrq()));
        query.eq(ZjtZwcjsbxxbDO::getTyrq, dto.getTyrq(), StringUtils.isNotEmpty(dto.getTyrq()));
        query.eq(ZjtZwcjsbxxbDO::getSjgsdwdm, dto.getSjgsdwdm(), StringUtils.isNotEmpty(dto.getSjgsdwdm()));
        query.eq(ZjtZwcjsbxxbDO::getSjgsdwmc, dto.getSjgsdwmc(), Objects.nonNull(dto.getSjgsdwmc()));
        query.eq(ZjtZwcjsbxxbDO::getBabz, dto.getBabz(), StringUtils.isNotEmpty(dto.getBabz()));
        query.eq(ZjtZwcjsbxxbDO::getBasj, dto.getBasj(), StringUtils.isNotEmpty(dto.getBasj()));
        query.eq(ZjtZwcjsbxxbDO::getFhdm, dto.getFhdm(), StringUtils.isNotEmpty(dto.getFhdm()));
        query.eq(ZjtZwcjsbxxbDO::getFhms, dto.getFhms(), Objects.nonNull(dto.getFhms()));
        query.eq(ZjtZwcjsbxxbDO::getSjbbh, dto.getSjbbh(), StringUtils.isNotEmpty(dto.getSjbbh()));
        query.eq(ZjtZwcjsbxxbDO::getRksj, dto.getRksj(), StringUtils.isNotEmpty(dto.getRksj()));
        query.eq(ZjtZwcjsbxxbDO::getJssjbbh, dto.getJssjbbh(), StringUtils.isNotEmpty(dto.getJssjbbh()));
        return query;
    }

    @Override
    public ZjtZwcjsbxxbDTO convertToDTO(ZjtZwcjsbxxbDO zjtZwcjsbxxbDO) {
        return convert.convert(zjtZwcjsbxxbDO);
    }

    @Override
    public ZjtZwcjsbxxbDO convertToDO(ZjtZwcjsbxxbDTO zjtZwcjsbxxbDTO) {
        return convert.convertToDO(zjtZwcjsbxxbDTO);
    }

    @Override
    public void validateZwcjqid(String zwcjqid) {
        Validate.notEmpty(zwcjqid);

        ZjtZwcjsbxxbDTO query = new ZjtZwcjsbxxbDTO();
        query.setZwcjqbsh(zwcjqid);
        query.setSyztdm("10");
        List<ZjtZwcjsbxxbDTO> zwcjsbxxbList = list(query);
        if (zwcjsbxxbList.size() > 1) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON,
                    "标识号[" + zwcjqid + "]的采集器[启用中]存在多条记录，联系管理员核查。");
        } else if (zwcjsbxxbList.isEmpty()) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON,
                    "标识号[" + zwcjqid + "]的采集器未注册。");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtZwcjsbxxbDTO insert(ZjtZwcjsbxxbDTO zwcjsbxxbDTO) throws ServiceException {
        ZjtZwcjsbxxbDTO findDto = new ZjtZwcjsbxxbDTO();
        findDto.setZwcjqbsh(zwcjsbxxbDTO.getZwcjqbsh());
        findDto.setSyztdm("10");
        List<ZjtZwcjsbxxbDTO> oldDtos = list(findDto);
        if (!oldDtos.isEmpty()){
            throw new ServiceException(CkZzjErrorCode.ERR_ZWCJSBXX_REPEAT,
                    "系统中已存在启用中的设备，设备标识号为["+zwcjsbxxbDTO.getZwcjqbsh()+"]。");
        }

        String now = ServerTimeUtils.getCurrentTime();
        zwcjsbxxbDTO.setSyztdm("10");
        zwcjsbxxbDTO.setDjsj(now);
        zwcjsbxxbDTO.setRksj(now);
        return super.insert(zwcjsbxxbDTO);
    }
}
