package com.zjjcnt.project.ck.zzj.task;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.entity.ck.RxZpcjxxbLxDO;
import com.zjjcnt.project.ck.zzj.task.handler.ZlpzLxZpTbHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 浙里拍照离线照片同步任务
 *
 * <AUTHOR>
 * @date 2024-06-24 09:39:00
 */
@Slf4j
@ConditionalOnProperty(prefix = "zjjcnt.task.zlpz-lxtb", name = "enabled", havingValue = "true")
@RequiredArgsConstructor
@Component
public class ZlpzLxZpTbTask {

    private final ZlpzLxZpTbHandler zlpzLxZpTbHandler;

    @Scheduled(initialDelay = 30000, fixedDelay = 30000)
    public void handle() {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(RxZpcjxxbLxDO::getTbbz, CkZzjConstants.TBBZ_DTB)
                .limit(10);

        while (true) {
            List<RxZpcjxxbLxDO> rxZpcjxxbLxList = zlpzLxZpTbHandler.listDtbRxZpcjxxb();
            log.info("ZlpzLxZpTbTask size={}", rxZpcjxxbLxList.size());

            if (CollectionUtils.isEmpty(rxZpcjxxbLxList)) {
                break;
            }
            rxZpcjxxbLxList.forEach(zlpzLxZpTbHandler::handOne);
        }
    }


}
