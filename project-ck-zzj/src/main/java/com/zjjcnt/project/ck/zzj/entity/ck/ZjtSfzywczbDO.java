package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 身份证业务操作表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtSfzywczbDTO
 */
@Crypto
@Data
@Table("zjt_sfzywczb")
public class ZjtSfzywczbDO implements IdEntity<String> {
    private static final long serialVersionUID = -7223081889156042551L;

    /**
     * 证件业务ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String zjywid;

    /**
     *
     */
    @Column(value = "ywslh")
    private String ywslh;

    /**
     * 受理号
     */
    @Column(value = "slh")
    private String slh;

    /**
     * 业务标志
     */
    @Column(value = "ywbz")
    private String ywbz;

    /**
     * 受理状态
     */
    @Column(value = "slzt")
    private String slzt;

    /**
     * 操作员ID
     */
    @Column(value = "czyid")
    private String czyid;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     *
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作IP
     */
    @Column(value = "czip")
    private String czip;

    /**
     *
     */
    @Column(value = "czydwdm")
    private String czydwdm;

    /**
     *
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     *
     */
    @Column(value = "bz")
    private String bz;


    @Override
    public String getId() {
        return this.zjywid;
    }

    @Override
    public void setId(String id) {
        this.zjywid = id;
    }
}
