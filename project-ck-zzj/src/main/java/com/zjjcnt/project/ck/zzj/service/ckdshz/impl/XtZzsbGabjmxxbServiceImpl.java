package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSONObject;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbxxbService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.commons.io.FileUtils;

import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.service.ckdshz.XtZzsbGabjmxxbService;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.XtZzsbGabjmxxbDO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbGabjmxxbDTO;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.XtZzsbGabjmxxbConvert;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.XtZzsbGabjmxxbMapper;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-05-21 10:04:10
 */
@RequiredArgsConstructor
@Service
public class XtZzsbGabjmxxbServiceImpl extends AbstractBaseServiceImpl<XtZzsbGabjmxxbMapper, XtZzsbGabjmxxbDO, XtZzsbGabjmxxbDTO> implements XtZzsbGabjmxxbService {

    XtZzsbGabjmxxbConvert convert = XtZzsbGabjmxxbConvert.INSTANCE;

    private final XtZzsbxxbService xtZzsbxxbService;

    @Override
    protected QueryWrapper genQueryWrapper(XtZzsbGabjmxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(XtZzsbGabjmxxbDO::getSbid, dto.getSbid(), Objects.nonNull(dto.getSbid()));
        query.eq(XtZzsbGabjmxxbDO::getQybz, dto.getQybz(), Objects.nonNull(dto.getQybz()));
        query.eq(XtZzsbGabjmxxbDO::getDeviceID, dto.getDeviceID(), Objects.nonNull(dto.getDeviceID()));
        query.eq(XtZzsbGabjmxxbDO::getEncKey, dto.getEncKey(), Objects.nonNull(dto.getEncKey()));
        query.eq(XtZzsbGabjmxxbDO::getKeyVersion, dto.getKeyVersion(), Objects.nonNull(dto.getKeyVersion()));
        query.eq(XtZzsbGabjmxxbDO::getMacKey, dto.getMacKey(), Objects.nonNull(dto.getMacKey()));
        query.eq(XtZzsbGabjmxxbDO::getSampleValue, dto.getSampleValue(), Objects.nonNull(dto.getSampleValue()));
        query.eq(XtZzsbGabjmxxbDO::getUser, dto.getUser(), Objects.nonNull(dto.getUser()));
        query.eq(XtZzsbGabjmxxbDO::getValidDate, dto.getValidDate(), Objects.nonNull(dto.getValidDate()));
        query.eq(XtZzsbGabjmxxbDO::getVerifyValue, dto.getVerifyValue(), Objects.nonNull(dto.getVerifyValue()));
        return query;
    }

    @Override
    public XtZzsbGabjmxxbDTO getConfig(Long sbid) {
        XtZzsbGabjmxxbDTO xtZzsbGabjmxxbDTO = findBySbid(sbid);

        if (Objects.nonNull(xtZzsbGabjmxxbDTO)) {
            return xtZzsbGabjmxxbDTO;
        }

        XtZzsbxxbDTO xtZzsbxxbDTO = xtZzsbxxbService.findById(sbid);
        if (Objects.isNull(xtZzsbxxbDTO)) {
            throw new ServiceException(CkZzjErrorCode.DEVICE_NOT_FOUND, "设备id为:" + sbid + "的设备不存在!");
        }

        String config = downloadConfig(xtZzsbxxbDTO.getSbmac());
        if (StringUtils.isBlank(config)){
            return null;
        }

        xtZzsbGabjmxxbDTO = JSONObject.parseObject(config, XtZzsbGabjmxxbDTO.class);
        xtZzsbGabjmxxbDTO.setSbid(sbid);
        xtZzsbGabjmxxbDTO.setQybz(Constants.YES);
        return insert(xtZzsbGabjmxxbDTO);
    }

    @Override
    public XtZzsbGabjmxxbDTO findBySbid(Long sbid) {
        if (Objects.isNull(sbid)) {
            return null;
        }
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(XtZzsbGabjmxxbDO::getQybz, Constants.YES);
        queryWrapper.eq(XtZzsbGabjmxxbDO::getSbid, sbid);
        return find(queryWrapper);
    }

    private String downloadConfig(String sbmac) {
        String url = "http://10.5.153.118:56309/bjxxfw/services/bjxxfw/ZAGL_DOWNLOADJAR";
        String requestXML = CkZzjConstants.DOWNLOAD_JAR_PARAM_XML.replaceAll("\\{\\{DEVICEID\\}\\}", sbmac);

        String jsonString = "";

        // 发起POST请求
        try (HttpResponse response = HttpRequest.get(url)
//                .header("Content-Type", "application/xml")
                .header("Accept", "application/java-archive") // 设置请求头，指定内容类型为XML
                .header("Content-Disposition", "attachment")
                .body(requestXML) // 设置请求体为XML参数
                .execute()) {
            if (response.isOk()) { // 检查 HTTP 状态码为 200
                byte[] jarBytes = response.bodyBytes(); // 获取二进制内容
                Path path = Paths.get(System.getProperty("user.dir"));
                String outputPath = path.toAbsolutePath() + "\\jmjar\\" + sbmac;
                FileUtil.writeBytes(jarBytes, outputPath + "\\" + "aqfa.jar"); // 写入本地文件

                unJar(outputPath, "aqfa.jar", "config.json");

                try {
                    jsonString = FileUtils.readFileToString(new File(outputPath + "\\config.json"), "UTF-8");
                } catch (IOException e) {
                    throw new ServiceException(CkZzjErrorCode.GAB_WCNSLZGSC_HQJMXX_ERROR, "配置文件读取失败，设备mac ：" + sbmac);
                }
            } else {
                if(response.body().contains("下载的文件不存在")){
                    System.out.println("下载的文件不存在");
                    return null;
                }
                throw new ServiceException(CkZzjErrorCode.GAB_WCNSLZGSC_HQJMXX_ERROR, response.body());
            }
        }

        return jsonString;
    }

    private void unJar(String path, String jarName, String fileName) {
        Path jarPath = Paths.get(path + File.separator + jarName);
        try (JarFile jarFile = new JarFile(jarPath.toFile())) {
            Enumeration<JarEntry> entries = jarFile.entries();

            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                if (entry.getName().equals(fileName)) {
                    // 创建目标文件输出路径
                    Path outputPath = Paths.get(path + File.separator + entry.getName());
//                    outputPath = Paths.get(System.getProperty("user.dir")).resolve(entry.getName());
                    Files.createDirectories(outputPath.getParent());

                    // 复制文件内容
                    try (InputStream is = jarFile.getInputStream(entry);
                         OutputStream os = Files.newOutputStream(outputPath)) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = is.read(buffer)) != -1) {
                            os.write(buffer, 0, bytesRead);
                        }
                    }
                    break;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public XtZzsbGabjmxxbDTO convertToDTO(XtZzsbGabjmxxbDO xtZzsbGabjmxxbDO) {
        return convert.convert(xtZzsbGabjmxxbDO);
    }

    @Override
    public XtZzsbGabjmxxbDO convertToDO(XtZzsbGabjmxxbDTO xtZzsbGabjmxxbDTO) {
        return convert.convertToDO(xtZzsbGabjmxxbDTO);
    }
}
