package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 逃犯比对结果信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.WwTfbdjgxxbDTO
 */
@Crypto
@Data
@Table("ww_tfbdjgxxb")
public class WwTfbdjgxxbDO implements IdEntity<Long> {
    private static final long serialVersionUID = 3549843674666788665L;

    /**
     * 比对ID
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 + SID_WW_TFBDJGXXB.nextval FROM dual")
    private Long bdid;

    /**
     * 匹配条件
     */
    @Column(value = "pptj")
    private String pptj;

    /**
     * 比对语句
     */
    @Column(value = "bdyj")
    private String bdyj;

    /**
     * 操作时间
     */
    @Column(value = "czsj")
    private String czsj;

    /**
     * 操作人ID
     */
    @Column(value = "czrid")
    private Long czrid;

    /**
     * 操作人IP
     */
    @Column(value = "czrip")
    private String czrip;

    /**
     * 操作人单位
     */
    @Column(value = "czrdw")
    private String czrdw;

    /**
     * 操作人姓名
     */
    @Crypto
    @Column(value = "czrxm")
    private String czrxm;

    /**
     * 操作人登录名
     */
    @Column(value = "czrdlm")
    private String czrdlm;

    /**
     *
     */
    @Column(value = "bdywbh")
    private String bdywbh;

    /**
     * 审核人ID
     */
    @Column(value = "shrid")
    private Long shrid;

    /**
     * 审核时间
     */
    @Column(value = "shsj")
    private String shsj;

    /**
     * 处理结果
     */
    @Column(value = "cljg")
    private String cljg;

    /**
     * 处理说明
     */
    @Column(value = "clsm")
    private String clsm;

    /**
     * 处理人ID
     */
    @Column(value = "clrid")
    private Long clrid;

    /**
     * 处理时间
     */
    @Column(value = "clsj")
    private String clsj;

    /**
     * 处理人IP
     */
    @Column(value = "clrip")
    private String clrip;

    /**
     * 省市县区
     */
    @Column(value = "ssxq")
    private String ssxq;

    /**
     * 街路巷
     */
    @Column(value = "jlx")
    private String jlx;

    /**
     * 门(楼)牌号
     */
    @Column(value = "mlph")
    private String mlph;

    /**
     * 门(楼)详址
     */
    @Column(value = "mlxz")
    private String mlxz;

    /**
     * 派出所
     */
    @Column(value = "pcs")
    private String pcs;

    /**
     * 责任区
     */
    @Column(value = "zrq")
    private String zrq;

    /**
     * 乡镇(街道)
     */
    @Column(value = "xzjd")
    private String xzjd;

    /**
     * 居(村)委会
     */
    @Column(value = "jcwh")
    private String jcwh;

    /**
     * 人员内部ID
     */
    @Column(value = "rynbid")
    private Long rynbid;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 记录标志
     */
    @Column(value = "jlbz")
    private String jlbz;


    @Override
    public Long getId() {
        return this.bdid;
    }

    @Override
    public void setId(Long id) {
        this.bdid = id;
    }
}
