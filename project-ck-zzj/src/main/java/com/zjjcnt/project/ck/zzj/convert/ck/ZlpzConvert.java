package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.zzj.domain.JjzpUploadRequest;
import com.zjjcnt.project.ck.zzj.third.cjrzj.CrjZjResp;
import com.zjjcnt.project.ck.zzj.third.crj.req.CrjSavePaperPhotoReq;
import com.zjjcnt.project.ck.zzj.third.rop.req.YztyCrjRopReq;
import com.zjjcnt.project.ck.zzj.third.rop.req.YztyJjRopReq;
import com.zjjcnt.project.ck.zzj.third.yzty.req.YztyMpzjReq;
import com.zjjcnt.project.ck.zzj.web.request.CrjZpUploadReq;
import com.zjjcnt.project.ck.zzj.web.request.JjZpUploadReq;
import com.zjjcnt.project.ck.zzj.web.request.ZlpzMpzjReq;
import com.zjjcnt.project.ck.zzj.web.response.ZlpzCrjZjResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * ZlpzConvert
 *
 * <AUTHOR>
 * @date 2024-09-20 14:04:00
 */
@Mapper
public interface ZlpzConvert {

    ZlpzConvert INSTANCE = Mappers.getMapper(ZlpzConvert.class);

    @Mapping(target = "success", source = "ok")
    ZlpzCrjZjResp convert(CrjZjResp cjrZjResp);

    YztyMpzjReq convert(ZlpzMpzjReq zlpzMpzjReq);

    @Mapping(target = "xpbh", source = "zjhm")
    @Mapping(target = "xpnr", source = "crjzpBase64")
    @Mapping(target = "xpall", source = "mpBase64")
    @Mapping(target = "sfrgqztg", source = "sfrgqztg", defaultValue = Constants.NO)
    CrjSavePaperPhotoReq convert(CrjZpUploadReq zlpzMpzjReq);

    @Mapping(target = "gmsfhm", source = "zjhm")
    @Mapping(target = "base64crj", source = "crjzpBase64")
    @Mapping(target = "base64mp", source = "mpBase64")
    @Mapping(target = "sfrgqztg", source = "sfrgqztg", defaultValue = Constants.NO)
    YztyCrjRopReq convertRopReq(CrjZpUploadReq zlpzMpzjReq);

    @Mapping(target = "base64jj", source = "zp")
    YztyJjRopReq convertRopReq(JjZpUploadReq jjZpUploadReq);


    JjzpUploadRequest convert(JjZpUploadReq zlpzMpzjReq);
}
