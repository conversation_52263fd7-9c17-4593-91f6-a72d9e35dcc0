package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 财政执收单位对应表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtCzzsdwdybDTO
 */
@Crypto
@Data
@Table("zjt_czzsdwdyb")
public class ZjtCzzsdwdybDO implements IdEntity<String> {
    private static final long serialVersionUID = 7559569878636929982L;

    /**
     * 对应编号
     */
    @Id(keyType = KeyType.None)
    private String dyid;

    /**
     * 受理地分局数据归属单位代码
     */
    @Column(value = "sldfjsjgsdwdm")
    private String sldfjsjgsdwdm;

    /**
     * 受理地分局数据归属单位名称
     */
    @Column(value = "sldfjsjgsdwmc")
    private String sldfjsjgsdwmc;

    /**
     * 执收单位编码
     */
    @Column(value = "zsdwdm")
    private String zsdwdm;

    /**
     * 执收单位名称
     */
    @Column(value = "zsdwmc")
    private String zsdwmc;

    /**
     * 行政区划编码
     */
    @Column(value = "xzqhdm")
    private String xzqhdm;

    /**
     * 收费业务类型
     */
    @Column(value = "sfywlx")
    private String sfywlx;

    /**
     * 执收项目编码
     */
    @Crypto
    @Column(value = "zsxmbm")
    private String zsxmbm;

    /**
     * 执收项目名称
     */
    @Crypto
    @Column(value = "zsxmmc")
    private String zsxmmc;

    /**
     * 收费单价
     */
    @Column(value = "sfdj")
    private BigDecimal sfdj;

    /**
     * 收费数量
     */
    @Column(value = "sfsl")
    private Integer sfsl;

    /**
     * 收费金额
     */
    @Column(value = "sfje")
    private BigDecimal sfje;

    /**
     * 备注
     */
    @Column(value = "bz")
    private String bz;


    @Override
    public String getId() {
        return this.dyid;
    }

    @Override
    public void setId(String id) {
        this.dyid = id;
    }
}
