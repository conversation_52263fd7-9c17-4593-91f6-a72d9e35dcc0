package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbGabjmxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbGabjmxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbGabjmxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.XtZzsbGabjmxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbGabjmxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbGabjmxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.XtZzsbGabjmxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.XtZzsbGabjmxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2025-05-21 10:04:10
*/
@Mapper
public interface XtZzsbGabjmxxbConvert {

    XtZzsbGabjmxxbConvert INSTANCE = Mappers.getMapper(XtZzsbGabjmxxbConvert.class);

    XtZzsbGabjmxxbDTO convert(XtZzsbGabjmxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    XtZzsbGabjmxxbDO convertToDO(XtZzsbGabjmxxbDTO dto);

    XtZzsbGabjmxxbDTO convertToDTO(XtZzsbGabjmxxbPageReq req);

    XtZzsbGabjmxxbDTO convertToDTO(XtZzsbGabjmxxbCreateReq req);

    XtZzsbGabjmxxbDTO convertToDTO(XtZzsbGabjmxxbUpdateReq req);

    XtZzsbGabjmxxbPageResp convertToPageResp(XtZzsbGabjmxxbDTO dto);

    XtZzsbGabjmxxbViewResp convertToViewResp(XtZzsbGabjmxxbDTO dto);

    XtZzsbGabjmxxbCreateResp convertToCreateResp(XtZzsbGabjmxxbDTO dto);

}
