package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.core.file.CkFileManager;
import com.zjjcnt.project.ck.core.file.domain.DefaultFileObject;
import com.zjjcnt.project.ck.core.file.domain.FileObject;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtZwcjbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtZwcjbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtZwcjbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjbService;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjsbxxbService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 指纹采集表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ZjtZwcjbServiceImpl extends ExBaseServiceImpl<ZjtZwcjbMapper, ZjtZwcjbDO, ZjtZwcjbDTO> implements ZjtZwcjbService {

    ZjtZwcjbConvert convert = ZjtZwcjbConvert.INSTANCE;

    private final CkFileManager fileManager;
    private final ZjtZwcjsbxxbService zjtZwcjsbxxbService;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtZwcjbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtZwcjbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtZwcjbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtZwcjbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtZwcjbDO::getZwcjjgdm, dto.getZwcjjgdm(), StringUtils.isNotEmpty(dto.getZwcjjgdm()));
        query.eq(ZjtZwcjbDO::getSzyczkdm, dto.getSzyczkdm(), StringUtils.isNotEmpty(dto.getSzyczkdm()));
        query.eq(ZjtZwcjbDO::getZwyzcjg, dto.getZwyzcjg(), StringUtils.isNotEmpty(dto.getZwyzcjg()));
        query.eq(ZjtZwcjbDO::getZwyzw, dto.getZwyzw(), StringUtils.isNotEmpty(dto.getZwyzw()));
        query.eq(ZjtZwcjbDO::getZwytxwjbh, dto.getZwytxwjbh(), StringUtils.isNotEmpty(dto.getZwytxwjbh()));
        query.eq(ZjtZwcjbDO::getZwytxzlz, dto.getZwytxzlz(), Objects.nonNull(dto.getZwytxzlz()));
        query.eq(ZjtZwcjbDO::getZwyzwtzsj, dto.getZwyzwtzsj(), Objects.nonNull(dto.getZwyzwtzsj()));
        query.eq(ZjtZwcjbDO::getZwezcjg, dto.getZwezcjg(), StringUtils.isNotEmpty(dto.getZwezcjg()));
        query.eq(ZjtZwcjbDO::getZwezw, dto.getZwezw(), StringUtils.isNotEmpty(dto.getZwezw()));
        query.eq(ZjtZwcjbDO::getZwetxwjbh, dto.getZwetxwjbh(), StringUtils.isNotEmpty(dto.getZwetxwjbh()));
        query.eq(ZjtZwcjbDO::getZwetxzlz, dto.getZwetxzlz(), Objects.nonNull(dto.getZwetxzlz()));
        query.eq(ZjtZwcjbDO::getZwezwtzsj, dto.getZwezwtzsj(), Objects.nonNull(dto.getZwezwtzsj()));
        query.eq(ZjtZwcjbDO::getZwqdxtzch, dto.getZwqdxtzch(), StringUtils.isNotEmpty(dto.getZwqdxtzch()));
        query.eq(ZjtZwcjbDO::getZwcjqid, dto.getZwcjqid(), StringUtils.isNotEmpty(dto.getZwcjqid()));
        query.eq(ZjtZwcjbDO::getZwsfbbh, dto.getZwsfbbh(), StringUtils.isNotEmpty(dto.getZwsfbbh()));
        query.eq(ZjtZwcjbDO::getZwsfkfzdm, dto.getZwsfkfzdm(), StringUtils.isNotEmpty(dto.getZwsfkfzdm()));
        query.eq(ZjtZwcjbDO::getCzrid, dto.getCzrid(), StringUtils.isNotEmpty(dto.getCzrid()));
        query.ge(ZjtZwcjbDO::getBcsj, dto.getBcsjStart(), StringUtils.isNotEmpty(dto.getBcsjStart()));
        query.le(ZjtZwcjbDO::getBcsj, dto.getBcsjEnd(), StringUtils.isNotEmpty(dto.getBcsjEnd()));
        query.eq(ZjtZwcjbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(ZjtZwcjbDO::getCzydwdm, dto.getCzydwdm(), StringUtils.isNotEmpty(dto.getCzydwdm()));
        query.eq(ZjtZwcjbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.ge(ZjtZwcjbDO::getSbsj, dto.getSbsjStart(), StringUtils.isNotEmpty(dto.getSbsjStart()));
        query.le(ZjtZwcjbDO::getSbsj, dto.getSbsjEnd(), StringUtils.isNotEmpty(dto.getSbsjEnd()));
        query.eq(ZjtZwcjbDO::getSlh, dto.getSlh(), StringUtils.isNotEmpty(dto.getSlh()));
        query.eq(ZjtZwcjbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtZwcjbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtZwcjbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtZwcjbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        return query;
    }

    @Override
    public ZjtZwcjbDTO convertToDTO(ZjtZwcjbDO zjtZwcjbDO) {
        return convert.convert(zjtZwcjbDO);
    }

    @Override
    public ZjtZwcjbDO convertToDO(ZjtZwcjbDTO zjtZwcjbDTO) {
        return convert.convertToDO(zjtZwcjbDTO);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtZwcjbDTO insert(ZjtZwcjbDTO dto) {
        if (Objects.isNull(dto.getValidateZwcjqid())) {
            dto.setValidateZwcjqid(true);
        }

        ZjtZwcjbDTO result = super.insert(dto);
        try {
            String zwytxwjbh = saveZwtx(result.getZwtxid(), dto.getGmsfhm() + "-1-"
                    + dto.getZwyzw(), dto.getZwytxsj());
            result.setZwytxwjbh(zwytxwjbh);
            String zwetxwjbh = saveZwtx(result.getZwtxid(), dto.getGmsfhm() + "-2-"
                    + dto.getZwezw(), dto.getZwetxsj());
            result.setZwetxwjbh(zwetxwjbh);
            update(result);
        } catch (Exception e) {
            log.error("保存指纹图像数据发生错误：", e);
            throw new ServiceException(CkZzjErrorCode.ZWTX_SAVE_ERROR);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtZwcjbDTO insertWithNotValidateZwcjqid(ZjtZwcjbDTO zjtZwcjbDTO) {
        zjtZwcjbDTO.setValidateZwcjqid(false);
        return insert(zjtZwcjbDTO);
    }

    @Override
    protected void beforeInsert(ZjtZwcjbDTO dto) {
        super.beforeInsert(dto);

        //判断是否需要校验指纹机器的ID是否注册
        if (!Boolean.FALSE.equals(dto.getValidateZwcjqid())) {
            //校验指纹采集器是否注册及有效性
            zjtZwcjsbxxbService.validateZwcjqid(dto.getZwcjqid());
        }

        dto.setSbsj(ServerTimeUtils.getCurrentTime());
        if (StringUtils.isBlank(dto.getBcsj())) {
            dto.setBcsj(dto.getSbsj());
        }
        CustomUserDetails user = SecurityUtils.getCurrentUser();
        dto.setCzrid(String.valueOf(user.getUserId()));
        dto.setCzyxm(user.getName());
        dto.setCzydwdm(user.getDeptCode());
        dto.setCzydwmc(user.getDeptName());
    }

    private String saveZwtx(String zwtxid, String name, byte[] data) {
        FileObject fileObject = new DefaultFileObject(data, MediaType.IMAGE_JPEG_VALUE, name);
        return fileManager.save(getBizTable(), zwtxid, fileObject);
    }


}
