package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-05-15 16:18:06
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.RxZpwjbDTO
 */
@Crypto
@Data
@Table("rx_zpwjb")
public class RxZpwjbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -9044144737315023957L;

    /**
     * 照片ID
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 + SID_RX_ZPWJB.nextval FROM dual")
    private Long zpid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 照片类型
     */
    @Column(value = "zplx")
    private String zplx;

    /**
     * 照片
     */
    @Column(value = "zp")
    private byte[] zp;

    /**
     * 保存时间
     */
    @Column(value = "bcsj")
    private String bcsj;

    /**
     * 同步标志
     */
    @Column(value = "tbbz")
    private String tbbz;

    /**
     * 同步时间
     */
    @Column(value = "tbsj")
    private String tbsj;

    /**
     * 文件后缀
     */
    @Column(value = "wjhz")
    private String wjhz;

    /**
     * 文件大小
     */
    @Column(value = "wjdx")
    private Long wjdx;


    @Override
    public Long getId() {
        return this.zpid;
    }

    @Override
    public void setId(Long id) {
        this.zpid = id;
    }
}
