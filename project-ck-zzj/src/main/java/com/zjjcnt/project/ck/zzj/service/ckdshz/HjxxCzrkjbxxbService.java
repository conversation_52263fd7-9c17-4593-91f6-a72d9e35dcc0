package com.zjjcnt.project.ck.zzj.service.ckdshz;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.sysadmin.domain.Xtsjfw;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO;

import java.util.List;

/**
 * 常住人口基本信息表Service
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
public interface HjxxCzrkjbxxbService extends IBaseService<HjxxCzrkjbxxbDTO> {

    HjxxCzrkjbxxbDTO findNormalByGmsfhmAndSjfw(String gmsfhm, List<Xtsjfw> xtsjfwList);

    HjxxCzrkjbxxbDTO findNormalByGmsfhm(String gmsfhm);

    HjxxCzrkjbxxbDTO findEnabledByGmsfhm(String gmsfhm);

    String getHjdz(String ssxq, String xzjd, String jlx, String mlph, String mlxz);

}
