package com.zjjcnt.project.ck.zzj.service.ckdshz;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxZplsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxZplsbSaveReq;

/**
 * 照片临时表Service
 *
 * <AUTHOR>
 * @date 2024-05-07 11:35:02
 */
public interface HjxxZplsbService extends IBaseService<HjxxZplsbDTO> {

    HjxxZplsbDTO processSaveZplsb(HjxxZplsbSaveReq req);

    HjxxZplsbDTO getLastValidPhoto(String gmsfhm);

}
