
package com.zjjcnt.project.ck.zzj.third.crj;

import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.WebService;
import jakarta.xml.ws.RequestWrapper;
import jakarta.xml.ws.ResponseWrapper;

import jakarta.xml.bind.annotation.XmlSeeAlso;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 *
 */
@WebService(name = "eecmiswsPortType", targetNamespace = "http://e3.org/eecmisws/soapws")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface EecmiswsPortType {


    /**
     *
     * @param args
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:loginNew")
    @WebResult(targetNamespace = "http://e3.org/eecmisws/soapws")
    @RequestWrapper(localName = "loginNew", targetNamespace = "http://e3.org/eecmisws/soapws", className = "com.zjjcnt.project.ck.zzj.third.crj.LoginNew")
    @ResponseWrapper(localName = "loginNewResponse", targetNamespace = "http://e3.org/eecmisws/soapws", className = "com.zjjcnt.project.ck.zzj.third.crj.LoginNewResponse")
    public String loginNew(
        @WebParam(name = "args", targetNamespace = "http://e3.org/eecmisws/soapws")
        String args);

    /**
     *
     * @param password
     * @param loginName
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:login")
    @WebResult(targetNamespace = "http://e3.org/eecmisws/soapws")
    @RequestWrapper(localName = "login", targetNamespace = "http://e3.org/eecmisws/soapws", className = "com.zjjcnt.project.ck.zzj.third.crj.Login")
    @ResponseWrapper(localName = "loginResponse", targetNamespace = "http://e3.org/eecmisws/soapws", className = "com.zjjcnt.project.ck.zzj.third.crj.LoginResponse")
    public String login(
        @WebParam(name = "loginName", targetNamespace = "http://e3.org/eecmisws/soapws")
        String loginName,
        @WebParam(name = "password", targetNamespace = "http://e3.org/eecmisws/soapws")
        String password);

    /**
     *
     * @param args
     * @param funccode
     * @param sessionId
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "urn:unimethod")
    @WebResult(targetNamespace = "http://e3.org/eecmisws/soapws")
    @RequestWrapper(localName = "unimethod", targetNamespace = "http://e3.org/eecmisws/soapws", className = "com.zjjcnt.project.ck.zzj.third.crj.Unimethod")
    @ResponseWrapper(localName = "unimethodResponse", targetNamespace = "http://e3.org/eecmisws/soapws", className = "com.zjjcnt.project.ck.zzj.third.crj.UnimethodResponse")
    public String unimethod(
        @WebParam(name = "sessionId", targetNamespace = "http://e3.org/eecmisws/soapws")
        String sessionId,
        @WebParam(name = "funccode", targetNamespace = "http://e3.org/eecmisws/soapws")
        String funccode,
        @WebParam(name = "args", targetNamespace = "http://e3.org/eecmisws/soapws")
        String args);

}
