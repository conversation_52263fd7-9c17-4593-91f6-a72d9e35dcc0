package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.YwtbtSendTaskConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.YwtbtSendTaskDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.YwtbtSendTaskDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.YwtbtSendTaskMapper;
import com.zjjcnt.project.ck.zzj.service.ck.YwtbtSendTaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-06-21 15:36:54
 */
@Service
public class YwtbtSendTaskServiceImpl extends AbstractBaseServiceImpl<YwtbtSendTaskMapper, YwtbtSendTaskDO, YwtbtSendTaskDTO> implements YwtbtSendTaskService {

    YwtbtSendTaskConvert convert = YwtbtSendTaskConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(YwtbtSendTaskDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(YwtbtSendTaskDO::getBiztype, dto.getBiztype(), StringUtils.isNotEmpty(dto.getBiztype()));
        query.eq(YwtbtSendTaskDO::getTablename, dto.getTablename(), StringUtils.isNotEmpty(dto.getTablename()));
        query.eq(YwtbtSendTaskDO::getPkname, dto.getPkname(), StringUtils.isNotEmpty(dto.getPkname()));
        query.eq(YwtbtSendTaskDO::getPkvalue, dto.getPkvalue(), StringUtils.isNotEmpty(dto.getPkvalue()));
        query.eq(YwtbtSendTaskDO::getData, dto.getData(), StringUtils.isNotEmpty(dto.getData()));
        query.eq(YwtbtSendTaskDO::getAction, dto.getAction(), StringUtils.isNotEmpty(dto.getAction()));
        query.ge(YwtbtSendTaskDO::getTasktime, dto.getTasktimeStart(), StringUtils.isNotEmpty(dto.getTasktimeStart()));
        query.le(YwtbtSendTaskDO::getTasktime, dto.getTasktimeEnd(), StringUtils.isNotEmpty(dto.getTasktimeEnd()));
        query.eq(YwtbtSendTaskDO::getStatus, dto.getStatus(), StringUtils.isNotEmpty(dto.getStatus()));
        query.eq(YwtbtSendTaskDO::getSendtype, dto.getSendtype(), StringUtils.isNotEmpty(dto.getSendtype()));
        query.eq(YwtbtSendTaskDO::getSendstatus, dto.getSendstatus(), StringUtils.isNotEmpty(dto.getSendstatus()));
        query.ge(YwtbtSendTaskDO::getSendtime, dto.getSendtimeStart(), StringUtils.isNotEmpty(dto.getSendtimeStart()));
        query.le(YwtbtSendTaskDO::getSendtime, dto.getSendtimeEnd(), StringUtils.isNotEmpty(dto.getSendtimeEnd()));
        query.eq(YwtbtSendTaskDO::getSendresult, dto.getSendresult(), StringUtils.isNotEmpty(dto.getSendresult()));
        query.eq(YwtbtSendTaskDO::getMessage, dto.getMessage(), StringUtils.isNotEmpty(dto.getMessage()));
        query.eq(YwtbtSendTaskDO::getYxbz, dto.getYxbz(), StringUtils.isNotEmpty(dto.getYxbz()));
        query.eq(YwtbtSendTaskDO::getCjrid, dto.getCjrid(), StringUtils.isNotEmpty(dto.getCjrid()));
        query.eq(YwtbtSendTaskDO::getXgrid, dto.getXgrid(), StringUtils.isNotEmpty(dto.getXgrid()));
        query.eq(YwtbtSendTaskDO::getSjgsdwdm, dto.getSjgsdwdm(), StringUtils.isNotEmpty(dto.getSjgsdwdm()));
        return query;
    }

    @Override
    public YwtbtSendTaskDTO convertToDTO(YwtbtSendTaskDO ywtbtSendTaskDO) {
        return convert.convert(ywtbtSendTaskDO);
    }

    @Override
    public YwtbtSendTaskDO convertToDO(YwtbtSendTaskDTO ywtbtSendTaskDTO) {
        return convert.convertToDO(ywtbtSendTaskDTO);
    }

    @Override
    public void createLxZpTask(Long id, String pcs) {
        YwtbtSendTaskDTO dto = new YwtbtSendTaskDTO();
        dto.setBiztype("common_curd");
        dto.setTablename("rx_zpcjxxb_lx");
        dto.setAction("create");
        dto.setPkname("id");
        dto.setPkvalue(String.valueOf(id));
        dto.setSendtype("0000");
        dto.setSjgsdwdm(pcs);
        insert(dto);
    }

    @Override
    public YwtbtSendTaskDTO insert(YwtbtSendTaskDTO dto) {
        String currentTime = ServerTimeUtils.getCurrentTime();
        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        dto.setCjrid(currentUser.getUsername());
        dto.setCjr(currentUser.getName());
        dto.setCjrip(currentUser.getRemoteAddress());
        dto.setCjsj(currentTime);
        dto.setYxbz(Constants.YES);
        dto.setStatus(Constants.NO);
        dto.setTasktime(currentTime);

        return super.insert(dto);
    }
}
