package com.zjjcnt.project.ck.zzj.web.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "受理信息入参")
@NoArgsConstructor
public class PdfRes {

    @Schema(description = "文件base64")
    private String base64String;

    @Schema(description = "文件byte")
    private byte[] bytes;

    public PdfRes(String base64String) {
        this.base64String = base64String;
    }

    public PdfRes(byte[] bytes) {
        this.bytes = bytes;
    }
}
