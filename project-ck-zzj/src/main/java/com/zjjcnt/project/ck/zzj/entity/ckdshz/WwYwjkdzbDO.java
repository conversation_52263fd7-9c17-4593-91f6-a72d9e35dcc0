package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 业务接口对照表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.WwYwjkdzbDTO
 */
@Data
@Table("ww_ywjkdzb")
public class WwYwjkdzbDO implements IdEntity<Long> {
    private static final long serialVersionUID = -2050609501093659605L;

    /**
     * 对照ID
     */
    @Id(keyType = KeyType.Auto)
    private Long dzid;

    /**
     * 比对业务编号
     */
    @Column(value = "bdywbh")
    private String bdywbh;

    /**
     * 比对业务名称
     */
    @Column(value = "bdywmc")
    private String bdywmc;

    /**
     * 参数ID
     */
    @Column(value = "csid")
    private Long csid;

    /**
     * 返回类型0:继续1:终止2.用户选择 3,需要审核
     */
    @Column(value = "fhlx")
    private String fhlx;

    /**
     * 启用标志
     */
    @Column(value = "qybz")
    private String qybz;

    /**
     * 创建时间
     */
    @Column(value = "cjsj")
    private String cjsj;


    @Override
    public Long getId() {
        return this.dzid;
    }

    @Override
    public void setId(Long id) {
        this.dzid = id;
    }
}
