package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.core.util.DwUtils;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.sysadmin.domain.Xtsjfw;
import com.zjjcnt.project.ck.sysadmin.service.XtJlxxxbService;
import com.zjjcnt.project.ck.sysadmin.service.XtXzjdxxbService;
import com.zjjcnt.project.ck.sysadmin.service.XtXzqhbService;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.HjxxCzrkjbxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxCzrkjbxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.HjxxCzrkjbxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxCzrkjbxxbService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 常住人口基本信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@RequiredArgsConstructor
@Service
public class HjxxCzrkjbxxbServiceImpl extends AbstractBaseServiceImpl<HjxxCzrkjbxxbMapper, HjxxCzrkjbxxbDO, HjxxCzrkjbxxbDTO> implements HjxxCzrkjbxxbService {

    private final XtXzqhbService xtXzqhbService;
    private final XtXzjdxxbService xtXzjdxxbService;
    private final XtJlxxxbService xtJlxxxbService;

    HjxxCzrkjbxxbConvert convert = HjxxCzrkjbxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(HjxxCzrkjbxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(HjxxCzrkjbxxbDO::getRyid, dto.getRyid(), Objects.nonNull(dto.getRyid()));
        query.eq(HjxxCzrkjbxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(HjxxCzrkjbxxbDO::getRyzt, dto.getRyzt(), StringUtils.isNotEmpty(dto.getRyzt()));
        query.eq(HjxxCzrkjbxxbDO::getCxbz, dto.getCxbz(), StringUtils.isNotEmpty(dto.getCxbz()));
        query.eq(HjxxCzrkjbxxbDO::getJlbz, dto.getJlbz(), StringUtils.isNotEmpty(dto.getJlbz()));
        query.eq(HjxxCzrkjbxxbDO::getPcs, dto.getPcs(), StringUtils.isNotEmpty(dto.getPcs()));
        query.eq(HjxxCzrkjbxxbDO::getJcwh, dto.getJcwh(), StringUtils.isNotEmpty(dto.getJcwh()));

        if (!CollectionUtils.isEmpty(dto.getSjfwList())) {
            query.and(queryWrapper -> {
                for (Xtsjfw xtsjfw : dto.getSjfwList()) {
                    if (SysadminConstants.SJFWBZ_JWH.equals(xtsjfw.getSjfwbz())) {
                        queryWrapper.or(HjxxCzrkjbxxbDO::getJcwh).eq(xtsjfw.getSjfw());
                    } else if (SysadminConstants.SJFWBZ_PCS.equals(xtsjfw.getSjfwbz())) {
                        queryWrapper.or(HjxxCzrkjbxxbDO::getPcs).eq(xtsjfw.getSjfw());
                    } else if (SysadminConstants.SJFWBZ_XZQH.equals(xtsjfw.getSjfwbz())) {
                        queryWrapper.or(HjxxCzrkjbxxbDO::getSsxq).likeLeft(DwUtils.removeXzqhZeroSuffix(xtsjfw.getSjfw()));
                    }
                }
            });
        }
        return query;
    }

    @Override
    public HjxxCzrkjbxxbDTO convertToDTO(HjxxCzrkjbxxbDO hjxxCzrkjbxxbDO) {
        return convert.convert(hjxxCzrkjbxxbDO);
    }

    @Override
    public HjxxCzrkjbxxbDO convertToDO(HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO) {
        return convert.convertToDO(hjxxCzrkjbxxbDTO);
    }

    @Override
    public HjxxCzrkjbxxbDTO findNormalByGmsfhmAndSjfw(String gmsfhm, List<Xtsjfw> xtsjfwList) {
        HjxxCzrkjbxxbDTO dto = new HjxxCzrkjbxxbDTO();
        dto.setGmsfhm(gmsfhm);
        dto.setJlbz(CkZzjConstants.JLBZ_YX);
        dto.setCxbz(CkZzjConstants.CXBZ_0);
        dto.setRyzt(CkZzjConstants.RYZT_ZC);
        dto.setSjfwList(xtsjfwList);
        QueryWrapper queryWrapper = genQueryWrapper(dto);
        queryWrapper.orderBy(HjxxCzrkjbxxbDO::getQysj, false);
        List<HjxxCzrkjbxxbDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : convertToDTO(list.get(0));
    }

    @Override
    public HjxxCzrkjbxxbDTO findNormalByGmsfhm(String gmsfhm) {
        HjxxCzrkjbxxbDTO dto = new HjxxCzrkjbxxbDTO();
        dto.setGmsfhm(gmsfhm);
        dto.setJlbz(CkZzjConstants.JLBZ_YX);
        dto.setCxbz(CkZzjConstants.CXBZ_0);
        dto.setRyzt(CkZzjConstants.RYZT_ZC);
        QueryWrapper queryWrapper = genQueryWrapper(dto);
        queryWrapper.orderBy(HjxxCzrkjbxxbDO::getQysj, false);
        List<HjxxCzrkjbxxbDO> list = list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : convertToDTO(list.get(0));
    }

    @Override
    public HjxxCzrkjbxxbDTO findEnabledByGmsfhm(String gmsfhm) {
        HjxxCzrkjbxxbDTO dto = new HjxxCzrkjbxxbDTO();
        dto.setGmsfhm(gmsfhm);
        dto.setJlbz(CkZzjConstants.JLBZ_YX);
        dto.setCxbz(CkZzjConstants.CXBZ_0);
        QueryWrapper queryWrapper = genQueryWrapper(dto);
        queryWrapper.orderBy(HjxxCzrkjbxxbDO::getQysj, false);
        queryWrapper.limit(1);
        return find(queryWrapper);
    }

    @Override
    public String getHjdz(String ssxq, String xzjd, String jlx, String mlph, String mlxz) {
        StringBuilder hjdz = new StringBuilder();
        hjdz.append(xtXzqhbService.getXzqhMc(ssxq))
                .append(xtXzjdxxbService.getXzjdBz(xzjd))
                .append(xtJlxxxbService.getJlxMc(jlx));

        if (StringUtils.isNotBlank(mlph)) {
            hjdz.append(mlph);
        }

        if (StringUtils.isNotBlank(mlxz)) {
            hjdz.append(mlxz);
        }

        return hjdz.toString();
    }
}
