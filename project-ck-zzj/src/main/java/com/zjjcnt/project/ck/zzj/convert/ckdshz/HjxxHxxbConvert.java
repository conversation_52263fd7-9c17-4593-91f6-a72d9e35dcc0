package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxHxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxHxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxHxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxHxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxHxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxHxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxHxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxHxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 户信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface HjxxHxxbConvert {

    HjxxHxxbConvert INSTANCE = Mappers.getMapper(HjxxHxxbConvert.class);

    HjxxHxxbDTO convert(HjxxHxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    HjxxHxxbDO convertToDO(HjxxHxxbDTO dto);

    HjxxHxxbDTO convertToDTO(HjxxHxxbPageReq req);

    HjxxHxxbDTO convertToDTO(HjxxHxxbCreateReq req);

    HjxxHxxbDTO convertToDTO(HjxxHxxbUpdateReq req);

    HjxxHxxbPageResp convertToPageResp(HjxxHxxbDTO dto);

    HjxxHxxbViewResp convertToViewResp(HjxxHxxbDTO dto);

    HjxxHxxbCreateResp convertToCreateResp(HjxxHxxbDTO dto);

}
