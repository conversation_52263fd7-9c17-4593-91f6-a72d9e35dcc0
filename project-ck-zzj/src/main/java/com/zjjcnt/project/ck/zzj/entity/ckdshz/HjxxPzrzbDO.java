package com.zjjcnt.project.ck.zzj.entity.ckdshz;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 拍照日志信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 * @see com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxPzrzbDTO
 */
@Crypto
@Data
@Table("hjxx_pzrzb")
public class HjxxPzrzbDO implements IdEntity<Long> {
    private static final long serialVersionUID = 6533067652221544089L;

    /**
     * 拍照日志ID
     */
    @Id(keyType = KeyType.Sequence, value = "select 3300000000000000000 + SID_HJXX_PZRZB.nextval FROM dual")
    private Long pzrzid;

    /**
     * 照片流水ID
     */
    @Column(value = "zplsid")
    private Long zplsid;

    /**
     * 内部受理ID
     */
    @Column(value = "nbslid")
    private Long nbslid;

    /**
     *
     */
    @Column(value = "yhid")
    private Long yhid;

    /**
     * IP地址
     */
    @Column(value = "ipdz")
    private String ipdz;

    /**
     * 用户登录名
     */
    @Column(value = "yhdlm")
    private String yhdlm;

    /**
     * 用户单位
     */
    @Column(value = "yhdw")
    private String yhdw;

    /**
     * 用户姓名
     */
    @Crypto
    @Column(value = "yhxm")
    private String yhxm;

    /**
     * 保存时间
     */
    @Column(value = "bcsj")
    private String bcsj;

    /**
     * 入库时间
     */
    @Column(value = "rksj")
    private String rksj;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 受理号
     */
    @Column(value = "slh")
    private String slh;

    /**
     * 拍照序列号
     */
    @Column(value = "pzxlh")
    private String pzxlh;

    /**
     * 照片采集类型
     */
    @Column(value = "zpcjlx")
    private String zpcjlx;

    /**
     * 照片设备标识号
     */
    @Column(value = "zpsbbsh")
    private String zpsbbsh;

    /**
     * 照片设备品牌型号编码
     */
    @Column(value = "zpsbppxhdm")
    private String zpsbppxhdm;

    /**
     * 照片设备品牌型号
     */
    @Column(value = "zpsbppxh")
    private String zpsbppxh;

    /**
     * 照片原图地址
     */
    @Column(value = "zpytfileurl")
    private String zpytfileurl;

    /**
     * 备注1
     */
    @Column(value = "bz1")
    private String bz1;

    /**
     * 备注2
     */
    @Column(value = "bz2")
    private String bz2;

    /**
     * 照片原图表ID
     */
    @Column(value = "zpytbid")
    private String zpytbid;

    /**
     *
     */
    @Column(value = "sbid")
    private String sbid;

    /**
     *
     */
    @Column(value = "zlpzsbdwbh")
    private String zlpzsbdwbh;


    @Override
    public Long getId() {
        return this.pzrzid;
    }

    @Override
    public void setId(Long id) {
        this.pzrzid = id;
    }
}
