package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.XtZzsbxxbLxConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.XtZzsbxxbLxDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.XtZzsbxxbLxCheckReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.XtZzsbxxbLxCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.XtZzsbxxbLxUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.XtZzsbxxbLxCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.XtZzsbxxbLxViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.XtZzsbxxbLxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
* 自助设备信息表_离线前端控制器
*
* <AUTHOR>
* @date 2024-06-18 13:39:25
*/
@Tag(name = "自助设备信息表_离线")
@RestController
@RequestMapping("/xtZzsbxxbLx")
public class XtZzsbxxbLxController extends AbstractCrudController<XtZzsbxxbLxDTO> {

    @Autowired
    private XtZzsbxxbLxService xtZzsbxxbLxService;

    @Override
    protected IBaseService getService() {
        return xtZzsbxxbLxService;
    }

//    @GetMapping("page")
//    @Operation(summary = "查询自助设备信息表_离线")
//    public CommonResult<PageResult<XtZzsbxxbLxPageResp>> page(XtZzsbxxbLxPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, XtZzsbxxbLxConvert.INSTANCE::convertToDTO, XtZzsbxxbLxConvert.INSTANCE::convertToPageResp);
//    }

    @GetMapping("view")
    @Operation(summary = "查看自助设备信息表_离线详情")
    public CommonResult<XtZzsbxxbLxViewResp> view(@RequestParam(value = "id") Long id) {
        return super.view(id, XtZzsbxxbLxConvert.INSTANCE::convertToViewResp);
    }

    @PostMapping("create")
    @Operation(summary = "新增自助设备信息表_离线")
    public CommonResult<XtZzsbxxbLxCreateResp> create(@Validated @RequestBody XtZzsbxxbLxCreateReq req) {
        return super.create(req, XtZzsbxxbLxConvert.INSTANCE::convertToDTO, XtZzsbxxbLxConvert.INSTANCE::convertToCreateResp);
    }

    @PostMapping("check")
    @Operation(summary = "校验自助设备信息表_离线")
    public CommonResult<Boolean> check(@Validated @RequestBody XtZzsbxxbLxCheckReq req) {
        xtZzsbxxbLxService.checkZzsb(req.getSbmac(), req.getSbyjm());
        return CommonResult.success(true);
    }

    @PostMapping("update")
    @Operation(summary = "编辑自助设备信息表_离线")
    public CommonResult<Boolean> update(@Validated @RequestBody XtZzsbxxbLxUpdateReq req) {
        return super.update(req, XtZzsbxxbLxConvert.INSTANCE::convertToDTO);
    }

}
