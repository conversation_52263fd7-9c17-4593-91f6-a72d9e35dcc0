package com.zjjcnt.project.ck.zzj.service.ck;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;

/**
 * 居民身份证受理信息表Service
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
public interface ZjtSlxxbService extends IBaseService<ZjtSlxxbDTO> {

    ZjtSlxxbDTO findLssfzSlxxb(String gmsfhm, String czsj);

    /**
     * 查询最新的正常证件的受理信息
     * @param gmsfhm
     * @return
     */
    ZjtSlxxbDTO findNewestNormal(String gmsfhm);

    boolean processZjslYsfcl(String nbslid);

    void updateLssfzslbzById(String nbslid, String lssfzslbz);

    long countByGmsfhmIsblz(String gmsfhm);

}
