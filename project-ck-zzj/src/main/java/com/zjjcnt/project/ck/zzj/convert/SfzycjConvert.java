package com.zjjcnt.project.ck.zzj.convert;

import com.zjjcnt.project.ck.zzj.third.sfzycj.resp.SfzycjFhxxResp;
import com.zjjcnt.project.ck.zzj.web.response.SfzycjxxResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 身份证预采集convert
 *
 * <AUTHOR>
 * @date 2025-05-09 16:43:00
 */

@Mapper
public interface SfzycjConvert {

    SfzycjConvert INSTANCE = Mappers.getMapper(SfzycjConvert.class);

    SfzycjxxResp convertToSfzycjxxResp(SfzycjFhxxResp fhxxResp);

}
