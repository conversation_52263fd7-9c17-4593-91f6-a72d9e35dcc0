package com.zjjcnt.project.ck.zzj.third.rop.req;

import lombok.Data;

/**
 * 办证资格审查req
 *
 * <AUTHOR>
 * @date 2024-10-14 11:14:00
 */
@Data
public class KssfzBzzgscRopReq implements IZdbzsbKssfzRopReq {

    /**
     * 公民身份号码
     */
    private String gmsfhm;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 用户登录名
     */
    private String yhdlm;

    /**
     * 协同类型代码
     * 40：居民身份证首申业务跨省通办
     * 41: 居民身份证补换领业务跨省通办
     */
    private String xthjdm;

    /**
     * 设备IP
     */
    private String sbip;

    /**
     * 设备秘钥
     */
    private String license;
}
