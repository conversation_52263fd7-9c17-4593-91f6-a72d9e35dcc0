package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtZwcjsbxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjsbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjsbxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbxxbViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjsbxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2024-05-09 13:35:50
 */

@Tag(name = "指纹采集设备信息")
@RestController
@RequestMapping("/zjtZwcjsbxxb")
public class ZjtZwcjsbxxbController extends AbstractCrudController<ZjtZwcjsbxxbDTO> {

    @Autowired
    private ZjtZwcjsbxxbService zjtZwcjsbxxbService;

    @Override
    protected IBaseService getService() {
        return zjtZwcjsbxxbService;
    }

//    @GetMapping("page")
//    @Operation(summary = "查询")
//    public CommonResult<PageResult<ZjtZwcjsbxxbPageResp>> page(ZjtZwcjsbxxbPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, ZjtZwcjsbxxbConvert.INSTANCE::convertToDTO, ZjtZwcjsbxxbConvert.INSTANCE::convertToPageResp);
//    }
//
//    @GetMapping("view")
//    @Operation(summary = "查看详情")
//    public CommonResult<ZjtZwcjsbxxbViewResp> view(String id) {
//        return super.view(id, ZjtZwcjsbxxbConvert.INSTANCE::convertToViewResp);
//    }

    @GetMapping("viewByZwcjqbsh")
    @Operation(summary = "查看详情")
    public CommonResult<ZjtZwcjsbxxbViewResp> viewByZwcjqbsh(@RequestParam(value = "zwcjqbsh") String zwcjqbsh) {

        ZjtZwcjsbxxbDTO query = new ZjtZwcjsbxxbDTO();
        query.setZwcjqbsh(zwcjqbsh);
        ZjtZwcjsbxxbDTO zjtZwcjsbxxbDTO = zjtZwcjsbxxbService.find(query);
        return CommonResult.success(ZjtZwcjsbxxbConvert.INSTANCE.convertToViewResp(zjtZwcjsbxxbDTO));
    }

    @PostMapping("create")
    @Operation(summary = "新增")
    public CommonResult<ZjtZwcjsbxxbCreateResp> create(@Validated @RequestBody ZjtZwcjsbxxbCreateReq req) {
        return super.create(req, ZjtZwcjsbxxbConvert.INSTANCE::convertToDTO, ZjtZwcjsbxxbConvert.INSTANCE::convertToCreateResp);
    }

//    @PostMapping("update")
//    @Operation(summary = "编辑")
//    public CommonResult<Boolean> update(@RequestBody ZjtZwcjsbxxbUpdateReq req) {
//        return super.update(req, ZjtZwcjsbxxbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }
//
//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(ZjtZwcjsbxxbExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出")
//    public void export(ZjtZwcjsbxxbPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, ZjtZwcjsbxxbExp.class,
//                    ZjtZwcjsbxxbConvert.INSTANCE::convertToDTO, ZjtZwcjsbxxbConvert.INSTANCE::convertToExp, response);
//    }

}
