package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjBbgxpzConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjBbgxpzDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjBbgxpzDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZzjBbgxpzMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjBbgxpzService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 自助机版本更新配置ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-07-30 15:27:57
 */
@Service
public class ZzjBbgxpzServiceImpl extends ExBaseServiceImpl<ZzjBbgxpzMapper, ZzjBbgxpzDO, ZzjBbgxpzDTO> implements ZzjBbgxpzService {

    ZzjBbgxpzConvert convert = ZzjBbgxpzConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZzjBbgxpzDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZzjBbgxpzDO::getBbh, dto.getBbh(), StringUtils.isNotEmpty(dto.getBbh()));
        query.eq(ZzjBbgxpzDO::getGjpc, dto.getGjpc(), StringUtils.isNotEmpty(dto.getGjpc()));
        query.eq(ZzjBbgxpzDO::getCxlx, dto.getCxlx(), StringUtils.isNotEmpty(dto.getCxlx()));
        query.eq(ZzjBbgxpzDO::getSfqzgx, dto.getSfqzgx(), StringUtils.isNotEmpty(dto.getSfqzgx()));
        query.eq(ZzjBbgxpzDO::getGxnr, dto.getGxnr(), StringUtils.isNotEmpty(dto.getGxnr()));
        query.eq(ZzjBbgxpzDO::getYxbz, dto.getYxbz(), StringUtils.isNotEmpty(dto.getYxbz()));
        return query;
    }

    @Override
    public ZzjBbgxpzDTO convertToDTO(ZzjBbgxpzDO zzjBbgxpzDO) {
        return convert.convert(zzjBbgxpzDO);
    }

    @Override
    public ZzjBbgxpzDO convertToDO(ZzjBbgxpzDTO zzjBbgxpzDTO) {
        return convert.convertToDO(zzjBbgxpzDTO);
    }

    @Override
    protected void beforeInsert(ZzjBbgxpzDTO dto) {
        dto.setCxlx(StringUtils.defaultIfEmpty(dto.getCxlx(), CkZzjConstants.CXLX_ZZJ));
        checkVersionUnique(dto);

        dto.setYxbz(Constants.YES);
        String currentTime = ServerTimeUtils.getCurrentTime();
        dto.setCjsj(currentTime);
        dto.setXgsj(currentTime);
        dto.setSfqzgx(StringUtils.defaultIfEmpty(dto.getSfqzgx(), Constants.NO));

        dto.setBbhgsh(formatBbh(dto.getBbh()));
        super.beforeInsert(dto);
    }

    @Override
    protected void beforeUpdate(ZzjBbgxpzDTO dto) {
        dto.setXgsj(ServerTimeUtils.getCurrentTime());
        if (StringUtils.isNotBlank(dto.getBbh())) {
            dto.setBbhgsh(formatBbh(dto.getBbh()));
        }
        super.beforeUpdate(dto);
    }

    private void checkVersionUnique(ZzjBbgxpzDTO dto) {
        ZzjBbgxpzDTO exist = new ZzjBbgxpzDTO();
        exist.setCxlx(dto.getCxlx());
        exist.setBbh(dto.getBbh());
        exist = find(exist);
        if (!doCheckUnique(dto, exist)) {
            throw new ServiceException(CkZzjErrorCode.VERSION_ALREADY_EXIST);
        }
    }

    @Override
    public ZzjBbgxpzDTO findLatestVersion(String cxlx) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZzjBbgxpzDO::getCxlx, cxlx);
        query.eq(ZzjBbgxpzDO::getYxbz, Constants.YES);
        query.orderBy(ZzjBbgxpzDO::getBbhgsh, false);
        query.limit(1);
        return find(query);
    }

    @Override
    public ZzjBbgxpzDTO findLowestVersion(String cxlx) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZzjBbgxpzDO::getCxlx, cxlx);
        query.eq(ZzjBbgxpzDO::getYxbz, Constants.YES);
        query.eq(ZzjBbgxpzDO::getSfqzgx, Constants.YES);
        query.orderBy(ZzjBbgxpzDO::getBbhgsh, false);
        query.limit(1);
        return find(query);
    }

    private String formatBbh(String bbh) {
        String[] versions = bbh.split("\\.");
        StringBuilder sb = new StringBuilder();
        for (String v : versions) {
            sb.append(StringUtils.leftPad(v, 4, Constants.ZERO));
        }
        return sb.toString();
    }
}
