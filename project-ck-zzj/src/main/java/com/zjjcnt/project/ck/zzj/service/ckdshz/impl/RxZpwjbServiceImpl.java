package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.RxZpwjbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.RxZpwjbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.RxZpwjbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.RxZpwjbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.RxZpwjbService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-15 16:18:06
 */
@Service
public class RxZpwjbServiceImpl extends AbstractBaseServiceImpl<RxZpwjbMapper, RxZpwjbDO, RxZpwjbDTO> implements RxZpwjbService {

    RxZpwjbConvert convert = RxZpwjbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(RxZpwjbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(RxZpwjbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(RxZpwjbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(RxZpwjbDO::getZplx, dto.getZplx(), Objects.nonNull(dto.getZplx()));
        query.eq(RxZpwjbDO::getZp, dto.getZp(), Objects.nonNull(dto.getZp()));
        query.ge(RxZpwjbDO::getBcsj, dto.getBcsjStart(), StringUtils.isNotEmpty(dto.getBcsjStart()));
        query.le(RxZpwjbDO::getBcsj, dto.getBcsjEnd(), StringUtils.isNotEmpty(dto.getBcsjEnd()));
        query.eq(RxZpwjbDO::getTbbz, dto.getTbbz(), StringUtils.isNotEmpty(dto.getTbbz()));
        query.ge(RxZpwjbDO::getTbsj, dto.getTbsjStart(), StringUtils.isNotEmpty(dto.getTbsjStart()));
        query.le(RxZpwjbDO::getTbsj, dto.getTbsjEnd(), StringUtils.isNotEmpty(dto.getTbsjEnd()));
        query.eq(RxZpwjbDO::getWjhz, dto.getWjhz(), Objects.nonNull(dto.getWjhz()));
        query.eq(RxZpwjbDO::getWjdx, dto.getWjdx(), Objects.nonNull(dto.getWjdx()));
        return query;
    }

    @Override
    public RxZpwjbDTO convertToDTO(RxZpwjbDO rxZpwjbDO) {
        return convert.convert(rxZpwjbDO);
    }

    @Override
    public RxZpwjbDO convertToDO(RxZpwjbDTO rxZpwjbDTO) {
        return convert.convertToDO(rxZpwjbDTO);
    }

    @Override
    public Long insertWj(String zplx, String gmsfhm, String xm, String base64zp, String wjhz) {
        if (StringUtils.isBlank(base64zp)) {
            return null;
        }
        return insertWj(zplx, gmsfhm, xm, Base64.getDecoder().decode(base64zp), wjhz);
    }

    @Override
    public Long insertWj(String zplx, String gmsfhm, String xm, byte[] zp, String wjhz) {
        Long zpid = 0L;

        if (ArrayUtils.isNotEmpty(zp)) {
            RxZpwjbDTO zpwjb = new RxZpwjbDTO();
            zpwjb.setGmsfhm(gmsfhm);
            zpwjb.setXm(xm);
            zpwjb.setZplx(zplx);
            zpwjb.setZp(zp);
            zpwjb.setWjhz(wjhz);
            zpwjb.setWjdx((long) zp.length);
            zpwjb.setBcsj(ServerTimeUtils.getCurrentTime());
            zpwjb.setTbbz(Constants.NO);
            zpwjb = insert(zpwjb);
            zpid = zpwjb.getZpid();
        }

        return zpid;
    }
}
