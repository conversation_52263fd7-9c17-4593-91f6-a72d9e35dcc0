package com.zjjcnt.project.ck.zzj.web.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "繳款信息生成二维码出参")
@AllArgsConstructor
@NoArgsConstructor
public class ZjtFyzflsbJkdhQrRes {
    @Schema(description = "缴款单号")
    private String jkdh;
    @Schema(description = "二维码")
    private String base64qr;
}
