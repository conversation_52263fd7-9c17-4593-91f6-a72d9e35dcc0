package com.zjjcnt.project.ck.zzj.web.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "获取带指纹信息的管理员出参")
public class GlyWithZwxxRes {

    @Schema(description = "用户id")
    private String yhid;
    @Schema(description = "姓名")
    private String yhxm;
    @Schema(description = "公民身份号码")
    private String gmsfhm;
    @Schema(description = "指纹一指位")
    private String zwyzw;
    @Schema(description = "指纹一特征数据")
    private String zwytzsj;
    @Schema(description = "指纹二指位")
    private String zwezw;
    @Schema(description = "指纹二特征数据")
    private String zwetzsj;
}
