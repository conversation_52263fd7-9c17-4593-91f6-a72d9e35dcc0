package com.zjjcnt.project.ck.zzj.third.rop.req;

import lombok.Data;

/**
 * 一照通用出入境照片上传
 *
 * <AUTHOR>
 * @date 2024-12-04 14:56:00
 */
@Data
public class YztyCrjRopReq {

    /**
     * 公民身份号码
     */
    private String gmsfhm;


    /**
     * 测试传“浙里拍照（临海版）”，正式ZLPZ-DL-HTB-XX(XX为版本号)
     */
    private String xtmc;

    /**
     * 	出入境照片base64
     */
    private String base64crj;

    /**
     * 母片照片base64
     */
    private String base64mp;

    /**
     * 是否人工强制通过（0否1是）。
     * 默认为0，如质检不通过，人工判断符合照片要求，即可上传1
     */
    private String sfrgqztg;

    /**
     *  新增字段，拍摄日期 yyyyMMdd
     */
    private String psrq;

    /**
     * 采集终端编码
     */
    private String cjzdbm;

    /**
     * 操作员用户表id
     */
    private String czyid;

    /**
     * 操作员用用户姓名
     */
    private String czyxm;

    /**
     * 设备id
     */
    private String sbid;
}
