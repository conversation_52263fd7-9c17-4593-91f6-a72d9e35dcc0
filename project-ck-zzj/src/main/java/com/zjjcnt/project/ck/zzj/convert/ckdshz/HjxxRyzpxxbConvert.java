package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxRyzpxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxRyzpxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxRyzpxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.HjxxRyzpxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxRyzpxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxRyzpxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.HjxxRyzpxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.HjxxRyzpxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
* 人员照片信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 11:35:02
*/
@Mapper
public interface HjxxRyzpxxbConvert {

    HjxxRyzpxxbConvert INSTANCE = Mappers.getMapper(HjxxRyzpxxbConvert.class);

    HjxxRyzpxxbDTO convert(HjxxRyzpxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    HjxxRyzpxxbDO convertToDO(HjxxRyzpxxbDTO dto);

    HjxxRyzpxxbDTO convertToDTO(HjxxRyzpxxbPageReq req);

    HjxxRyzpxxbDTO convertToDTO(HjxxRyzpxxbCreateReq req);

    HjxxRyzpxxbDTO convertToDTO(HjxxRyzpxxbUpdateReq req);

    HjxxRyzpxxbPageResp convertToPageResp(HjxxRyzpxxbDTO dto);

    @Mapping(target = "zp", expression = "java(dto.getZp() != null ? java.util.Base64.getEncoder().encodeToString(dto.getZp()) : \"\")")
    HjxxRyzpxxbViewResp convertToViewResp(HjxxRyzpxxbDTO dto);

    HjxxRyzpxxbCreateResp convertToCreateResp(HjxxRyzpxxbDTO dto);

}
