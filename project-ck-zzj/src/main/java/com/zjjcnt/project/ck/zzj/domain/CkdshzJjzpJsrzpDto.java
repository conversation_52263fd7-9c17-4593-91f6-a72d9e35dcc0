package com.zjjcnt.project.ck.zzj.domain;


import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 * 驾驶人照片写入接口
 */
@XmlRootElement(name = "root")
@XmlType(propOrder = {"head", "data"})
public class CkdshzJjzpJsrzpDto {
    private CkdshzJjzpHeadDto ckdshzJjzpHeadDto;
    private CkdshzJjzpDataDto data;

    @XmlElement(name = "head")
    public CkdshzJjzpHeadDto getHead() {
        return ckdshzJjzpHeadDto;
    }

    public void setHead(CkdshzJjzpHeadDto ckdshzJjzpHeadDto) {
        this.ckdshzJjzpHeadDto = ckdshzJjzpHeadDto;
    }

    @XmlElement(name = "drvphoto")
    public CkdshzJjzpDataDto getData() {
        return data;
    }

    public void setData(CkdshzJjzpDataDto data) {
        this.data = data;
    }

}
