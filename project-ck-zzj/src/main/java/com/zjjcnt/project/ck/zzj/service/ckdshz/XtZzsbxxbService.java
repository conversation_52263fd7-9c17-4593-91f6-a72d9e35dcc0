package com.zjjcnt.project.ck.zzj.service.ckdshz;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.XtZzsbxxbDTO;

import java.util.List;

/**
 * 自助设备信息表Service
 *
 * <AUTHOR>
 * @date 2024-05-07 11:19:21
 */
public interface XtZzsbxxbService extends IBaseService<XtZzsbxxbDTO> {

    /**
     * 根据appkey, sbip查询设备信息
     * @param appkey appkey
     * @param sbip 设备ip
     * @return
     */
    XtZzsbxxbDTO findEnabledByAppkeyAndSbip(String appkey, String sbip);

    /**
     * 根据appkey, sbip查询设备信息
     * @param appkey appkey
     * @param sbip 设备ip
     * @param sbmac 设备mac
     * @return
     */
    XtZzsbxxbDTO findEnabledByAppkeyAndSbipAndSbmac(String appkey, String sbip, String sbmac);


    /**
     * 根据APPKEY,sbxh 查询设备信息
     * @param
     * @return
     */
    XtZzsbxxbDTO findEnabledByAppkeyAndZlpzsbdwbh(String appkey, String zlpzsbdwbh);

    /**
     * 终端设备后台注册
     * @param xtZzsbxxbDTO
     * @return
     */
    XtZzsbxxbDTO insertZzsb(XtZzsbxxbDTO xtZzsbxxbDTO);

    XtZzsbxxbDTO updateZzsb(XtZzsbxxbDTO xtZzsbxxb);

    XtZzsbxxbDTO findByZlpzsbdwbh(String zlpzsbdwbh);

    XtZzsbxxbDTO findEnabledByAppkeyAndSbmac(String appkey, String sbmac);

    /**
     * 获取待注册设备列表
     * @param dto
     * @return
     */
    List<XtZzsbxxbDTO> listDzcZlpz(XtZzsbxxbDTO dto);
}
