package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.core.file.FileObjectService;
import com.zjjcnt.project.ck.core.file.domain.DefaultFileMeta;
import com.zjjcnt.project.ck.core.file.domain.DefaultFileObject;
import com.zjjcnt.project.ck.core.file.domain.FileMeta;
import com.zjjcnt.project.ck.core.file.domain.FileObject;
import com.zjjcnt.project.ck.zzj.convert.ck.ZzjYztyZpConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZzjYztyZpDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZzjYztyZpDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZzjYztyZpMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZzjYztyZpService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Base64;
import java.util.Objects;

/**
 * 自助机一照通用照片ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-01-14 16:13:45
 */
@Service
public class ZzjYztyZpServiceImpl extends AbstractBaseServiceImpl<ZzjYztyZpMapper, ZzjYztyZpDO, ZzjYztyZpDTO> implements ZzjYztyZpService {

    ZzjYztyZpConvert convert = ZzjYztyZpConvert.INSTANCE;

    private final FileObjectService fileObjectService;

    public ZzjYztyZpServiceImpl(@Qualifier("zzjYztyZpFileObjectService")FileObjectService fileObjectService) {
        this.fileObjectService = fileObjectService;
    }

    @Override
    protected QueryWrapper genQueryWrapper(ZzjYztyZpDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZzjYztyZpDO::getZjhm, dto.getZjhm(), StringUtils.isNotEmpty(dto.getZjhm()));
        query.eq(ZzjYztyZpDO::getCjrzh, dto.getCjrzh(), StringUtils.isNotEmpty(dto.getCjrzh()));
        query.ge(ZzjYztyZpDO::getBcsj, dto.getBcsjStart(), StringUtils.isNotEmpty(dto.getBcsjStart()));
        query.le(ZzjYztyZpDO::getBcsj, dto.getBcsjEnd(), StringUtils.isNotEmpty(dto.getBcsjEnd()));
        query.eq(ZzjYztyZpDO::getSbid, dto.getSbid(), Objects.nonNull(dto.getSbid()));
        query.eq(ZzjYztyZpDO::getCqsf, dto.getCqsf(), StringUtils.isNotEmpty(dto.getCqsf()));
        query.eq(ZzjYztyZpDO::getCqjg, dto.getCqjg(), StringUtils.isNotEmpty(dto.getCqjg()));
        query.eq(ZzjYztyZpDO::getCqcs, dto.getCqcs(), StringUtils.isNotEmpty(dto.getCqcs()));
        query.eq(ZzjYztyZpDO::getCqsbms, dto.getCqsbms(), StringUtils.isNotEmpty(dto.getCqsbms()));
        query.eq(ZzjYztyZpDO::getCrjzjjg, dto.getCrjzjjg(), StringUtils.isNotEmpty(dto.getCrjzjjg()));
        query.eq(ZzjYztyZpDO::getCrjzjms, dto.getCrjzjms(), StringUtils.isNotEmpty(dto.getCrjzjms()));
        query.eq(ZzjYztyZpDO::getMpzjjg, dto.getMpzjjg(), StringUtils.isNotEmpty(dto.getMpzjjg()));
        query.eq(ZzjYztyZpDO::getMpzjms, dto.getMpzjms(), StringUtils.isNotEmpty(dto.getMpzjms()));
        query.eq(ZzjYztyZpDO::getZpwjlx, dto.getZpwjlx(), StringUtils.isNotEmpty(dto.getZpwjlx()));
        query.eq(ZzjYztyZpDO::getZpsjdzlx, dto.getZpsjdzlx(), StringUtils.isNotEmpty(dto.getZpsjdzlx()));
        query.eq(ZzjYztyZpDO::getZpsjdz, dto.getZpsjdz(), StringUtils.isNotEmpty(dto.getZpsjdz()));
        query.in(ZzjYztyZpDO::getId, dto.getIdList(), !CollectionUtils.isEmpty(dto.getIdList()));
        return query;
    }

    @Override
    public ZzjYztyZpDTO convertToDTO(ZzjYztyZpDO zzjYztyZpDO) {
        return convert.convert(zzjYztyZpDO);
    }

    @Override
    public ZzjYztyZpDO convertToDO(ZzjYztyZpDTO zzjYztyZpDTO) {
        return convert.convertToDO(zzjYztyZpDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZzjYztyZpDTO insert(ZzjYztyZpDTO dto) {

        byte[] data = Base64.getDecoder().decode(dto.getZpBase64());

        dto.setZpdx(data.length);
        dto.setZphash(DigestUtils.md5Hex(data));

        String currentTime = ServerTimeUtils.getCurrentTime();

        dto.setBcsj(currentTime);
        dto.setZpwjlx(StringUtils.defaultIfBlank(dto.getZpwjlx(), MediaType.IMAGE_JPEG_VALUE));

        FileObject fo = new DefaultFileObject(data, dto.getZpwjlx(), dto.getZjhm());
        String wjdz = fileObjectService.save(fo, "yztyzp");
        String zpsjdzlx = fileObjectService.getStorageType();

        dto.setZpsjdz(wjdz);
        dto.setZpsjdzlx(zpsjdzlx);

        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        dto.setCjr(currentUser.getName());
        dto.setCjrzh(currentUser.getUsername());
        dto.setCjrip(currentUser.getRemoteAddress());
        dto.setCjsj(currentTime);

        return super.insert(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteById(Serializable id) {
        ZzjYztyZpDTO zzjYztyZpDTO = findById(id);

        if (Objects.isNull(zzjYztyZpDTO)) {
            return false;
        }

        DefaultFileMeta fileMeta = new DefaultFileMeta();
        fileMeta.setFileLocation(zzjYztyZpDTO.getZpsjdz());
        fileObjectService.delete(fileMeta);
        return super.deleteById(id);
    }

    @Override
    public byte[] getImageData(Long id) {
        ZzjYztyZpDTO zzjYztyZpDTO = findById(id);
        if (Objects.isNull(zzjYztyZpDTO)) {
            return null;
        }
        FileMeta fileMeta = new DefaultFileMeta();
        fileMeta.setFileLocation(zzjYztyZpDTO.getZpsjdz());
        return fileObjectService.getData(fileMeta);
    }
}
