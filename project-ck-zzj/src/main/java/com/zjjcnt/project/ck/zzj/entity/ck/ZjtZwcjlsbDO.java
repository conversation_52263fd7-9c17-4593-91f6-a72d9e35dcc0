package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:39:12
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjlsbDTO
 */
@Crypto
@Data
@Table("zjt_zwcjlsb")
public class ZjtZwcjlsbDO implements IdEntity<String> {
    private static final long serialVersionUID = 1929344579039718500L;

    /**
     * 指纹图像临时ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String zwtxid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 指纹采集结果代码
     */
    @Column(value = "zwcjjgdm")
    private String zwcjjgdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "szyczkdm")
    private String szyczkdm;

    /**
     * 指纹一注册结果
     */
    @Column(value = "zwyzcjg")
    private String zwyzcjg;

    /**
     * 指纹一指位
     */
    @Column(value = "zwyzw")
    private String zwyzw;

    /**
     * 指纹图像
     */
    @Column(value = "zwytxsj")
    private byte[] zwytxsj;

    /**
     * 指纹一图像质量值
     */
    @Column(value = "zwytxzlz")
    private BigDecimal zwytxzlz;

    /**
     * 指纹特征数据
     */
    @Column(value = "zwyzwtzsj")
    private byte[] zwyzwtzsj;

    /**
     * 指纹二注册结果
     */
    @Column(value = "zwezcjg")
    private String zwezcjg;

    /**
     * 指纹二指位
     */
    @Column(value = "zwezw")
    private String zwezw;

    /**
     * 指纹图像
     */
    @Column(value = "zwetxsj")
    private byte[] zwetxsj;

    /**
     * 指纹二图像质量值
     */
    @Column(value = "zwetxzlz")
    private BigDecimal zwetxzlz;

    /**
     * 指纹特征数据
     */
    @Column(value = "zwezwtzsj")
    private byte[] zwezwtzsj;

    /**
     * 指纹采集器代码
     */
    @Column(value = "zwqdxtzch")
    private String zwqdxtzch;

    /**
     * 指纹采集器标识号
     */
    @Column(value = "zwcjqid")
    private String zwcjqid;

    /**
     * 指纹算法版本号
     */
    @Column(value = "zwsfbbh")
    private String zwsfbbh;

    /**
     * 指纹算法开发者代码
     */
    @Column(value = "zwsfkfzdm")
    private String zwsfkfzdm;

    /**
     *
     */
    @Column(value = "czrid")
    private String czrid;

    /**
     * 保存时间
     */
    @Column(value = "bcsj")
    private String bcsj;

    /**
     * 操作员姓名
     */
    @Crypto
    @Column(value = "czyxm")
    private String czyxm;

    /**
     * 单位代码
     */
    @Column(value = "czydwdm")
    private String czydwdm;

    /**
     *
     */
    @Column(value = "czydwmc")
    private String czydwmc;

    /**
     *
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     *
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     *
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     *
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;


    @Override
    public String getId() {
        return this.zwtxid;
    }

    @Override
    public void setId(String id) {
        this.zwtxid = id;
    }
}
