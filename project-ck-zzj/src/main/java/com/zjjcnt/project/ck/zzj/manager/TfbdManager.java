package com.zjjcnt.project.ck.zzj.manager;

import cn.hutool.core.util.IdcardUtil;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.constant.CkZzjConstants;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.WwTfbdjgxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwTfbdjgxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.ZTaofanDTO;
import com.zjjcnt.project.ck.zzj.service.ckdshz.HjxxCzrkjbxxbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.WwTfbdjgxxbService;
import com.zjjcnt.project.ck.zzj.service.ckdshz.ZTaofanService;
import com.zjjcnt.project.ck.zzj.web.response.TfbdRes;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 逃犯比对
 *
 * <AUTHOR>
 * @date 2024-11-19 14:40:00
 */
@RequiredArgsConstructor
@Service
public class TfbdManager {

    private final HjxxCzrkjbxxbService hjxxCzrkjbxxbService;
    private final ZTaofanService zTaofanService;
    private final WwTfbdjgxxbService wwTfbdjgxxbService;

    private static final String SQL_TEMPLATE = "select * from z_taofan where SFZH = '%s' or SFZH = '%s'";

    @Transactional(rollbackFor = Exception.class)
    public TfbdRes doTfbd(String gmsfhm) {
        HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO = hjxxCzrkjbxxbService.findNormalByGmsfhm(gmsfhm);
        TfbdRes tfbdRes = new TfbdRes();
        tfbdRes.setResult("1");
        tfbdRes.setMessage("无比对信息");

        List<ZTaofanDTO> zTaofanList = zTaofanService.listBySfzh(gmsfhm);
        if (CollectionUtils.isEmpty(zTaofanList)) {
            tfbdRes.setResult("0");
            tfbdRes.setMessage("比对不成功");
        } else {
            CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
            wwTfbdjgxxbService.disableTodayWclByGmsfhmAndCzrid(gmsfhm, currentUser.getUserId());

            WwTfbdjgxxbDTO wwTfbdjgxxbDTO = WwTfbdjgxxbConvert.INSTANCE.convertToDTO(hjxxCzrkjbxxbDTO);
            if (Objects.isNull(wwTfbdjgxxbDTO)) {
                wwTfbdjgxxbDTO = new WwTfbdjgxxbDTO();
            }

            wwTfbdjgxxbDTO.setPptj(gmsfhm);
            String sql = String.format(SQL_TEMPLATE, gmsfhm, IdcardUtil.convert18To15(gmsfhm));
            wwTfbdjgxxbDTO.setBdyj(sql);
            wwTfbdjgxxbDTO.setCzrid(currentUser.getUserId());
            wwTfbdjgxxbDTO.setCzrip(currentUser.getRemoteAddress());
            wwTfbdjgxxbDTO.setCzsj(ServerTimeUtils.getCurrentTime());
            wwTfbdjgxxbDTO.setCzrdw(currentUser.getDeptCode());
            wwTfbdjgxxbDTO.setCzrxm(currentUser.getName());
            wwTfbdjgxxbDTO.setCzrdlm(currentUser.getUsername());
            wwTfbdjgxxbDTO.setBdywbh("121");
            wwTfbdjgxxbDTO.setJlbz(Constants.YES);
            wwTfbdjgxxbService.insert(wwTfbdjgxxbDTO);
            tfbdRes.setResult("4");
            tfbdRes.setMessage("人员数据异常，请到人工窗口处理");
        }

        if (Objects.isNull(hjxxCzrkjbxxbDTO)) {
            return tfbdRes;
        }

        if (CkZzjConstants.DJZT_DJ.equals(hjxxCzrkjbxxbDTO.getDjzt())) {
            tfbdRes.setResult("4");
            tfbdRes.setMessage(hjxxCzrkjbxxbDTO.getXm() + "(" +hjxxCzrkjbxxbDTO.getGmsfhm() + ")户口已冻结，不允许办理业务。");
        } else if (CkZzjConstants.DJZT_SD.equals(hjxxCzrkjbxxbDTO.getDjzt())) {
            tfbdRes.setResult("4");
            tfbdRes.setMessage(hjxxCzrkjbxxbDTO.getXm() + "(" +hjxxCzrkjbxxbDTO.getGmsfhm() + ")该人信息已锁定，不允许办理业务，请联系省厅户籍科。");
        }
        return tfbdRes;
    }

}
