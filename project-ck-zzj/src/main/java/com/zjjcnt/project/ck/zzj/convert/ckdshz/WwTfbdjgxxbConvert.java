package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwTfbdjgxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwTfbdjgxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwTfbdjgxxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwTfbdjgxxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwTfbdjgxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwTfbdjgxxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwTfbdjgxxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.WwTfbdjgxxbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 逃犯比对结果信息表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface WwTfbdjgxxbConvert {

    WwTfbdjgxxbConvert INSTANCE = Mappers.getMapper(WwTfbdjgxxbConvert.class);

    WwTfbdjgxxbDTO convert(WwTfbdjgxxbDO entity);

    @InheritInverseConfiguration(name="convert")
    WwTfbdjgxxbDO convertToDO(WwTfbdjgxxbDTO dto);

    WwTfbdjgxxbDTO convertToDTO(WwTfbdjgxxbPageReq req);

    WwTfbdjgxxbDTO convertToDTO(HjxxCzrkjbxxbDTO czrkjbxxbDTO);

    WwTfbdjgxxbDTO convertToDTO(WwTfbdjgxxbCreateReq req);

    WwTfbdjgxxbDTO convertToDTO(WwTfbdjgxxbUpdateReq req);

    WwTfbdjgxxbPageResp convertToPageResp(WwTfbdjgxxbDTO dto);

    WwTfbdjgxxbViewResp convertToViewResp(WwTfbdjgxxbDTO dto);

    WwTfbdjgxxbCreateResp convertToCreateResp(WwTfbdjgxxbDTO dto);

}
