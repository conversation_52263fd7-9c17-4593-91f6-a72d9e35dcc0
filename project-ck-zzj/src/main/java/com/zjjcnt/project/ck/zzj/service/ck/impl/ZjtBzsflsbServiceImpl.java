package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.exception.UserErrorCode;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.zzj.constant.ZjtBzsflsbType;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtBzsflsbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtBzsflsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSqxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtBzsflsbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtBzsflsbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtBzsflsbService;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtSqxxbService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 办证收费流水表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@RequiredArgsConstructor
@Service
public class ZjtBzsflsbServiceImpl extends AbstractBaseServiceImpl<ZjtBzsflsbMapper, ZjtBzsflsbDO, ZjtBzsflsbDTO> implements ZjtBzsflsbService {

    ZjtBzsflsbConvert convert = ZjtBzsflsbConvert.INSTANCE;

    private final ZjtSqxxbService zjtSqxxbService;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtBzsflsbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtBzsflsbDO::getNbslid, dto.getNbslid(), StringUtils.isNotEmpty(dto.getNbslid()));
        query.eq(ZjtBzsflsbDO::getYwslh, dto.getYwslh(), StringUtils.isNotEmpty(dto.getYwslh()));
        query.eq(ZjtBzsflsbDO::getSlh, dto.getSlh(), StringUtils.isNotEmpty(dto.getSlh()));
        query.eq(ZjtBzsflsbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtBzsflsbDO::getRynbid, dto.getRynbid(), StringUtils.isNotEmpty(dto.getRynbid()));
        query.eq(ZjtBzsflsbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtBzsflsbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtBzsflsbDO::getSlyy, dto.getSlyy(), StringUtils.isNotEmpty(dto.getSlyy()));
        query.eq(ZjtBzsflsbDO::getSlzt, dto.getSlzt(), StringUtils.isNotEmpty(dto.getSlzt()));
        query.eq(ZjtBzsflsbDO::getSfje, dto.getSfje(), Objects.nonNull(dto.getSfje()));
        query.eq(ZjtBzsflsbDO::getQfjg, dto.getQfjg(), StringUtils.isNotEmpty(dto.getQfjg()));
        query.ge(ZjtBzsflsbDO::getYxqxqsrq, dto.getYxqxqsrqStart(), StringUtils.isNotEmpty(dto.getYxqxqsrqStart()));
        query.le(ZjtBzsflsbDO::getYxqxqsrq, dto.getYxqxqsrqEnd(), StringUtils.isNotEmpty(dto.getYxqxqsrqEnd()));
        query.ge(ZjtBzsflsbDO::getYxqxjzrq, dto.getYxqxjzrqStart(), StringUtils.isNotEmpty(dto.getYxqxjzrqStart()));
        query.le(ZjtBzsflsbDO::getYxqxjzrq, dto.getYxqxjzrqEnd(), StringUtils.isNotEmpty(dto.getYxqxjzrqEnd()));
        query.eq(ZjtBzsflsbDO::getSflx, dto.getSflx(), StringUtils.isNotEmpty(dto.getSflx()));
        query.eq(ZjtBzsflsbDO::getSlfs, dto.getSlfs(), StringUtils.isNotEmpty(dto.getSlfs()));
        query.eq(ZjtBzsflsbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.likeLeft(ZjtBzsflsbDO::getSsxq, dto.getSsxq(), StringUtils.isNotEmpty(dto.getSsxq()));
        query.eq(ZjtBzsflsbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtBzsflsbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtBzsflsbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(ZjtBzsflsbDO::getSldfjsjgsdwdm, dto.getSldfjsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwdm()));
        query.eq(ZjtBzsflsbDO::getSldfjsjgsdwmc, dto.getSldfjsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwmc()));
        query.ge(ZjtBzsflsbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjtBzsflsbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjtBzsflsbDO::getCzyid, dto.getCzyid(), StringUtils.isNotEmpty(dto.getCzyid()));
        query.eq(ZjtBzsflsbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(ZjtBzsflsbDO::getDwdm, dto.getDwdm(), StringUtils.isNotEmpty(dto.getDwdm()));
        query.eq(ZjtBzsflsbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.eq(ZjtBzsflsbDO::getSfdjh, dto.getSfdjh(), StringUtils.isNotEmpty(dto.getSfdjh()));
        return query;
    }

    @Override
    public ZjtBzsflsbDTO convertToDTO(ZjtBzsflsbDO zjtBzsflsbDO) {
        return convert.convert(zjtBzsflsbDO);
    }

    @Override
    public ZjtBzsflsbDO convertToDO(ZjtBzsflsbDTO zjtBzsflsbDTO) {
        return convert.convertToDO(zjtBzsflsbDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtBzsflsbDTO insert(ZjtSlxxbDTO zjtSlxxb, ZjtBzsflsbType type) {
        Objects.requireNonNull(zjtSlxxb);
        ZjtBzsflsbDTO entity = convert.convertToDTO(zjtSlxxb);
        entity.setType(type);
        return insert(entity);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtBzsflsbDTO insert(ZjtBzsflsbDTO entity) {
        Objects.requireNonNull(entity);
        Validate.notNull(entity.getType(), "新增办证收费流水时, 收支(type)类型不能为空.");
        Validate.notBlank(entity.getYwslh(), "新增办证收费流水时, 受理申请号(ywslh)不能为空.");
        Validate.notBlank(entity.getNbslid(), "新增办证收费流水时, 受理号(nbslid)不能为空.");

        /*不再校验收费金额是否为null或0 fudongwz 2017-03-21
          1、收费金额、分成金额不会为空，如果为则说明数据问题
          2、收费金额有可能为0，但是分成金额不等于0
        */
//        if (isNotNeedInsert(entity)) {
//            logger.debug("不需要为nbslid={}的受理信息新增办证收费流水, 因为sfje={}",
//                    entity.getNbslid(), entity.getSfje());
//            return null;
//        }
        this.handleNullFjce(entity);
        //校验金额是否为空，及分成金额是否相等
        this.validateZjtBzsflsb(entity);

        if (ZjtBzsflsbType.SR == entity.getType()) {
            if (entity.getSfje().compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "新增办证收费流水-收入时, 收费金额(sfje)不能小于0.");
            }
            if (entity.getZfcje().compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "新增办证收费流水-收入时, 总分成金额(zfcje)不能小于0.");
            }
        } else {
            // 支出时如果收费金额不为负数则转换为负数.
            if (entity.getSfje().compareTo(BigDecimal.ZERO) > 0) {
                entity.setSfje(entity.getSfje().negate());
            }
            if (entity.getZfcje().compareTo(BigDecimal.ZERO) > 0) {
                entity.setZfcje(entity.getZfcje().negate());
            }
            if (entity.getQxfcje().compareTo(BigDecimal.ZERO) > 0) {
                entity.setQxfcje(entity.getQxfcje().negate());
            }
            if (entity.getDsfcje().compareTo(BigDecimal.ZERO) > 0) {
                entity.setDsfcje(entity.getDsfcje().negate());
            }
            if (entity.getZxfcje().compareTo(BigDecimal.ZERO) > 0) {
                entity.setZxfcje(entity.getZxfcje().negate());
            }
        }

        if (entity.getSfje().compareTo(BigDecimal.ZERO) < 0) {
            ZjtBzsflsbDTO lastOne = findLastSfjeSr(entity.getYwslh(), entity.getNbslid());
            if (lastOne == null) {
                throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "未找到nbslid=" + entity.getNbslid()
                        + "的收费流水记录, 无法进行冲销操作.");
            }
            if (entity.getSfje().add(lastOne.getSfje()).compareTo(BigDecimal.ZERO) != 0) {
                throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "无法新增办证收费流水: nbslid=" + entity.getNbslid()
                        + "的办证冲销金额(" + entity.getSfje() + ")不等于收费金额(" + lastOne.getSfje() + ").");
            }
        }

        if (entity.getZfcje().compareTo(BigDecimal.ZERO) < 0) {
            ZjtBzsflsbDTO lastOne = findLastFcjeSr(entity.getYwslh(), entity.getNbslid());
            //考虑历史数据，历史数据lastOne可能就是为空 todo:历史数据是不是可以不考虑，毕竟目前历史数据不多（2017-03-22）
//            if (lastOne == null) {
//                throw new ServiceException("未找到nbslid=" + entity.getNbslid()
//                        + "的收费流水记录, 无法进行冲销操作.");
//            }
            //考虑到历史数据，上一条的分成金额有可能为空，这里进行例外处理 todo:历史数据是不是可以不考虑，毕竟目前历史数据不多（2017-03-21）
            if (lastOne != null && lastOne.getZfcje() != null
                    && entity.getZfcje().add(lastOne.getZfcje()).compareTo(BigDecimal.ZERO) != 0) {
                throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "无法新增办证收费流水: nbslid=" + entity.getNbslid()
                        + "的办证总分成冲销金额(" + entity.getZfcje() + ")不等于总分成金额(" + lastOne.getZfcje() + ").");
            }
        }

        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        entity.setCzsj(ServerTimeUtils.getCurrentTime());
        entity.setCzyid(String.valueOf(currentUser.getUserId()));
        entity.setCzyxm(currentUser.getName());
        entity.setDwdm(currentUser.getDeptCode());
        entity.setCzydwmc(currentUser.getDeptName());

        ZjtBzsflsbDTO result = super.insert(entity);

        // 更新申请信息表的最终收费金额.
        ZjtSqxxbDTO zjtSqxxb = zjtSqxxbService.findById(entity.getYwslh());
        Validate.notNull(zjtSqxxb, "无法新增办证收费流水: 未找到ywslh="
                + entity.getYwslh() + "的制证申请信息.");
        zjtSqxxb.setSfje(calculateSfje(entity.getYwslh()));

        return result;
    }

    private BigDecimal calculateSfje(String ywslh) {
        Validate.notNull(ywslh);

        List<ZjtBzsflsbDTO> list = listByYwslh(ywslh);
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = BigDecimal.ZERO;
        for (ZjtBzsflsbDTO entity : list) {
            result = result.add(entity.getSfje());
        }
        return result;
    }

    private List<ZjtBzsflsbDTO> listByYwslh(String ywslh) {
        ZjtBzsflsbDTO query = new ZjtBzsflsbDTO();
        query.setYwslh(ywslh);
        return list(query);
    }

    /**
     * 设置fcje为null时的情况：该情况只会在历史数据上，且只有将历史数据进行冲销（将收入记为支出）时，才会出现，否则收入如果也为null，则说明是数据问题
     * 如果不再考虑这种历史数据，可以将该方法去除
     * fudongwz 2017-03-22
     */
    private void handleNullFjce(ZjtBzsflsbDTO entity) {
        //只考虑冲销（支出）的情况
        if (ZjtBzsflsbType.SR == entity.getType()) {
            return;
        }
        if (entity.getZfcje() == null) {
            entity.setZfcje(BigDecimal.ZERO);
        }
        if (entity.getQxfcje() == null) {
            entity.setQxfcje(BigDecimal.ZERO);
        }
        if (entity.getDsfcje() == null) {
            entity.setDsfcje(BigDecimal.ZERO);
        }
        if (entity.getZxfcje() == null) {
            entity.setZxfcje(BigDecimal.ZERO);
        }
    }

    /**
     * 校验收费是否为空：收费金额或分成金额为空，则说明数据为题
     */
    private void validateZjtBzsflsb(ZjtBzsflsbDTO entity) {
        if (entity.getSfje() == null) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "ZjtBzsflsb.sfje为NULL.");
        }
        if (entity.getZfcje() == null) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "ZjtBzsflsb.zfcje为NULL.");
        }
        if (entity.getQxfcje() == null) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "ZjtBzsflsb.qxfcje为NULL.");
        }
        if (entity.getDsfcje() == null) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "ZjtBzsflsb.dsfcje为NULL.");
        }
        if (entity.getZxfcje() == null) {
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR, "ZjtBzsflsb.zxfcje为NULL.");
        }
        //校验分成金额是否相等
        BigDecimal sumFcje = entity.getQxfcje().add(entity.getDsfcje()).add(entity.getZxfcje());
        if (entity.getZfcje().compareTo(sumFcje) != 0) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "zfcje【" + entity.getZfcje() + "】不等于qxfcje+dsfcje+zxfcje【" + sumFcje + "】");
        }
    }


    /**
     * 获取最后一条资金收入记录(收费金额)
     * @param ywslh
     * @param nbslid
     * @return
     */
    private ZjtBzsflsbDTO findLastSfjeSr(String ywslh, String nbslid) {
        Validate.notEmpty(ywslh);
        Validate.notEmpty(nbslid);
        QueryWrapper query = QueryWrapper.create()
                .eq(ZjtBzsflsbDO::getYwslh, ywslh)
                .eq(ZjtBzsflsbDO::getNbslid, nbslid)
                .ge(ZjtBzsflsbDO::getSfje, BigDecimal.ZERO)
                .orderBy(ZjtBzsflsbDO::getCzsj, false)
                .limit(1);
        return find(query);
    }

    /**
     * 获取最后一条资金收入记录(分成金额)
     * @param ywslh
     * @param nbslid
     * @return
     */
    private ZjtBzsflsbDTO findLastFcjeSr(String ywslh, String nbslid) {
        Validate.notNull(ywslh);
        Validate.notNull(nbslid);
        QueryWrapper query = QueryWrapper.create()
                .eq(ZjtBzsflsbDO::getYwslh, ywslh)
                .eq(ZjtBzsflsbDO::getNbslid, nbslid)
                .ge(ZjtBzsflsbDO::getZfcje, BigDecimal.ZERO)
                .orderBy(ZjtBzsflsbDO::getCzsj, false)
                .limit(1);
        return find(query);
    }

}
