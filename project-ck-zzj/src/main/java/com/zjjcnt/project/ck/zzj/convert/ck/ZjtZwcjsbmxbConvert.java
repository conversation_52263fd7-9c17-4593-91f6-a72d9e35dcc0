package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjsbmxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjsbmxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjsbmxbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtZwcjsbmxbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbmxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbmxbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtZwcjsbmxbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtZwcjsbmxbDO;
import com.zjjcnt.project.ck.zzj.exp.ck.ZjtZwcjsbmxbExp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* Convert
*
* <AUTHOR>
* @date 2024-05-09 13:35:50
*/
@Mapper
public interface ZjtZwcjsbmxbConvert {

    ZjtZwcjsbmxbConvert INSTANCE = Mappers.getMapper(ZjtZwcjsbmxbConvert.class);

    ZjtZwcjsbmxbDTO convert(ZjtZwcjsbmxbDO entity);

    @InheritInverseConfiguration(name="convert")
    ZjtZwcjsbmxbDO convertToDO(ZjtZwcjsbmxbDTO dto);

    ZjtZwcjsbmxbDTO convertToDTO(ZjtZwcjsbmxbPageReq req);

    ZjtZwcjsbmxbDTO convertToDTO(ZjtZwcjsbmxbCreateReq req);

    ZjtZwcjsbmxbDTO convertToDTO(ZjtZwcjsbmxbUpdateReq req);

    ZjtZwcjsbmxbPageResp convertToPageResp(ZjtZwcjsbmxbDTO dto);

    ZjtZwcjsbmxbViewResp convertToViewResp(ZjtZwcjsbmxbDTO dto);

    ZjtZwcjsbmxbCreateResp convertToCreateResp(ZjtZwcjsbmxbDTO dto);

    ZjtZwcjsbmxbExp convertToExp(ZjtZwcjsbmxbDTO dto);

}
