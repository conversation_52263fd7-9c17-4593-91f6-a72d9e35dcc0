package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtCzzsdwdybConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtCzzsdwdybDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtCzzsdwdybDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtCzzsdwdybMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtCzzsdwdybService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 财政执收单位对应表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Service
public class ZjtCzzsdwdybServiceImpl extends AbstractBaseServiceImpl<ZjtCzzsdwdybMapper, ZjtCzzsdwdybDO, ZjtCzzsdwdybDTO> implements ZjtCzzsdwdybService {

    ZjtCzzsdwdybConvert convert = ZjtCzzsdwdybConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtCzzsdwdybDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtCzzsdwdybDO::getSldfjsjgsdwdm, dto.getSldfjsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwdm()));
        query.eq(ZjtCzzsdwdybDO::getZsdwdm, dto.getZsdwdm(), StringUtils.isNotEmpty(dto.getZsdwdm()));
        query.eq(ZjtCzzsdwdybDO::getXzqhdm, dto.getXzqhdm(), StringUtils.isNotEmpty(dto.getXzqhdm()));
        query.eq(ZjtCzzsdwdybDO::getSfywlx, dto.getSfywlx(), StringUtils.isNotEmpty(dto.getSfywlx()));
        query.eq(ZjtCzzsdwdybDO::getSfdj, dto.getSfdj(), Objects.nonNull(dto.getSfdj()));
        query.eq(ZjtCzzsdwdybDO::getSfsl, dto.getSfsl(), Objects.nonNull(dto.getSfsl()));
        query.eq(ZjtCzzsdwdybDO::getSfje, dto.getSfje(), Objects.nonNull(dto.getSfje()));
        return query;
    }

    @Override
    public ZjtCzzsdwdybDTO convertToDTO(ZjtCzzsdwdybDO zjtCzzsdwdybDO) {
        return convert.convert(zjtCzzsdwdybDO);
    }

    @Override
    public ZjtCzzsdwdybDO convertToDO(ZjtCzzsdwdybDTO zjtCzzsdwdybDTO) {
        return convert.convertToDO(zjtCzzsdwdybDTO);
    }

    @Override
    public List<ZjtCzzsdwdybDTO> findDwdyb(String fjsjgsdwdm, String sfywlx, BigDecimal sfje) {
        ZjtCzzsdwdybDTO dto  = new ZjtCzzsdwdybDTO();
        dto.setSldfjsjgsdwdm(fjsjgsdwdm);
        dto.setSfywlx(sfywlx);
        dto.setSfje(sfje);
        return list(dto);
    }

}
