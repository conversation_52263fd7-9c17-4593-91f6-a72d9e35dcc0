package com.zjjcnt.project.ck.zzj.convert.ckdshz;

import com.zjjcnt.project.ck.zzj.dto.ckdshz.WwJkcsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwJkcsbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwJkcsbPageReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.req.WwJkcsbUpdateReq;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwJkcsbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwJkcsbPageResp;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.resp.WwJkcsbViewResp;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.WwJkcsbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 外围接口参数表Convert
*
* <AUTHOR>
* @date 2024-05-07 10:18:38
*/
@Mapper
public interface WwJkcsbConvert {

    WwJkcsbConvert INSTANCE = Mappers.getMapper(WwJkcsbConvert.class);

    WwJkcsbDTO convert(WwJkcsbDO entity);

    @InheritInverseConfiguration(name="convert")
    WwJkcsbDO convertToDO(WwJkcsbDTO dto);

    WwJkcsbDTO convertToDTO(WwJkcsbPageReq req);

    WwJkcsbDTO convertToDTO(WwJkcsbCreateReq req);

    WwJkcsbDTO convertToDTO(WwJkcsbUpdateReq req);

    WwJkcsbPageResp convertToPageResp(WwJkcsbDTO dto);

    WwJkcsbViewResp convertToViewResp(WwJkcsbDTO dto);

    WwJkcsbCreateResp convertToCreateResp(WwJkcsbDTO dto);

}
