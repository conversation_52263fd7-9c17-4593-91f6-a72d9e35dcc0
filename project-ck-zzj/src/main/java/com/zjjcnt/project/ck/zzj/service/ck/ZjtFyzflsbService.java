package com.zjjcnt.project.ck.zzj.service.ck;

import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtFyzflsbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtLssfzSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;

/**
 * 费用支付流水表Service
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
public interface ZjtFyzflsbService extends IBaseService<ZjtFyzflsbDTO> {

    ZjtFyzflsbDTO addZjtSlxxbZjtFyzflsb(ZjtSlxxbDTO zjtSlxxb, boolean issf);

    ZjtFyzflsbDTO addZjtLssfzSlxxbZjtFyzflsb(ZjtLssfzSlxxbDTO zjtLssfzSlxxb, ZjtSlxxbDTO zjtSlxxbDTO);

    String queryJfewm(String jkdh);
}
