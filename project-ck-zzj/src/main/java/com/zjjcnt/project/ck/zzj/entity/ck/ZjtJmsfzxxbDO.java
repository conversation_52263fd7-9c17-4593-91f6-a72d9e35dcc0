package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 居民身份证信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtJmsfzxxbDTO
 */
@Crypto
@Data
@Table("zjt_jmsfzxxb")
public class ZjtJmsfzxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = 7410533890120438092L;

    /**
     * 内部身份证ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String nbsfzid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 照片ID
     */
    @Column(value = "zpid")
    private String zpid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 性别
     */
    @Column(value = "xb")
    private String xb;

    /**
     * 民族
     */
    @Column(value = "mz")
    private String mz;

    /**
     * 出生日期
     */
    @Column(value = "csrq")
    private String csrq;

    /**
     * 签发机关
     */
    @Column(value = "qfjg")
    private String qfjg;

    /**
     * 有效期限起始日期
     */
    @Column(value = "yxqxqsrq")
    private String yxqxqsrq;

    /**
     * 有效期限截止日期
     */
    @Column(value = "yxqxjzrq")
    private String yxqxjzrq;

    /**
     * 申领原因
     */
    @Column(value = "slyy")
    private String slyy;

    /**
     * 办证原因
     */
    @Column(value = "bzyy")
    private String bzyy;

    /**
     * 收缴原因
     */
    @Column(value = "sjyy")
    private String sjyy;

    /**
     * 领证方式
     */
    @Column(value = "lqfs")
    private String lqfs;

    /**
     * 住址
     */
    @Column(value = "zz")
    private String zz;

    /**
     * 证件类别
     */
    @Column(value = "zjlb")
    private String zjlb;

    /**
     * 证件状态
     */
    @Column(value = "zjzt")
    private String zjzt;

    /**
     * 同步标志
     */
    @Column(value = "tbbz")
    private Integer tbbz;

    /**
     * 包文编号
     */
    @Column(value = "bwbh")
    private String bwbh;

    /**
     * 受理号
     */
    @Column(value = "slh")
    private String slh;

    /**
     *
     */
    @Column(value = "ktglh")
    private String ktglh;

    /**
     * 增加时间
     */
    @Column(value = "zjsj")
    private String zjsj;

    /**
     * 增加类型
     */
    @Column(value = "zjlx")
    private String zjlx;

    /**
     * 指纹一指纹特征数据
     */
    @Column(value = "zwyzwtzsj")
    private byte[] zwyzwtzsj;

    /**
     * 指纹二指纹特征数据
     */
    @Column(value = "zwezwtzsj")
    private byte[] zwezwtzsj;

    /**
     * 机读照片文件编号
     */
    @Column(value = "czrkjdzpwjbh")
    private String czrkjdzpwjbh;

    /**
     * 指纹一指位
     */
    @Column(value = "zwyzw")
    private String zwyzw;

    /**
     * 指纹一注册结果
     */
    @Column(value = "zwyzcjg")
    private String zwyzcjg;

    /**
     * 指纹二指位
     */
    @Column(value = "zwezw")
    private String zwezw;

    /**
     * 指纹二注册结果
     */
    @Column(value = "zwezcjg")
    private String zwezcjg;

    /**
     * 指纹采集结果代码
     */
    @Column(value = "zwcjjgdm")
    private String zwcjjgdm;

    /**
     * 手指异常状况代码
     */
    @Column(value = "szyczkdm")
    private String szyczkdm;

    /**
     * 证件来源类别
     */
    @Column(value = "zjlylb")
    private String zjlylb;

    /**
     * 证件来源时间
     */
    @Column(value = "zjlysj")
    private String zjlysj;

    /**
     * 数据归属单位代码
     */
    @Column(value = "hjdsjgsdwdm")
    private String hjdsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "hjdsjgsdwmc")
    private String hjdsjgsdwmc;

    /**
     * 数据归属单位代码
     */
    @Column(value = "sldsjgsdwdm")
    private String sldsjgsdwdm;

    /**
     * 数据归属单位名称
     */
    @Column(value = "sldsjgsdwmc")
    private String sldsjgsdwmc;

    /**
     * 暂居住地省市县区
     */
    @Column(value = "zjzdssxq")
    private String zjzdssxq;

    /**
     * 暂居住地详址
     */
    @Column(value = "zjzdxz")
    private String zjzdxz;

    /**
     *
     */
    @Crypto
    @Column(value = "lxdh")
    private String lxdh;


    @Override
    public String getId() {
        return this.nbsfzid;
    }

    @Override
    public void setId(String id) {
        this.nbsfzid = id;
    }
}
