package com.zjjcnt.project.ck.zzj.convert.ck;

import com.zjjcnt.project.ck.zzj.dto.ck.RzbJkdyrzbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.RzbJkdyrzbDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 第三方接口调用日志表Convert
*
* <AUTHOR>
* @date 2025-05-09 13:40:14
*/
@Mapper
public interface RzbJkdyrzbConvert {

    RzbJkdyrzbConvert INSTANCE = Mappers.getMapper(RzbJkdyrzbConvert.class);

    RzbJkdyrzbDTO convert(RzbJkdyrzbDO entity);

    @InheritInverseConfiguration(name="convert")
    RzbJkdyrzbDO convertToDO(RzbJkdyrzbDTO dto);

}
