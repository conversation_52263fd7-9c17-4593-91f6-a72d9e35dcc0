
package com.zjjcnt.project.ck.zzj.third.crj;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.zjjcnt.project.ck.zzj.third.crj package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _LoginNewResponseReturn_QNAME = new QName("http://e3.org/eecmisws/soapws", "return");
    private final static QName _LoginNewArgs_QNAME = new QName("http://e3.org/eecmisws/soapws", "args");
    private final static QName _UnimethodSessionId_QNAME = new QName("http://e3.org/eecmisws/soapws", "sessionId");
    private final static QName _UnimethodFunccode_QNAME = new QName("http://e3.org/eecmisws/soapws", "funccode");
    private final static QName _LoginPassword_QNAME = new QName("http://e3.org/eecmisws/soapws", "password");
    private final static QName _LoginLoginName_QNAME = new QName("http://e3.org/eecmisws/soapws", "loginName");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.zjjcnt.project.ck.zzj.third.crj
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link LoginResponse }
     * 
     */
    public LoginResponse createLoginResponse() {
        return new LoginResponse();
    }

    /**
     * Create an instance of {@link LoginNew }
     * 
     */
    public LoginNew createLoginNew() {
        return new LoginNew();
    }

    /**
     * Create an instance of {@link UnimethodResponse }
     * 
     */
    public UnimethodResponse createUnimethodResponse() {
        return new UnimethodResponse();
    }

    /**
     * Create an instance of {@link Login }
     * 
     */
    public Login createLogin() {
        return new Login();
    }

    /**
     * Create an instance of {@link LoginNewResponse }
     * 
     */
    public LoginNewResponse createLoginNewResponse() {
        return new LoginNewResponse();
    }

    /**
     * Create an instance of {@link Unimethod }
     * 
     */
    public Unimethod createUnimethod() {
        return new Unimethod();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "return", scope = LoginNewResponse.class)
    public JAXBElement<String> createLoginNewResponseReturn(String value) {
        return new JAXBElement<String>(_LoginNewResponseReturn_QNAME, String.class, LoginNewResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "return", scope = LoginResponse.class)
    public JAXBElement<String> createLoginResponseReturn(String value) {
        return new JAXBElement<String>(_LoginNewResponseReturn_QNAME, String.class, LoginResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "args", scope = LoginNew.class)
    public JAXBElement<String> createLoginNewArgs(String value) {
        return new JAXBElement<String>(_LoginNewArgs_QNAME, String.class, LoginNew.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "return", scope = UnimethodResponse.class)
    public JAXBElement<String> createUnimethodResponseReturn(String value) {
        return new JAXBElement<String>(_LoginNewResponseReturn_QNAME, String.class, UnimethodResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "sessionId", scope = Unimethod.class)
    public JAXBElement<String> createUnimethodSessionId(String value) {
        return new JAXBElement<String>(_UnimethodSessionId_QNAME, String.class, Unimethod.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "args", scope = Unimethod.class)
    public JAXBElement<String> createUnimethodArgs(String value) {
        return new JAXBElement<String>(_LoginNewArgs_QNAME, String.class, Unimethod.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "funccode", scope = Unimethod.class)
    public JAXBElement<String> createUnimethodFunccode(String value) {
        return new JAXBElement<String>(_UnimethodFunccode_QNAME, String.class, Unimethod.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "password", scope = Login.class)
    public JAXBElement<String> createLoginPassword(String value) {
        return new JAXBElement<String>(_LoginPassword_QNAME, String.class, Login.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://e3.org/eecmisws/soapws", name = "loginName", scope = Login.class)
    public JAXBElement<String> createLoginLoginName(String value) {
        return new JAXBElement<String>(_LoginLoginName_QNAME, String.class, Login.class, value);
    }

}
