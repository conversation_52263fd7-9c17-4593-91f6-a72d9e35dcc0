package com.zjjcnt.project.ck.zzj.util;


import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

/**
 * Created by simon on 2020/09/17.
 */
public class CkHttpUtils {

    public static String sendPost1(String url, String data, String contenttype, String sessionid) {
        CloseableHttpClient httpClient = null;// HTTP客户端
        CloseableHttpResponse response = null;// 响应模型

        try {
            httpClient = HttpClientBuilder.create().build();

            // 创建Post请求
            HttpPost httpPost = new HttpPost(url);

            // post请求是将参数放在请求体里面传过去的;这里将entity放入post请求体中
            if (data != null) {
                httpPost.setEntity(new StringEntity(data, "UTF-8"));
            }
            if (contenttype != null) {
                httpPost.setHeader("Content-Type", contenttype);
            }
            if (sessionid != null ){
                httpPost.setHeader("session_id", sessionid);
            }

            // 由客户端执行(发送)Post请求
            response = httpClient.execute(httpPost);
            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();

            //System.out.println("响应状态为:" + response.getStatusLine());
            if (responseEntity != null) {
                //System.out.println("响应内容长度为:" + responseEntity.getContentLength());
                String retstr = EntityUtils.toString(responseEntity);
                //System.out.println("响应内容为:" + retstr);
                return retstr;
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 发送string，设置超时时间
     *
     * @param url
     * @param data
     * @param contenttype
     * @return
     */
    public static String sendPost2(String url, String data, String contenttype) throws Exception {
        CloseableHttpClient httpClient = null;// HTTP客户端
        CloseableHttpResponse response = null;// 响应模型

        try {
            httpClient = HttpClientBuilder.create().build();

            // 创建Post请求
            HttpPost httpPost = new HttpPost(url);

            // 设置超时时间 毫秒
            RequestConfig requestConfig = RequestConfig.custom()
                    // 设置连接超时时间(单位毫秒)
                    .setConnectTimeout(30000)
                    // 设置请求超时时间(单位毫秒)
                    .setConnectionRequestTimeout(30000)
                    // socket读写超时时间(单位毫秒)
                    .setSocketTimeout(30000)
                    .build();
            httpPost.setConfig(requestConfig);

            // post请求是将参数放在请求体里面传过去的;这里将entity放入post请求体中
            httpPost.setEntity(new StringEntity(data, "UTF-8"));
            if (contenttype != null) {
                httpPost.setHeader("Content-Type", contenttype);
            }

            // 由客户端执行(发送)Post请求
            response = httpClient.execute(httpPost);
            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();

            //System.out.println("响应状态为:" + response.getStatusLine());
            if (responseEntity != null) {
                //System.out.println("响应内容长度为:" + responseEntity.getContentLength());
                String retstr = EntityUtils.toString(responseEntity, "UTF-8");
                //System.out.println("响应内容为:" + retstr);
                return retstr;
            }
        } catch (Exception e) {
            throw new Exception("调用第三方接口异常：", e);
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static String sendGet(String url, String hhid) {
        CloseableHttpClient httpClient = null;// HTTP客户端
        CloseableHttpResponse response = null;// 响应模型

        try {
            httpClient = HttpClientBuilder.create().build();

            // 创建Get请求
            HttpGet httpGet = new HttpGet(url);
            if (hhid != null) {
                httpGet.setHeader("session_id", hhid);
            }
            // 由客户端执行(发送)get请求
            response = httpClient.execute(httpGet);
            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();

            //System.out.println("响应状态为:" + response.getStatusLine());
            if (responseEntity != null) {
                //System.out.println("响应内容长度为:" + responseEntity.getContentLength());
                String retstr = EntityUtils.toString(responseEntity);
                //System.out.println("响应内容为:" + retstr);
                return retstr;
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return null;
    }


}
