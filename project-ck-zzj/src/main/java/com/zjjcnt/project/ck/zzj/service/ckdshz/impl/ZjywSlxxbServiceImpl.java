package com.zjjcnt.project.ck.zzj.service.ckdshz.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import com.zjjcnt.project.ck.zzj.convert.ckdshz.ZjywSlxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ckdshz.ZjywSlxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ckdshz.ZjywSlxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ckdshz.ZjywSlxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ckdshz.ZjywSlxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 受理信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-07 10:18:38
 */
@Service
public class ZjywSlxxbServiceImpl extends AbstractBaseServiceImpl<ZjywSlxxbMapper, ZjywSlxxbDO, ZjywSlxxbDTO> implements ZjywSlxxbService {

    ZjywSlxxbConvert convert = ZjywSlxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjywSlxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjywSlxxbDO::getSlh, dto.getSlh(), Objects.nonNull(dto.getSlh()));
        query.eq(ZjywSlxxbDO::getRyid, dto.getRyid(), Objects.nonNull(dto.getRyid()));
        query.eq(ZjywSlxxbDO::getRynbid, dto.getRynbid(), Objects.nonNull(dto.getRynbid()));
        query.eq(ZjywSlxxbDO::getZpid, dto.getZpid(), Objects.nonNull(dto.getZpid()));
        query.eq(ZjywSlxxbDO::getQfjg, dto.getQfjg(), Objects.nonNull(dto.getQfjg()));
        query.ge(ZjywSlxxbDO::getYxqxqsrq, dto.getYxqxqsrqStart(), StringUtils.isNotEmpty(dto.getYxqxqsrqStart()));
        query.le(ZjywSlxxbDO::getYxqxqsrq, dto.getYxqxqsrqEnd(), StringUtils.isNotEmpty(dto.getYxqxqsrqEnd()));
        query.ge(ZjywSlxxbDO::getYxqxjzrq, dto.getYxqxjzrqStart(), StringUtils.isNotEmpty(dto.getYxqxjzrqStart()));
        query.le(ZjywSlxxbDO::getYxqxjzrq, dto.getYxqxjzrqEnd(), StringUtils.isNotEmpty(dto.getYxqxjzrqEnd()));
        query.eq(ZjywSlxxbDO::getZz, dto.getZz(), Objects.nonNull(dto.getZz()));
        query.eq(ZjywSlxxbDO::getSlyy, dto.getSlyy(), Objects.nonNull(dto.getSlyy()));
        query.eq(ZjywSlxxbDO::getZzlx, dto.getZzlx(), Objects.nonNull(dto.getZzlx()));
        query.eq(ZjywSlxxbDO::getLqfs, dto.getLqfs(), Objects.nonNull(dto.getLqfs()));
        query.eq(ZjywSlxxbDO::getSflx, dto.getSflx(), Objects.nonNull(dto.getSflx()));
        query.eq(ZjywSlxxbDO::getSfje, dto.getSfje(), Objects.nonNull(dto.getSfje()));
        query.eq(ZjywSlxxbDO::getSjblsh, dto.getSjblsh(), Objects.nonNull(dto.getSjblsh()));
        query.eq(ZjywSlxxbDO::getSlzt, dto.getSlzt(), Objects.nonNull(dto.getSlzt()));
        query.eq(ZjywSlxxbDO::getZjywid, dto.getZjywid(), Objects.nonNull(dto.getZjywid()));
        query.eq(ZjywSlxxbDO::getCxbz, dto.getCxbz(), Objects.nonNull(dto.getCxbz()));
        query.ge(ZjywSlxxbDO::getCxsj, dto.getCxsjStart(), StringUtils.isNotEmpty(dto.getCxsjStart()));
        query.le(ZjywSlxxbDO::getCxsj, dto.getCxsjEnd(), StringUtils.isNotEmpty(dto.getCxsjEnd()));
        query.eq(ZjywSlxxbDO::getCxrid, dto.getCxrid(), Objects.nonNull(dto.getCxrid()));
        query.eq(ZjywSlxxbDO::getCxzjywid, dto.getCxzjywid(), Objects.nonNull(dto.getCxzjywid()));
        query.eq(ZjywSlxxbDO::getTbbz, dto.getTbbz(), Objects.nonNull(dto.getTbbz()));
        query.eq(ZjywSlxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjywSlxxbDO::getNbsfzid, dto.getNbsfzid(), Objects.nonNull(dto.getNbsfzid()));
        query.eq(ZjywSlxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjywSlxxbDO::getXb, dto.getXb(), Objects.nonNull(dto.getXb()));
        query.eq(ZjywSlxxbDO::getMz, dto.getMz(), Objects.nonNull(dto.getMz()));
        query.ge(ZjywSlxxbDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(ZjywSlxxbDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.likeLeft(ZjywSlxxbDO::getCsdssxq, dto.getCsdssxq(), StringUtils.isNotEmpty(dto.getCsdssxq()));
        query.eq(ZjywSlxxbDO::getMlpnbid, dto.getMlpnbid(), Objects.nonNull(dto.getMlpnbid()));
        query.likeLeft(ZjywSlxxbDO::getSsxq, dto.getSsxq(), StringUtils.isNotEmpty(dto.getSsxq()));
        query.eq(ZjywSlxxbDO::getJlx, dto.getJlx(), Objects.nonNull(dto.getJlx()));
        query.eq(ZjywSlxxbDO::getMlph, dto.getMlph(), Objects.nonNull(dto.getMlph()));
        query.eq(ZjywSlxxbDO::getMlxz, dto.getMlxz(), Objects.nonNull(dto.getMlxz()));
        query.eq(ZjywSlxxbDO::getPcs, dto.getPcs(), Objects.nonNull(dto.getPcs()));
        query.eq(ZjywSlxxbDO::getZrq, dto.getZrq(), Objects.nonNull(dto.getZrq()));
        query.eq(ZjywSlxxbDO::getXzjd, dto.getXzjd(), Objects.nonNull(dto.getXzjd()));
        query.eq(ZjywSlxxbDO::getJcwh, dto.getJcwh(), Objects.nonNull(dto.getJcwh()));
        query.eq(ZjywSlxxbDO::getPxh, dto.getPxh(), Objects.nonNull(dto.getPxh()));
        query.eq(ZjywSlxxbDO::getYwbz, dto.getYwbz(), Objects.nonNull(dto.getYwbz()));
        query.eq(ZjywSlxxbDO::getCzyid, dto.getCzyid(), Objects.nonNull(dto.getCzyid()));
        query.ge(ZjywSlxxbDO::getCzsj, dto.getCzsjStart(), StringUtils.isNotEmpty(dto.getCzsjStart()));
        query.le(ZjywSlxxbDO::getCzsj, dto.getCzsjEnd(), StringUtils.isNotEmpty(dto.getCzsjEnd()));
        query.eq(ZjywSlxxbDO::getDwdm, dto.getDwdm(), Objects.nonNull(dto.getDwdm()));
        query.eq(ZjywSlxxbDO::getSjrxm, ColumnUtils.encryptColumn(dto.getSjrxm()), StringUtils.isNotEmpty(dto.getSjrxm()));
        query.eq(ZjywSlxxbDO::getSjrlxdh, ColumnUtils.encryptColumn(dto.getSjrlxdh()), StringUtils.isNotEmpty(dto.getSjrlxdh()));
        query.eq(ZjywSlxxbDO::getSjryb, dto.getSjryb(), Objects.nonNull(dto.getSjryb()));
        query.eq(ZjywSlxxbDO::getSjrtxdz, dto.getSjrtxdz(), Objects.nonNull(dto.getSjrtxdz()));
        query.eq(ZjywSlxxbDO::getZzxxcwlb, dto.getZzxxcwlb(), Objects.nonNull(dto.getZzxxcwlb()));
        query.eq(ZjywSlxxbDO::getCwms, dto.getCwms(), Objects.nonNull(dto.getCwms()));
        query.eq(ZjywSlxxbDO::getJydw, dto.getJydw(), Objects.nonNull(dto.getJydw()));
        query.eq(ZjywSlxxbDO::getJyrxm, ColumnUtils.encryptColumn(dto.getJyrxm()), StringUtils.isNotEmpty(dto.getJyrxm()));
        query.ge(ZjywSlxxbDO::getJyrq, dto.getJyrqStart(), StringUtils.isNotEmpty(dto.getJyrqStart()));
        query.le(ZjywSlxxbDO::getJyrq, dto.getJyrqEnd(), StringUtils.isNotEmpty(dto.getJyrqEnd()));
        query.eq(ZjywSlxxbDO::getCldw, dto.getCldw(), Objects.nonNull(dto.getCldw()));
        query.eq(ZjywSlxxbDO::getClqk, dto.getClqk(), Objects.nonNull(dto.getClqk()));
        query.ge(ZjywSlxxbDO::getClrq, dto.getClrqStart(), StringUtils.isNotEmpty(dto.getClrqStart()));
        query.le(ZjywSlxxbDO::getClrq, dto.getClrqEnd(), StringUtils.isNotEmpty(dto.getClrqEnd()));
        query.eq(ZjywSlxxbDO::getZlhkzt, dto.getZlhkzt(), Objects.nonNull(dto.getZlhkzt()));
        query.ge(ZjywSlxxbDO::getHksj, dto.getHksjStart(), StringUtils.isNotEmpty(dto.getHksjStart()));
        query.le(ZjywSlxxbDO::getHksj, dto.getHksjEnd(), StringUtils.isNotEmpty(dto.getHksjEnd()));
        query.eq(ZjywSlxxbDO::getBwbha, dto.getBwbha(), Objects.nonNull(dto.getBwbha()));
        query.eq(ZjywSlxxbDO::getBwbhb, dto.getBwbhb(), Objects.nonNull(dto.getBwbhb()));
        query.ge(ZjywSlxxbDO::getShrq, dto.getShrqStart(), StringUtils.isNotEmpty(dto.getShrqStart()));
        query.le(ZjywSlxxbDO::getShrq, dto.getShrqEnd(), StringUtils.isNotEmpty(dto.getShrqEnd()));
        query.ge(ZjywSlxxbDO::getStjssj, dto.getStjssjStart(), StringUtils.isNotEmpty(dto.getStjssjStart()));
        query.le(ZjywSlxxbDO::getStjssj, dto.getStjssjEnd(), StringUtils.isNotEmpty(dto.getStjssjEnd()));
        query.eq(ZjywSlxxbDO::getBwbhd, dto.getBwbhd(), Objects.nonNull(dto.getBwbhd()));
        query.eq(ZjywSlxxbDO::getFjpch, dto.getFjpch(), Objects.nonNull(dto.getFjpch()));
        query.eq(ZjywSlxxbDO::getRlbdid, dto.getRlbdid(), Objects.nonNull(dto.getRlbdid()));
        query.eq(ZjywSlxxbDO::getRlbdbz, dto.getRlbdbz(), Objects.nonNull(dto.getRlbdbz()));
        query.ge(ZjywSlxxbDO::getRlbdsj, dto.getRlbdsjStart(), StringUtils.isNotEmpty(dto.getRlbdsjStart()));
        query.le(ZjywSlxxbDO::getRlbdsj, dto.getRlbdsjEnd(), StringUtils.isNotEmpty(dto.getRlbdsjEnd()));
        query.eq(ZjywSlxxbDO::getZwyzw, dto.getZwyzw(), Objects.nonNull(dto.getZwyzw()));
        query.eq(ZjywSlxxbDO::getZwyzcjg, dto.getZwyzcjg(), Objects.nonNull(dto.getZwyzcjg()));
        query.eq(ZjywSlxxbDO::getZwezw, dto.getZwezw(), Objects.nonNull(dto.getZwezw()));
        query.eq(ZjywSlxxbDO::getZwezcjg, dto.getZwezcjg(), Objects.nonNull(dto.getZwezcjg()));
        query.eq(ZjywSlxxbDO::getZwcjjgdm, dto.getZwcjjgdm(), Objects.nonNull(dto.getZwcjjgdm()));
        query.eq(ZjywSlxxbDO::getSzyczkdm, dto.getSzyczkdm(), Objects.nonNull(dto.getSzyczkdm()));
        query.eq(ZjywSlxxbDO::getSfzwzj, dto.getSfzwzj(), Objects.nonNull(dto.getSfzwzj()));
        query.eq(ZjywSlxxbDO::getDztbbz, dto.getDztbbz(), Objects.nonNull(dto.getDztbbz()));
        query.ge(ZjywSlxxbDO::getDztbsj, dto.getDztbsjStart(), StringUtils.isNotEmpty(dto.getDztbsjStart()));
        query.le(ZjywSlxxbDO::getDztbsj, dto.getDztbsjEnd(), StringUtils.isNotEmpty(dto.getDztbsjEnd()));
        query.eq(ZjywSlxxbDO::getDzsjbbh, dto.getDzsjbbh(), Objects.nonNull(dto.getDzsjbbh()));
        query.eq(ZjywSlxxbDO::getSlfs, dto.getSlfs(), Objects.nonNull(dto.getSlfs()));
        return query;
    }

    @Override
    public ZjywSlxxbDTO convertToDTO(ZjywSlxxbDO zjywSlxxbDO) {
        return convert.convert(zjywSlxxbDO);
    }

    @Override
    public ZjywSlxxbDO convertToDO(ZjywSlxxbDTO zjywSlxxbDTO) {
        return convert.convertToDO(zjywSlxxbDTO);
    }

    @Override
    public long countGmsfhmSfyzj(String gmsfhm) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ZjywSlxxbDO::getGmsfhm, gmsfhm)
                .lt(ZjywSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_YCB)
                .ne(ZjywSlxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF);
        return count(queryWrapper);
    }
}
