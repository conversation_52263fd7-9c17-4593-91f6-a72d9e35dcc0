package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtJmsfzxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtJmsfzxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtJmsfzxxbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtJmsfzxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtJmsfzxxbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 居民身份证信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Service
public class ZjtJmsfzxxbServiceImpl extends AbstractBaseServiceImpl<ZjtJmsfzxxbMapper, ZjtJmsfzxxbDO, ZjtJmsfzxxbDTO> implements ZjtJmsfzxxbService {

    ZjtJmsfzxxbConvert convert = ZjtJmsfzxxbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtJmsfzxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtJmsfzxxbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtJmsfzxxbDO::getZpid, dto.getZpid(), StringUtils.isNotEmpty(dto.getZpid()));
        query.eq(ZjtJmsfzxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtJmsfzxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtJmsfzxxbDO::getXb, dto.getXb(), StringUtils.isNotEmpty(dto.getXb()));
        query.eq(ZjtJmsfzxxbDO::getMz, dto.getMz(), StringUtils.isNotEmpty(dto.getMz()));
        query.ge(ZjtJmsfzxxbDO::getCsrq, dto.getCsrqStart(), StringUtils.isNotEmpty(dto.getCsrqStart()));
        query.le(ZjtJmsfzxxbDO::getCsrq, dto.getCsrqEnd(), StringUtils.isNotEmpty(dto.getCsrqEnd()));
        query.eq(ZjtJmsfzxxbDO::getQfjg, dto.getQfjg(), StringUtils.isNotEmpty(dto.getQfjg()));
        query.ge(ZjtJmsfzxxbDO::getYxqxqsrq, dto.getYxqxqsrqStart(), StringUtils.isNotEmpty(dto.getYxqxqsrqStart()));
        query.le(ZjtJmsfzxxbDO::getYxqxqsrq, dto.getYxqxqsrqEnd(), StringUtils.isNotEmpty(dto.getYxqxqsrqEnd()));
        query.ge(ZjtJmsfzxxbDO::getYxqxjzrq, dto.getYxqxjzrqStart(), StringUtils.isNotEmpty(dto.getYxqxjzrqStart()));
        query.le(ZjtJmsfzxxbDO::getYxqxjzrq, dto.getYxqxjzrqEnd(), StringUtils.isNotEmpty(dto.getYxqxjzrqEnd()));
        query.eq(ZjtJmsfzxxbDO::getSlyy, dto.getSlyy(), StringUtils.isNotEmpty(dto.getSlyy()));
        query.eq(ZjtJmsfzxxbDO::getBzyy, dto.getBzyy(), StringUtils.isNotEmpty(dto.getBzyy()));
        query.eq(ZjtJmsfzxxbDO::getSjyy, dto.getSjyy(), StringUtils.isNotEmpty(dto.getSjyy()));
        query.eq(ZjtJmsfzxxbDO::getLqfs, dto.getLqfs(), StringUtils.isNotEmpty(dto.getLqfs()));
        query.eq(ZjtJmsfzxxbDO::getZz, dto.getZz(), StringUtils.isNotEmpty(dto.getZz()));
        query.eq(ZjtJmsfzxxbDO::getZjlb, dto.getZjlb(), StringUtils.isNotEmpty(dto.getZjlb()));
        query.eq(ZjtJmsfzxxbDO::getZjzt, dto.getZjzt(), StringUtils.isNotEmpty(dto.getZjzt()));
        query.eq(ZjtJmsfzxxbDO::getTbbz, dto.getTbbz(), Objects.nonNull(dto.getTbbz()));
        query.eq(ZjtJmsfzxxbDO::getBwbh, dto.getBwbh(), StringUtils.isNotEmpty(dto.getBwbh()));
        query.eq(ZjtJmsfzxxbDO::getSlh, dto.getSlh(), StringUtils.isNotEmpty(dto.getSlh()));
        query.eq(ZjtJmsfzxxbDO::getKtglh, dto.getKtglh(), StringUtils.isNotEmpty(dto.getKtglh()));
        query.ge(ZjtJmsfzxxbDO::getZjsj, dto.getZjsjStart(), StringUtils.isNotEmpty(dto.getZjsjStart()));
        query.le(ZjtJmsfzxxbDO::getZjsj, dto.getZjsjEnd(), StringUtils.isNotEmpty(dto.getZjsjEnd()));
        query.eq(ZjtJmsfzxxbDO::getZjlx, dto.getZjlx(), StringUtils.isNotEmpty(dto.getZjlx()));
        query.eq(ZjtJmsfzxxbDO::getZwyzwtzsj, dto.getZwyzwtzsj(), Objects.nonNull(dto.getZwyzwtzsj()));
        query.eq(ZjtJmsfzxxbDO::getZwezwtzsj, dto.getZwezwtzsj(), Objects.nonNull(dto.getZwezwtzsj()));
        query.eq(ZjtJmsfzxxbDO::getCzrkjdzpwjbh, dto.getCzrkjdzpwjbh(), StringUtils.isNotEmpty(dto.getCzrkjdzpwjbh()));
        query.eq(ZjtJmsfzxxbDO::getZwyzw, dto.getZwyzw(), StringUtils.isNotEmpty(dto.getZwyzw()));
        query.eq(ZjtJmsfzxxbDO::getZwyzcjg, dto.getZwyzcjg(), StringUtils.isNotEmpty(dto.getZwyzcjg()));
        query.eq(ZjtJmsfzxxbDO::getZwezw, dto.getZwezw(), StringUtils.isNotEmpty(dto.getZwezw()));
        query.eq(ZjtJmsfzxxbDO::getZwezcjg, dto.getZwezcjg(), StringUtils.isNotEmpty(dto.getZwezcjg()));
        query.eq(ZjtJmsfzxxbDO::getZwcjjgdm, dto.getZwcjjgdm(), StringUtils.isNotEmpty(dto.getZwcjjgdm()));
        query.eq(ZjtJmsfzxxbDO::getSzyczkdm, dto.getSzyczkdm(), StringUtils.isNotEmpty(dto.getSzyczkdm()));
        query.eq(ZjtJmsfzxxbDO::getZjlylb, dto.getZjlylb(), StringUtils.isNotEmpty(dto.getZjlylb()));
        query.ge(ZjtJmsfzxxbDO::getZjlysj, dto.getZjlysjStart(), StringUtils.isNotEmpty(dto.getZjlysjStart()));
        query.le(ZjtJmsfzxxbDO::getZjlysj, dto.getZjlysjEnd(), StringUtils.isNotEmpty(dto.getZjlysjEnd()));
        query.eq(ZjtJmsfzxxbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtJmsfzxxbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtJmsfzxxbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtJmsfzxxbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.likeLeft(ZjtJmsfzxxbDO::getZjzdssxq, dto.getZjzdssxq(), StringUtils.isNotEmpty(dto.getZjzdssxq()));
        query.eq(ZjtJmsfzxxbDO::getZjzdxz, dto.getZjzdxz(), StringUtils.isNotEmpty(dto.getZjzdxz()));
        query.eq(ZjtJmsfzxxbDO::getLxdh, ColumnUtils.encryptColumn(dto.getLxdh()), StringUtils.isNotEmpty(dto.getLxdh()));
        return query;
    }

    @Override
    public ZjtJmsfzxxbDTO convertToDTO(ZjtJmsfzxxbDO zjtJmsfzxxbDO) {
        return convert.convert(zjtJmsfzxxbDO);
    }

    @Override
    public ZjtJmsfzxxbDO convertToDO(ZjtJmsfzxxbDTO zjtJmsfzxxbDTO) {
        return convert.convertToDO(zjtJmsfzxxbDTO);
    }
}
