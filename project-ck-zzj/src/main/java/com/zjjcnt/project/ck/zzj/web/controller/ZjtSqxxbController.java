package com.zjjcnt.project.ck.zzj.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtSqxxbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSqxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.req.ZjtSqxxbCreateReq;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSqxxbCreateResp;
import com.zjjcnt.project.ck.zzj.dto.ck.resp.ZjtSqxxbViewResp;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtSqxxbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
* 证件业务申请信息表前端控制器
*
* <AUTHOR>
* @date 2024-05-09 11:08:52
*/

@Tag(name = "证件业务申请信息表")
@RestController
@RequestMapping("/zjtSqxxb")
public class ZjtSqxxbController extends AbstractCrudController<ZjtSqxxbDTO> {

    @Autowired
    private ZjtSqxxbService zjtSqxxbService;

    @Override
    protected IBaseService getService() {
        return zjtSqxxbService;
    }

//    @GetMapping("page")
//    @Operation(summary = "查询证件业务申请信息表")
//    public CommonResult<PageResult<ZjtSqxxbPageResp>> page(ZjtSqxxbPageReq req, PageParam pageParam) {
//        return super.page(req, pageParam, ZjtSqxxbConvert.INSTANCE::convertToDTO, ZjtSqxxbConvert.INSTANCE::convertToPageResp);
//    }

    @GetMapping("view")
    @Operation(summary = "查看证件业务申请信息表详情 N23002")
    public CommonResult<ZjtSqxxbViewResp> view(String id) {
        return super.view(id, ZjtSqxxbConvert.INSTANCE::convertToViewResp);
    }

    @PostMapping("create")
    @Operation(summary = "新增证件业务申请信息表 F23931")
    public CommonResult<ZjtSqxxbCreateResp> create(@RequestBody ZjtSqxxbCreateReq req) {
        return super.create(req, ZjtSqxxbConvert.INSTANCE::convertToDTO, ZjtSqxxbConvert.INSTANCE::convertToCreateResp);
    }

//    @PostMapping("update")
//    @Operation(summary = "编辑证件业务申请信息表")
//    public CommonResult<Boolean> update(@RequestBody ZjtSqxxbUpdateReq req) {
//        return super.update(req, ZjtSqxxbConvert.INSTANCE::convertToDTO);
//    }
//
//    @DeleteMapping(value = "delete")
//    @Operation(summary = "删除证件业务申请信息表")
//    public CommonResult<Boolean> delete(@RequestBody String[] id) {
//        return super.delete(id);
//    }
//
//    @GetMapping(value = "/column")
//    @Operation(summary = "查询可导出列")
//    public CommonResult<List<ExportColumn>> column() {
//        List<ExportColumn> exportColumns = getColumns(ZjtSqxxbExp.class);
//        return CommonResult.success(exportColumns);
//    }
//
//    @GetMapping("export")
//    @Operation(summary = "导出证件业务申请信息表")
//    public void export(ZjtSqxxbPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
//        String filename = "证件业务申请信息表" + DateTimeUtils.now() + ".xlsx";
//        super.export(req, columns, pageParam, filename, ZjtSqxxbExp.class,
//                    ZjtSqxxbConvert.INSTANCE::convertToDTO, ZjtSqxxbConvert.INSTANCE::convertToExp, response);
//    }

}
