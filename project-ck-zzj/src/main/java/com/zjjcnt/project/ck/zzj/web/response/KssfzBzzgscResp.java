package com.zjjcnt.project.ck.zzj.web.response;

import com.zjjcnt.common.core.annotation.StringDateFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 办证资格审查resp
 *
 * <AUTHOR>
 * @date 2024-10-14 11:31:00
 */
@Data
public class KssfzBzzgscResp {

    @Schema(description = "办证资格判断标志 0不允许 1允许")
    private String bzzgpdbz;

    @Schema(description = "办证资格判断描述")
    private String bzzgpdms;

    @Schema(description = "户籍地省市县区")
    private String hjdssxq;

    @Schema(description = "性别代码")
    private String xbdm;

    @Schema(description = "民族代码")
    private String mzdm;

    @Schema(description = "户籍地详址")
    private String hjdxz;

    @Schema(description = "人员相片")
    private String ryxp;

    @StringDateFormat
    @Schema(description = "持有证件有效期开始时间")
    private String cyzjyxqkssj;

    @StringDateFormat
    @Schema(description = "持有证件有效期结束时间")
    private String cyzjyxqjssj;

    @Schema(description = "持有证件相片")
    private String cyzjxp;

    @Schema(description = "持有证件指纹一指位代码")
    private String cyzjzwyzwdm;

    @Schema(description = "持有证件指纹一特征数据")
    private String cyzjzwytzsj;

    @Schema(description = "持有证件指纹二指位代码")
    private String cyzjzwezwdm;

    @Schema(description = "持有证件指纹二特征数据")
    private String cyzjzwetzsj;

    @Schema(description = "办证照片")
    private String bzzp;

    @Schema(description = "办证照片id")
    private String bzzpid;
}
