package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.DwUtils;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.constant.CkConstants;
import com.zjjcnt.project.ck.sysadmin.constant.ZjConstant;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtSqxxbConvert;
import com.zjjcnt.project.ck.sysadmin.dto.GmsfhmAndXmEntity;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtRxxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtSqxxbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtSqxxbDO;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.sysadmin.manager.ZjSlhGenerator;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtSqxxbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtRxxxbService;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtSqxxbService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 证件业务申请信息表ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 */
@Service
public class ZjtSqxxbServiceImpl extends AbstractBaseServiceImpl<ZjtSqxxbMapper, ZjtSqxxbDO, ZjtSqxxbDTO> implements ZjtSqxxbService {

    ZjtSqxxbConvert convert = ZjtSqxxbConvert.INSTANCE;

    @Autowired
    private ZjSlhGenerator zjSlhGenerator;

    @Autowired
    private ZjtRxxxbService zjtRxxxbService;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtSqxxbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtSqxxbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtSqxxbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtSqxxbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtSqxxbDO::getSqrgmsfhm, ColumnUtils.encryptColumn(dto.getSqrgmsfhm()), StringUtils.isNotEmpty(dto.getSqrgmsfhm()));
        query.eq(ZjtSqxxbDO::getSqrxm, ColumnUtils.encryptColumn(dto.getSqrxm()), StringUtils.isNotEmpty(dto.getSqrxm()));
        query.eq(ZjtSqxxbDO::getSqrlxdh, ColumnUtils.encryptColumn(dto.getSqrlxdh()), StringUtils.isNotEmpty(dto.getSqrlxdh()));
        query.ge(ZjtSqxxbDO::getSqrq, dto.getSqrqStart(), StringUtils.isNotEmpty(dto.getSqrqStart()));
        query.le(ZjtSqxxbDO::getSqrq, dto.getSqrqEnd(), StringUtils.isNotEmpty(dto.getSqrqEnd()));
        query.eq(ZjtSqxxbDO::getSqrzpid, dto.getSqrzpid(), StringUtils.isNotEmpty(dto.getSqrzpid()));
        query.eq(ZjtSqxxbDO::getSlzt, dto.getSlzt(), StringUtils.isNotEmpty(dto.getSlzt()));
        query.eq(ZjtSqxxbDO::getSflx, dto.getSflx(), StringUtils.isNotEmpty(dto.getSflx()));
        query.eq(ZjtSqxxbDO::getSfje, dto.getSfje(), Objects.nonNull(dto.getSfje()));
        query.eq(ZjtSqxxbDO::getSldwgajgjgdm, dto.getSldwgajgjgdm(), StringUtils.isNotEmpty(dto.getSldwgajgjgdm()));
        query.eq(ZjtSqxxbDO::getSldwgajgmc, dto.getSldwgajgmc(), StringUtils.isNotEmpty(dto.getSldwgajgmc()));
        query.eq(ZjtSqxxbDO::getSlrxm, ColumnUtils.encryptColumn(dto.getSlrxm()), StringUtils.isNotEmpty(dto.getSlrxm()));
        query.ge(ZjtSqxxbDO::getSlsj, dto.getSlsjStart(), StringUtils.isNotEmpty(dto.getSlsjStart()));
        query.le(ZjtSqxxbDO::getSlsj, dto.getSlsjEnd(), StringUtils.isNotEmpty(dto.getSlsjEnd()));
        query.eq(ZjtSqxxbDO::getJcwh, dto.getJcwh(), StringUtils.isNotEmpty(dto.getJcwh()));
        query.eq(ZjtSqxxbDO::getBz, dto.getBz(), StringUtils.isNotEmpty(dto.getBz()));
        query.eq(ZjtSqxxbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtSqxxbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtSqxxbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtSqxxbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        query.eq(ZjtSqxxbDO::getSldfjsjgsdwdm, dto.getSldfjsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwdm()));
        query.eq(ZjtSqxxbDO::getSldfjsjgsdwmc, dto.getSldfjsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldfjsjgsdwmc()));
        query.eq(ZjtSqxxbDO::getSfdjh, dto.getSfdjh(), StringUtils.isNotEmpty(dto.getSfdjh()));
        query.eq(ZjtSqxxbDO::getBysllx, dto.getBysllx(), StringUtils.isNotEmpty(dto.getBysllx()));
        query.eq(ZjtSqxxbDO::getByslsm, dto.getByslsm(), StringUtils.isNotEmpty(dto.getByslsm()));
        query.eq(ZjtSqxxbDO::getZzlx, dto.getZzlx(), StringUtils.isNotEmpty(dto.getZzlx()));
        query.eq(ZjtSqxxbDO::getFwdx, dto.getFwdx(), StringUtils.isNotEmpty(dto.getFwdx()));
        query.eq(ZjtSqxxbDO::getHlwsqid, dto.getHlwsqid(), StringUtils.isNotEmpty(dto.getHlwsqid()));
        query.eq(ZjtSqxxbDO::getUsername, dto.getUsername(), StringUtils.isNotEmpty(dto.getUsername()));
        query.ge(ZjtSqxxbDO::getSqrrxbdsj, dto.getSqrrxbdsjStart(), StringUtils.isNotEmpty(dto.getSqrrxbdsjStart()));
        query.le(ZjtSqxxbDO::getSqrrxbdsj, dto.getSqrrxbdsjEnd(), StringUtils.isNotEmpty(dto.getSqrrxbdsjEnd()));
        query.eq(ZjtSqxxbDO::getSqrrxbdxsd, dto.getSqrrxbdxsd(), StringUtils.isNotEmpty(dto.getSqrrxbdxsd()));
        query.eq(ZjtSqxxbDO::getSqrrxbdjg, dto.getSqrrxbdjg(), StringUtils.isNotEmpty(dto.getSqrrxbdjg()));
        return query;
    }

    @Override
    public ZjtSqxxbDTO convertToDTO(ZjtSqxxbDO zjtSqxxbDO) {
        return convert.convert(zjtSqxxbDO);
    }

    @Override
    public ZjtSqxxbDO convertToDO(ZjtSqxxbDTO zjtSqxxbDTO) {
        return convert.convertToDO(zjtSqxxbDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtSqxxbDTO insert(ZjtSqxxbDTO entity) throws ServiceException {
        validate(entity);

        // 作废前一条初步审查通过的申请信息.
        ZjtSqxxbDTO query = new ZjtSqxxbDTO();
        query.setSlzt(ZjConstant.ZJ_BLBZ_2ID_CBSCTG);
        query.setRyid(entity.getRyid());
        List<ZjtSqxxbDTO> prevCbsctgSqxxList = list(query);

        if (CollectionUtils.isNotEmpty(prevCbsctgSqxxList)) {
            ZjtSqxxbDTO prevSqxxb = prevCbsctgSqxxList.get(0);
            prevSqxxb.setSlzt(ZjConstant.ZJ_BLBZ_2ID_ZF);
            update(prevSqxxb);
        }

        // 判断受理人是否有正常证件业务受理中.
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ZjtSqxxbDO::getRyid, entity.getRyid());
        queryWrapper.notIn(ZjtSqxxbDO::getSlzt, ZjConstant.ZJ_BLBZ_2ID_ZF, ZjConstant.ZJ_BLBZ_2ID_YLZ,
                ZjConstant.ZJ_BLBZ_2ID_YGS, ZjConstant.ZJ_BLBZ_2ID_YSJ,
                ZjConstant.ZJ_BLBZ_2ID_YXH, ZjConstant.ZJ_BLBZ_2ID_YCB,
                ZjConstant.ZJ_BLBZ_2ID_ZFGD, ZjConstant.ZJ_BLBZ_2ID_BYSL);

        if (exists(queryWrapper)) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON,
                    "该人资格审查已通过或者已经存在正在受理的证件业务，无法重复保存证件申请信息。");
        }

        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();

        String ywslh = zjSlhGenerator.generateNewYwslh(entity.getSldsjgsdwdm());
        entity.setYwslh(ywslh);
        entity.setSqrq(ServerTimeUtils.getCurrentDate());
        entity.setSlsj(ServerTimeUtils.getCurrentTime());
        entity.setSlrxm(currentUser.getName());

        entity.setSldwgajgjgdm(DwUtils.expandSjgsdw(currentUser.getDeptCode()));
        entity.setSldwgajgmc(currentUser.getDeptName());

        ZjtRxxxbDTO insert = saveSqrzp(entity);
        if (insert != null) {
            entity.setSqrzpid(insert.getZpid());
        }
        return super.insert(entity);
    }


    /**
     * 受理表同步更新申请表
     * 1、同步更新受理状态
     */
    public ZjtSqxxbDTO syncZjtSqxxbFromZjtSlxxb(ZjtSlxxbDTO zjtSlxxb) {
        if (StringUtils.isEmpty(zjtSlxxb.getYwslh())) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "同步受理信息到申请表时，业务申请号为空");
        }
        ZjtSqxxbDTO zjtSqxxbFind = findById(zjtSlxxb.getYwslh());
        if (zjtSqxxbFind == null) {
            throw new ServiceException(CkZzjErrorCode.ERR_COMMON, "同步受理信息到申请表时，无法找到申请表数据，业务申请号[" + zjtSlxxb.getYwslh() + "]");
        }
        //同步受理状态
        zjtSqxxbFind.setSlzt(zjtSlxxb.getSlzt());
        zjtSqxxbFind.setSflx(zjtSlxxb.getSflx());
        zjtSqxxbFind.setSqrlxdh(zjtSlxxb.getSqrlxdh());//申请人联系电话已ZJT_SLXXB为准
        this.update(zjtSqxxbFind);
        return zjtSqxxbFind;
    }

    private ZjtRxxxbDTO saveSqrzp(ZjtSqxxbDTO entity) {
        // 保存申请人照片
        if (StringUtils.isNotBlank(entity.getBase64zp())) {
            ZjtRxxxbDTO zjtRxxxb = new ZjtRxxxbDTO();
            zjtRxxxb.setBase64zp(entity.getBase64zp());
            zjtRxxxb.setXm(entity.getSqrxm());
            zjtRxxxb.setGmsfhm(entity.getSqrgmsfhm());
            // 由于申请表没有申请人的ryid字段而ZjtRxxxb.ryid为非空字段, 所以这里的ryid天申请人的身份证号
            zjtRxxxb.setRyid(entity.getSqrgmsfhm());
            zjtRxxxb.setXplx(ZjConstant.ZJ_XPLX_QT);
            return zjtRxxxbService.insert(zjtRxxxb);
        }
        return null;
    }

    private void validate(ZjtSqxxbDTO entity) {
        List<String> messageList = Lists.newArrayList();

        if (!(ZjConstant.ZJ_BLBZ_2ID_CBSCTG.equals(entity.getSlzt())
                || ZjConstant.ZJ_BLBZ_2ID_BYSL.equals(entity.getSlzt()))) {
            messageList.add("受理状态必须为“初步审查通过”或“不予受理”");
        }
        if (StringUtils.isBlank(entity.getRyid())) {
            messageList.add("ryid        字段不能为空");
        }
        if (StringUtils.isBlank(entity.getGmsfhm())) {
            messageList.add("gmsfhm      字段不能为空");
        }
        if (StringUtils.isBlank(entity.getXm())) {
            messageList.add("xm          字段不能为空");
        }
        if (StringUtils.isBlank(entity.getSqrgmsfhm())) {
            messageList.add("sqrgmsfhm   字段不能为空");
        }
        if (StringUtils.isBlank(entity.getSqrxm())) {
            messageList.add("sqrxm       字段不能为空");
        }
        if (StringUtils.length(entity.getHjdsjgsdwdm()) != 12) {
            messageList.add("hjdsjgsdwdm 字段长度必须为12位");
        }
        if (StringUtils.length(entity.getSldsjgsdwdm()) != 12) {
            messageList.add("sldsjgsdwdm 字段长度必须为12位");
        }

        if (!messageList.isEmpty()) {
            StringBuilder message = new StringBuilder("新增证件业务申请信息时：");
            for (String m : messageList) {
                message.append("\r\n").append(m).append("；");
            }
            throw new ServiceException(500, message.toString());
        }
    }

    @Override
    public List<? extends GmsfhmAndXmEntity> findValidByYwslh(String ywslh) {
        ZjtSqxxbDTO zjtSqxxbDTO = findById(ywslh);
        if (Objects.isNull(zjtSqxxbDTO)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(zjtSqxxbDTO);
    }

    @Override
    public String[] getLcywlx() {
        return new String[]{CkConstants.LCYWLX_SFZ, CkConstants.LCYWLX_SFZ_YD};
    }
}
