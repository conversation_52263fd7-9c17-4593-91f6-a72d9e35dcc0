package com.zjjcnt.project.ck.zzj.service.ck.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.core.exception.UserErrorCode;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import com.zjjcnt.project.ck.zzj.convert.ck.ZjtZwcjlsbConvert;
import com.zjjcnt.project.ck.zzj.dto.ck.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.zzj.entity.ck.ZjtZwcjlsbDO;
import com.zjjcnt.project.ck.zzj.mapper.ck.ZjtZwcjlsbMapper;
import com.zjjcnt.project.ck.zzj.service.ck.ZjtZwcjlsbService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-09 11:39:12
 */
@Service
public class ZjtZwcjlsbServiceImpl extends ExBaseServiceImpl<ZjtZwcjlsbMapper, ZjtZwcjlsbDO, ZjtZwcjlsbDTO> implements ZjtZwcjlsbService {

    ZjtZwcjlsbConvert convert = ZjtZwcjlsbConvert.INSTANCE;

    @Autowired
    private ZjtZwcjsbxxbServiceImpl zjtZwcjsbxxbService;

    @Override
    protected QueryWrapper genQueryWrapper(ZjtZwcjlsbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtZwcjlsbDO::getRyid, dto.getRyid(), StringUtils.isNotEmpty(dto.getRyid()));
        query.eq(ZjtZwcjlsbDO::getGmsfhm, ColumnUtils.encryptColumn(dto.getGmsfhm()), StringUtils.isNotEmpty(dto.getGmsfhm()));
        query.eq(ZjtZwcjlsbDO::getXm, ColumnUtils.encryptColumn(dto.getXm()), StringUtils.isNotEmpty(dto.getXm()));
        query.eq(ZjtZwcjlsbDO::getZwcjjgdm, dto.getZwcjjgdm(), StringUtils.isNotEmpty(dto.getZwcjjgdm()));
        query.eq(ZjtZwcjlsbDO::getSzyczkdm, dto.getSzyczkdm(), StringUtils.isNotEmpty(dto.getSzyczkdm()));
        query.eq(ZjtZwcjlsbDO::getZwyzcjg, dto.getZwyzcjg(), StringUtils.isNotEmpty(dto.getZwyzcjg()));
        query.eq(ZjtZwcjlsbDO::getZwyzw, dto.getZwyzw(), StringUtils.isNotEmpty(dto.getZwyzw()));
        query.eq(ZjtZwcjlsbDO::getZwytxsj, dto.getZwytxsj(), Objects.nonNull(dto.getZwytxsj()));
        query.eq(ZjtZwcjlsbDO::getZwytxzlz, dto.getZwytxzlz(), Objects.nonNull(dto.getZwytxzlz()));
        query.eq(ZjtZwcjlsbDO::getZwyzwtzsj, dto.getZwyzwtzsj(), Objects.nonNull(dto.getZwyzwtzsj()));
        query.eq(ZjtZwcjlsbDO::getZwezcjg, dto.getZwezcjg(), StringUtils.isNotEmpty(dto.getZwezcjg()));
        query.eq(ZjtZwcjlsbDO::getZwezw, dto.getZwezw(), StringUtils.isNotEmpty(dto.getZwezw()));
        query.eq(ZjtZwcjlsbDO::getZwetxsj, dto.getZwetxsj(), Objects.nonNull(dto.getZwetxsj()));
        query.eq(ZjtZwcjlsbDO::getZwetxzlz, dto.getZwetxzlz(), Objects.nonNull(dto.getZwetxzlz()));
        query.eq(ZjtZwcjlsbDO::getZwezwtzsj, dto.getZwezwtzsj(), Objects.nonNull(dto.getZwezwtzsj()));
        query.eq(ZjtZwcjlsbDO::getZwqdxtzch, dto.getZwqdxtzch(), StringUtils.isNotEmpty(dto.getZwqdxtzch()));
        query.eq(ZjtZwcjlsbDO::getZwcjqid, dto.getZwcjqid(), StringUtils.isNotEmpty(dto.getZwcjqid()));
        query.eq(ZjtZwcjlsbDO::getZwsfbbh, dto.getZwsfbbh(), StringUtils.isNotEmpty(dto.getZwsfbbh()));
        query.eq(ZjtZwcjlsbDO::getZwsfkfzdm, dto.getZwsfkfzdm(), StringUtils.isNotEmpty(dto.getZwsfkfzdm()));
        query.eq(ZjtZwcjlsbDO::getCzrid, dto.getCzrid(), StringUtils.isNotEmpty(dto.getCzrid()));
        query.ge(ZjtZwcjlsbDO::getBcsj, dto.getBcsjStart(), StringUtils.isNotEmpty(dto.getBcsjStart()));
        query.le(ZjtZwcjlsbDO::getBcsj, dto.getBcsjEnd(), StringUtils.isNotEmpty(dto.getBcsjEnd()));
        query.eq(ZjtZwcjlsbDO::getCzyxm, ColumnUtils.encryptColumn(dto.getCzyxm()), StringUtils.isNotEmpty(dto.getCzyxm()));
        query.eq(ZjtZwcjlsbDO::getCzydwdm, dto.getCzydwdm(), StringUtils.isNotEmpty(dto.getCzydwdm()));
        query.eq(ZjtZwcjlsbDO::getCzydwmc, dto.getCzydwmc(), StringUtils.isNotEmpty(dto.getCzydwmc()));
        query.eq(ZjtZwcjlsbDO::getHjdsjgsdwdm, dto.getHjdsjgsdwdm(), StringUtils.isNotEmpty(dto.getHjdsjgsdwdm()));
        query.eq(ZjtZwcjlsbDO::getHjdsjgsdwmc, dto.getHjdsjgsdwmc(), StringUtils.isNotEmpty(dto.getHjdsjgsdwmc()));
        query.eq(ZjtZwcjlsbDO::getSldsjgsdwdm, dto.getSldsjgsdwdm(), StringUtils.isNotEmpty(dto.getSldsjgsdwdm()));
        query.eq(ZjtZwcjlsbDO::getSldsjgsdwmc, dto.getSldsjgsdwmc(), StringUtils.isNotEmpty(dto.getSldsjgsdwmc()));
        return query;
    }

    @Override
    public ZjtZwcjlsbDTO convertToDTO(ZjtZwcjlsbDO zjtZwcjlsbDO) {
        return convert.convert(zjtZwcjlsbDO);
    }

    @Override
    public ZjtZwcjlsbDO convertToDO(ZjtZwcjlsbDTO zjtZwcjlsbDTO) {
        return convert.convertToDO(zjtZwcjlsbDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZjtZwcjlsbDTO insert(ZjtZwcjlsbDTO entity) throws ServiceException {
        // 20180402将指纹采集设备的验证切换到MYSQL库的接口中，原接口关闭
        zjtZwcjsbxxbService.validateZwcjqid(entity.getZwcjqid());
        // 插入之前，先根据sfzh+xm删除历史数据,原先sfzh用的like，修改为eq，观察
        deleteHistory(entity.getXm(), entity.getGmsfhm());
        return super.insert(entity);
    }

    @Override
    public ZjtZwcjlsbDTO findNewZwByGmsfhm(String gmsfhm){
        ZjtZwcjlsbDTO dto = new ZjtZwcjlsbDTO();
        dto.setGmsfhm(gmsfhm);
        QueryWrapper queryWrapper = genQueryWrapper(dto);
        queryWrapper.orderBy(ZjtZwcjlsbDO::getBcsj, false);
        queryWrapper.limit(1);
        return find(queryWrapper);
    }


    @Override
    protected void beforeInsert(ZjtZwcjlsbDTO entity) throws ServiceException {
        checkBeforeSave(entity);
        super.beforeInsert(entity);
        setInfoBeforeSave(entity);
    }

    @Override
    protected void beforeUpdate(ZjtZwcjlsbDTO entity) throws ServiceException {
        checkBeforeSave(entity);
        super.beforeUpdate(entity);
        setInfoBeforeSave(entity);
    }

    private void checkBeforeSave(ZjtZwcjlsbDTO entity) {
        if (StringUtils.isBlank(entity.getGmsfhm())) {
            throw new ServiceException(500, "指纹采集临时表记录时[公民身份号码]不能为空.");
        }
        if (StringUtils.isBlank(entity.getHjdsjgsdwdm()) || StringUtils.isBlank(entity.getHjdsjgsdwmc())) {
            throw new ServiceException(500, "指纹采集临时表记录时[户籍地数据归属单位代码及名称]不能为空.");
        }
    }
    private void setInfoBeforeSave(ZjtZwcjlsbDTO entity) {
        this.validateSldsjgsdwdm(entity.getSldsjgsdwdm());
        this.validateSldsjgsdwmc(entity.getSldsjgsdwmc());
        CustomUserDetails user = SecurityUtils.getCurrentUser();
        entity.setCzrid(String.valueOf(user.getUserId()));
        entity.setBcsj(ServerTimeUtils.getCurrentTime());
        entity.setCzyxm(user.getName());
        entity.setCzydwdm(user.getDeptCode());
        entity.setCzydwmc(user.getDeptName());
    }

    private void deleteHistory(String xm, String gmsfhm) {
        Validate.notBlank(xm);
        Validate.notBlank(gmsfhm);
        QueryWrapper query = QueryWrapper.create();
        query.eq(ZjtZwcjlsbDO::getXm, xm);
        query.eq(ZjtZwcjlsbDO::getGmsfhm, gmsfhm);
        getMapper().deleteByQuery(query);
    }

    /**
     * 受理地数据归属单位代码必须为12位且不能为空
     * 该校验是由于该代码由前端传入，而非根据登录信息
     * */
    private void validateSldsjgsdwdm(String sldsjgsdwdm){
        if(StringUtils.isBlank(sldsjgsdwdm)){
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR,
                    "受理地数据归属单位代码不能为空.");
        }
        if(sldsjgsdwdm.length() != 12 ){
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR,
                    "受理地数据归属单位代码必须为12位.");
        }
    }
    /**
     * 受理地数据归属单位名称不能为空
     * */
    private void validateSldsjgsdwmc(String sldsjgsdwmc){
        if(StringUtils.isBlank(sldsjgsdwmc)){
            throw new ServiceException(UserErrorCode.USER_REQUEST_PARAM_ERROR,
                    "受理地数据归属单位名称不能为空.");
        }
    }
}
