package com.zjjcnt.project.ck.zzj.entity.ck;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 人像信息表DO
 *
 * <AUTHOR>
 * @date 2024-05-09 11:08:52
 * @see com.zjjcnt.project.ck.zzj.dto.ck.ZjtRxxxbDTO
 */
@Crypto
@Data
@Table("zjt_rxxxb")
public class ZjtRxxxbDO implements IdEntity<String> {
    private static final long serialVersionUID = 9143794144978334174L;

    /**
     * 照片ID
     */
    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String zpid;

    /**
     * 人员ID
     */
    @Column(value = "ryid")
    private String ryid;

    /**
     * 公民身份号码
     */
    @Crypto
    @Column(value = "gmsfhm")
    private String gmsfhm;

    /**
     * 姓名
     */
    @Crypto
    @Column(value = "xm")
    private String xm;

    /**
     * 照片文件编号
     */
    @Column(value = "zpwjbh")
    private String zpwjbh;

    /**
     * 录入时间
     */
    @Column(value = "lrrq")
    private String lrrq;

    /**
     * 常用证件代码
     */
    @Column(value = "cyzjdm")
    private String cyzjdm;

    /**
     * 证件号码
     */
    @Column(value = "zjhm")
    private String zjhm;

    /**
     * 外文姓
     */
    @Column(value = "wwx")
    private String wwx;

    /**
     * 外文名
     */
    @Column(value = "wwm")
    private String wwm;

    /**
     * 相片类型
     */
    @Column(value = "xplx")
    private String xplx;

    /**
     * 业务类别
     */
    @Column(value = "ywlb")
    private String ywlb;

    /**
     * 业务流水号
     */
    @Column(value = "ywlsh")
    private String ywlsh;

    /**
     * 注销时间
     */
    @Column(value = "zxsj")
    private String zxsj;

    /**
     * 采集时间
     */
    @Column(value = "cjsj")
    private String cjsj;


    @Override
    public String getId() {
        return this.zpid;
    }

    @Override
    public void setId(String id) {
        this.zpid = id;
    }
}
