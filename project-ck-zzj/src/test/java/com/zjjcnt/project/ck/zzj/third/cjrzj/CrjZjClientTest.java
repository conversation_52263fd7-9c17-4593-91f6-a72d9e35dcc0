package com.zjjcnt.project.ck.zzj.third.cjrzj;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.third.yzty.resp.*;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO description
 *
 * <AUTHOR>
 * @date 2024-09-20 13:44:00
 */
class CrjZjClientTest {

    @Test
    public void aVoid3() {
        String jsonResult = "{\"msg\":\"成功\",\"code\":\"00\",\"data\":\"\",\"datas\":{\"root\":{\"request\":{\"header\":{\"resourceId\":null,\"records\":1,\"pageSum\":1,\"subject\":null,\"action\":\"query\",\"msgId\":\"98213e7a09a1401aae0482f12d6174dd\",\"pageSize\":1,\"resourceName\":null,\"subResourceId\":null,\"pageNum\":1,\"sendTime\":\"2021-02-26 14:22:49\"},\"body\":{\"dataList\":{\"data\":[{\"FILEURL\":\"d:/出生医学证明副页02.pdf\",\"CSYXZMBH\":\"U330039278\"}]}}}}},\"requestId\":\"844a7cbd2b3041bab883bd3553dc4639\",\"totalPage\":1,\"dataCount\":1,\"totalDataCount\":0}";
        System.out.println(jsonResult);
    }

    public void aVoid2() {
//        YztyResponse yztyResponse = new YztyResponse();
//        yztyResponse.setData("aaa");
//        yztyResponse.setMessage("message11");
//        yztyResponse.setData("message");
//        System.out.printf(JsonUtils.toJsonString(yztyResponse));
//        String result = "{\"status\":\"200\",\"message\":\"成功\",\"dataId\":\" \",\"data\":{\"status\":200,\"message\":\"请求成功\",\"data\":{\"code\":\"00000\",\"info\":\"服务处理成功\",\"sfhg\":\"0\",\"zjbhgjgms\":\"照片模糊，对焦不准\"},\"ywlsh\":\"qwertyuio123456789\"}}";
        String result = "{\"gmsfhm\":\"330105********1232\",\"xm\":\"张XX\",\"jgdm\":\"330104**\",\"jgdmmc\":\"杭州市公安局科技信息化**\",\"zdbs\":\"41.190.**\",\"requestId\":\"A-330***\",\"appName\":\"浙警**系统\",\"requestData\":{\"sqzdxx\":{\"sqssdm\":\"330101\",\"sqssmc\":\"浙江省杭州市上城区\",\"sqdwdm\":\"130*****0000\",\"sqdwmc\":\"XX省XX市公安局XX区分局XX派出所\",\"sqrxm\":\"李XX\",\"sqrzjhm\":\"130100****12280011\",\"sqrdh\":\"130****5678\"},\"zdxx\":{\"sbdwbh\":\"ZLPZ330***\",\"fwckbh\":\"ZLPZ330***\",\"fwckmc\":\"XX省XX市***拍摄点\",\"sbxh\":\"SY\",\"sbdwdm\":\"130*****0000\",\"sbdwmc\":\"XX省XX市公安局XX区分局XX派出所\",\"sbdwdz\":\"XX省XX市公安局XX区分局XX派出所照片采集室\",\"macdz\":\" 08-5B-D6-7A-92-02\",\"sfgddw\":\"1\",\"jd\":\"116.2317\",\"wd\":\"39.5427\",\"sfyrzs\":\"1\",\"cjzdlxdm\":\"00\",\"cjzdlxmc\":\"公安窗口\",\"dwfwcklbdm\":\"11\",\"dwfwcklbmc\":\"户籍(行政服务中心综合窗口)\",\"sfqy\":\"1\",\"qyrq\":\"20240808\",\"zcrq\":\"20240808\",\"dwbdsj\":\"2024-08-08 18：30：11\",\"czlx\":\"1\",\"bz1\":\"\",\"bz2\":\"\",\"bz3\":\"\"}}}";
        YztyResponse<YztyCjdwcxDataResponse> yztyResponse = JsonUtils.parseObject(result,
                new TypeReference<YztyResponse<YztyCjdwcxDataResponse>>() {});
//        YztyResponse yztyResponse = JsonUtils.parseObject(result, YztyResponse.class);
        System.out.printf(yztyResponse.toString());
//        YztyDataResponse yztyDataResp = JsonUtils.getAsObject("data", result, YztyDataResponse.class);
//        System.out.printf(yztyDataResp.toString());
    }


    @Test
    public void aVoid() {

        Map<String, String> map = new HashMap<>();
        map.put("gmsfhm", "330102199001011111");
        String s = BuildString("select * from z_taofan where SFZH = '[[gmsfhm]]' or SFZH = substr('[[gmsfhm]]', 1,6)||substr('[[gmsfhm]]', 9,9)", map);
        System.out.println(s);
    }

    public String BuildString(String psStr, Map mapParam) {
        int nKs = 0, nJs = 0;
        String sKey, sKeyData, sSql = psStr;
        if (sSql == null || "".equals(sSql)) {
            return sSql;
        }
        nKs = sSql.indexOf("[[");
        nJs = sSql.indexOf("]]");
        while (nKs >= 0) {
            sKey = sSql.substring(nKs + 2, nJs);
            sKeyData = (String) mapParam.get(sKey);
            sSql = sSql.substring(0, nKs) + sKeyData + sSql.substring(nJs + 2);
            nKs = sSql.indexOf("[[");
            nJs = sSql.indexOf("]]");
        }
        return sSql;
    }



    @Test
    public void bb() {
        String a = "{\n" +
                "\"ok\": false,\n" +
                "\"code\": 0 ,\n" +
                "\"message\": \"DPI不符\",\n" +
                "\"duration\": 491.02810000000005,\n" +
                "\"image_base64\": null,\n" +
                "\"crop_ok\": false,\n" +
                "\"crop_message\": \"找不到人像\",\n" +
                "\"details\": \"\"\n" +
                "}";

        CrjZjResp crjZjResp = JsonUtils.parseObject(a, CrjZjResp.class);
        System.out.println(crjZjResp);

    }
    @Test
    public void aa() {
        String type = "qxdb";
        String imageBase64 = "data:image/jpeg;base64,/9j/4AAQSkZJRgm9QP/2Q==";
        String filename = "111.jpg";

        CrjZjReq crjZjReq = new CrjZjReq();
        crjZjReq.setModule("3");
        crjZjReq.setFltFile("crj-2024.flt");
        crjZjReq.setImageBase64(imageBase64);
        crjZjReq.setImageFilename(filename);
        crjZjReq.setVersion("");
        crjZjReq.setOptAutoAd("0");
        crjZjReq.setOptAutoAd("0");
        if ("zj".equals(type)) {
            crjZjReq.setMode(2);
            crjZjReq.setContent("");
        } else if ("qxdb".equals(type)) {
            crjZjReq.setMode(8);
            crjZjReq.setContent("{\"func\":\"SetFaceQCMark\",\"param\":\"\"}");
        } else {
            throw new ServiceException(CkZzjErrorCode.CRJZJ_API_ERROR, "请选择质检模块运行模式");
        }


        System.out.println(JsonUtils.toJsonString(crjZjReq));


        JSONObject map = new JSONObject();
        map.put("module", "3");
        map.put("flt_file", "crj-2024.flt");
//            map.put("mode", mode);
        map.put("image_base64", imageBase64);
        map.put("image_filename", filename);
        map.put("version", "");
        map.put("content", "");
        if ("zj".equals(type)) {
            map.put("optAutoAd", "1");
            map.put("mode", 2);
            map.put("content", "");
        } else if ("qxdb".equals(type)) {
            map.put("optAutoAd", "0");
            map.put("mode", 8);
            map.put("content", "{\"func\":\"SetFaceQCMark\",\"param\":\"\"}");
        }
        System.out.println(map);
    }
}