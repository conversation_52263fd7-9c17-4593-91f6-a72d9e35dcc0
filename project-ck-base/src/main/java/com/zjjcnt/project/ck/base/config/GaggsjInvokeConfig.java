package com.zjjcnt.project.ck.base.config;

import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.base.constant.CkDictTypeConstants;
import com.zjjcnt.project.ck.base.constant.GaggsjConstants;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.GaggsjConfigParameter;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.GaggsjConfigParameterImpl;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.MapSendDataBuilderImpl;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.ViewInvokeParam;
import com.zjjcnt.project.ck.base.wscall.gaggsj.convertor.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公安公共数据配置
 *
 * <AUTHOR>
 * @date 2025-07-14 16:19:00
 */
@Configuration
public class GaggsjInvokeConfig {

    private static final String SERVICE_NAME = "常口系统";
    private static final String APP_URL = "http://41.188.23.151:8080/zdpyc_server/queryInfo/queryInfo.do";
    private static final String APP_ID = "751624B7F20C9A0AE0533C03760AFEA7";

    @Bean(name = "gaggsjConfigParameterWGX3300001887")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300001887() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00001887");
        gaggsjConfigParameter.setExchangeServiceName("省卫生计生委出生医学证明");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("csbh", "出生编号", false));
        viewInvokeParamList.add(new ViewInvokeParam("fsfz", "父身份证", false));
        viewInvokeParamList.add(new ViewInvokeParam("msfz", "母身份证", false));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);
        gaggsjConfigParameter.setSendDataBuilder(new MapSendDataBuilderImpl());
        GaggsjPlatformDataLicenceConvertor dataLicenceConvertor = new GaggsjPlatformDataLicenceConvertor();

        dataLicenceConvertor.setUrlPath("ELC_LICENCE_FILE@URL");
        dataLicenceConvertor.setAutoPdfUpload(true);
        dataLicenceConvertor.setStructPath("ELC_LICENCE_STRUCT@DATA");

        dataLicenceConvertor.setPdf2JpgWidth(0);
        dataLicenceConvertor.setPdf2JpgScale(1);
        dataLicenceConvertor.setPdf2JpgQuality(0.9f);

        gaggsjConfigParameter.setDataConvertor(dataLicenceConvertor);

        return gaggsjConfigParameter;
    }


    @Bean(name = "gaggsjConfigParameterWGX3300000810")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000810() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000810");
        gaggsjConfigParameter.setExchangeServiceName("出生一件事出生医学证照副页");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("jhrsfzh", "监护人身份证号"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);
        gaggsjConfigParameter.setSendDataBuilder(new MapSendDataBuilderImpl());
        GaggsjPlatformDataLicenceConvertor dataLicenceConvertor = new GaggsjPlatformDataLicenceConvertor();

        dataLicenceConvertor.setUrlPath("FILEURL");
        dataLicenceConvertor.setAutoPdfUpload(true);
        dataLicenceConvertor.setStructPath("_SELF");

        dataLicenceConvertor.setPdf2JpgWidth(0);
        dataLicenceConvertor.setPdf2JpgScale(1);
        dataLicenceConvertor.setPdf2JpgQuality(0.9f);

        gaggsjConfigParameter.setDataConvertor(dataLicenceConvertor);

        return gaggsjConfigParameter;
    }


    @Bean(name = "gaggsjConfigParameterWGX33-00000236")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000236() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000236");
        gaggsjConfigParameter.setExchangeServiceName("婚姻登记信息");
        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("cardId", "个人身份证号"));

        ViewInvokeParam viewInvokeParam = new ViewInvokeParam("sex", "性别");
        viewInvokeParam.setComponent(GaggsjConstants.COMPONENT_SELECT);
        Map<String, String> dictionary = new HashMap<>();
        dictionary.put("M", "男");
        dictionary.put("F", "女");
        viewInvokeParam.setDictionary(dictionary);
        viewInvokeParamList.add(viewInvokeParam);

        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);
        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("cardId,sex");

        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);
        GaggsjPlatformDataStructConvertor dataLicenceConvertor = new GaggsjPlatformDataStructConvertor();

        dataLicenceConvertor.setUrlPath("");
        dataLicenceConvertor.setAutoPdfUpload(true);
        dataLicenceConvertor.setStructPath("_SELF");

        dataLicenceConvertor.setPdf2JpgWidth(0);
        dataLicenceConvertor.setPdf2JpgScale(1);
        dataLicenceConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("id", "编号"));
        translaterList.add(new StructTranslater("", ""));
        translaterList.add(new StructTranslater("businessType", "登记类型"));
        translaterList.add(new StructTranslater("registrationOrg", "登记机关名称"));
        translaterList.add(new StructTranslater("mName", "男方姓名"));
        translaterList.add(new StructTranslater("mCardId", "男方身份证号码"));
        translaterList.add(new StructTranslater("fName", "女方姓名"));
        translaterList.add(new StructTranslater("fCardId", "女方身份证号码"));
        translaterList.add(new StructTranslater("registrationDate", "登记日期"));
        translaterList.add(new StructTranslater("tong_time", "数据归集时间"));

        dataLicenceConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataLicenceConvertor);

        return gaggsjConfigParameter;
    }


    @Bean(name = "gaggsjConfigParameterWGX33-00000280")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000280() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000280");
        gaggsjConfigParameter.setExchangeServiceName("社会保险个人参保信息");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("AAC002", "社会保障号码"));
        viewInvokeParamList.add(new ViewInvokeParam("aac003", "姓名"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("AAC002,aac003");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setMergeStruct( true);
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("AAC003", "姓名"));
        translaterList.add(new StructTranslater("AAC008", "人员参保状态"));
        translaterList.add(new StructTranslater("AAB004", "单位名称"));
        translaterList.add(new StructTranslater("AAB001", "单位编号"));
        translaterList.add(new StructTranslater("AAE140", "险种类型"));
        translaterList.add(new StructTranslater("AAC001", "个人编号"));
        translaterList.add(new StructTranslater("BAB010", "统一社会信用代码"));
        translaterList.add(new StructTranslater("BAZ159", "基准参保关系ID"));
        translaterList.add(new StructTranslater("AAC031", "个人缴费状态"));
        translaterList.add(new StructTranslater("AAB301", "行政区划代码"));
        translaterList.add(new StructTranslater("AAE030", "开始日期"));
        translaterList.add(new StructTranslater("AAC147", "证件号码"));
        translaterList.add(new StructTranslater("AAC002", "社会保障号码"));
        translaterList.add(new StructTranslater("AAC058", "证件类型"));
        translaterList.add(new StructTranslater("AAE031", "终止日期"));
        translaterList.add(new StructTranslater("tong_time", "归集时间"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00000203")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000203() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000203");
        gaggsjConfigParameter.setExchangeServiceName("大学毕业生就业信息");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("cardId", "公民身份号码"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("cardId");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("kSH", "考生号"));
        translaterList.add(new StructTranslater("sFZJH", "身份证件号"));
        translaterList.add(new StructTranslater("xM", "姓名"));
        translaterList.add(new StructTranslater("xB", "性别"));
        translaterList.add(new StructTranslater("xXMC", "学校名称"));
        translaterList.add(new StructTranslater("xXDM", "学校代码"));
        translaterList.add(new StructTranslater("zYMC", "专业名称"));
        translaterList.add(new StructTranslater("zYDM", "专业代码"));
        translaterList.add(new StructTranslater("pYFS", "培养方式"));
        translaterList.add(new StructTranslater("dWSZD", "单位所在地"));
        translaterList.add(new StructTranslater("xL", "学历"));
        translaterList.add(new StructTranslater("xZ", "学制"));
        translaterList.add(new StructTranslater("zGDW", "主管单位"));
        translaterList.add(new StructTranslater("sFKNS", "是否困难生"));
        translaterList.add(new StructTranslater("zYDK", "专业对口"));
        translaterList.add(new StructTranslater("bYSJ", "毕业时间"));
        translaterList.add(new StructTranslater("eMAIL", "Email地址"));
        translaterList.add(new StructTranslater("yRDW", "用人单位"));
        translaterList.add(new StructTranslater("jYBZ", "就业标志"));
        translaterList.add(new StructTranslater("sFSFS", "是否师范生"));
        translaterList.add(new StructTranslater("sYDDM", "生源地代码"));
        translaterList.add(new StructTranslater("dATDDZ", "档案投递地址"));
        translaterList.add(new StructTranslater("bDZH", "报到证号"));
        translaterList.add(new StructTranslater("sYD", "生源地"));
        translaterList.add(new StructTranslater("xXID", "信息ID"));
        translaterList.add(new StructTranslater("tong_time", "归集时间"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00000320")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000320() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000320");
        gaggsjConfigParameter.setExchangeServiceName("省国土资源厅不动产权证");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("qlrid", "权利人证件号"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("qlrid");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("bdcqzh", "不动产权字号"));
        translaterList.add(new StructTranslater("qlxz", "权利性质"));
        translaterList.add(new StructTranslater("zsgbh", "证书工本号"));
        translaterList.add(new StructTranslater("syqx", "使用期限"));
        translaterList.add(new StructTranslater("sbh", "编号"));
        translaterList.add(new StructTranslater("qlr", "权利人"));
        translaterList.add(new StructTranslater("qlqtzk", "权利其他状况"));
        translaterList.add(new StructTranslater("bdcdyh", "不动产单元号"));
        translaterList.add(new StructTranslater("mj", "面积"));
        translaterList.add(new StructTranslater("zl", "坐落"));
        translaterList.add(new StructTranslater("yt", "用途"));
        translaterList.add(new StructTranslater("qllx", "权利类型"));
        translaterList.add(new StructTranslater("gyqk", "共有情况"));
        translaterList.add(new StructTranslater("fj", "附记"));
        translaterList.add(new StructTranslater("zszt", "证书状态"));
        translaterList.add(new StructTranslater("szsj", "缮证时间"));
        translaterList.add(new StructTranslater("dbsj", "登簿时间"));
        translaterList.add(new StructTranslater("elcLicenceCode", "电子证照编码"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00000243")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000243() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000243");
        gaggsjConfigParameter.setExchangeServiceName("省教育厅普通高等学校毕业证(新)");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("sfzh", "公民身份号码"));
        viewInvokeParamList.add(new ViewInvokeParam("xm", "姓名", false));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("sfzh");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataLicenceConvertor dataConvertor = new GaggsjPlatformDataLicenceConvertor();
        dataConvertor.setUrlPath("ELC_LICENCE_FILE@URL");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("ELC_LICENCE_STRUCT@DATA");
        dataConvertor.setEmptyUrlUseStruct2Jpg(true);
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("ZSID", "证书ID"));
        translaterList.add(new StructTranslater("XM", "姓名"));
        translaterList.add(new StructTranslater("XB", "性别"));
        translaterList.add(new StructTranslater("CSRQ", "出生日期"));
        translaterList.add(new StructTranslater("XXMC", "学校名称"));
        translaterList.add(new StructTranslater("ZY", "专业"));
        translaterList.add(new StructTranslater("XXXS", "学习形式"));
        translaterList.add(new StructTranslater("CC", "层次"));
        translaterList.add(new StructTranslater("RXRQ", "入学日期"));
        translaterList.add(new StructTranslater("BJYRQ", "毕（结）业日期"));
        translaterList.add(new StructTranslater("XZ", "学制"));
        translaterList.add(new StructTranslater("ZSBH", "证书编号"));
        translaterList.add(new StructTranslater("XYZXM", "校（院）长姓名"));
        translaterList.add(new StructTranslater("XLXX", "学历类型"));
        translaterList.add(new StructTranslater("CERT_STATUS", "证照状态"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00000332")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000332() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000332");
        gaggsjConfigParameter.setExchangeServiceName("职称证书信息");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("cardId", "公民身份号码"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("cardId");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("idCardNumber", "身份证号码"));
        translaterList.add(new StructTranslater("name", "姓名"));
        translaterList.add(new StructTranslater("certificateNumber", "证书编号"));
        translaterList.add(new StructTranslater("specialty", "专业"));
        translaterList.add(new StructTranslater("qualification", "资格名称"));
        translaterList.add(new StructTranslater("companyName", "工作单位"));
        translaterList.add(new StructTranslater("qualificationTime", "取得资格时间"));
        translaterList.add(new StructTranslater("certificateIssuanceTime", "证书颁发时间"));
        translaterList.add(new StructTranslater("theJury", "评委会名称"));
        translaterList.add(new StructTranslater("tong_time", "归集时间"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00000335")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000335() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000335");
        gaggsjConfigParameter.setExchangeServiceName("失业人员就业创业证信息");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("AAC002", "公民身份号码"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("AAC002");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setMergeStruct(true);
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("AAC003", "姓名"));
        translaterList.add(new StructTranslater("AAE044", "登记失业日期"));
        translaterList.add(new StructTranslater("ADC001", "就业创业证号码"));
        translaterList.add(new StructTranslater("AAC002", "身份证号码"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00000338")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000338() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000338");
        gaggsjConfigParameter.setExchangeServiceName("初中级职称证书");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("ZJHM", "证件号码"));
        viewInvokeParamList.add(new ViewInvokeParam("XM", "姓名"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("ZJHM,XM");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("xM", "姓名"));
        translaterList.add(new StructTranslater("sSXL", "所属系列"));
        translaterList.add(new StructTranslater("xB", "性别"));
        translaterList.add(new StructTranslater("tXDZ", "通讯地址"));
        translaterList.add(new StructTranslater("qDZGSJ", "取得资格时间"));
        translaterList.add(new StructTranslater("zYMC", "专业名称"));
        translaterList.add(new StructTranslater("gZDW", "工作单位"));
        translaterList.add(new StructTranslater("bYXX", "毕业学校"));
        translaterList.add(new StructTranslater("zGMC", "资格名称"));
        translaterList.add(new StructTranslater("jBMC", "级别名称"));
        translaterList.add(new StructTranslater("zZZT", "在职状态"));
        translaterList.add(new StructTranslater("sZDS", "主管部门"));
        translaterList.add(new StructTranslater("dH", "联系电话"));
        translaterList.add(new StructTranslater("fZSJ", "发证时间"));
        translaterList.add(new StructTranslater("zSBH", "证书编号"));
        translaterList.add(new StructTranslater("fZDW", "发证单位"));
        translaterList.add(new StructTranslater("yB", "邮政编码"));
        translaterList.add(new StructTranslater("zJHM", "证件号码"));
        translaterList.add(new StructTranslater("sXZY", "所学专业"));
        translaterList.add(new StructTranslater("wH", "公布文号"));
        translaterList.add(new StructTranslater("zGXW", "最高学位"));
        translaterList.add(new StructTranslater("zGXL", "最高学历"));
        translaterList.add(new StructTranslater("qDFS", "取得方式"));
        translaterList.add(new StructTranslater("cSNY", "出生年月"));
        translaterList.add(new StructTranslater("zSCX", "证书查询"));
        translaterList.add(new StructTranslater("sZXS", "所在县（市、区）、市本级"));
        translaterList.add(new StructTranslater("zJLB", "证件类别"));
        translaterList.add(new StructTranslater("bZ", "备注"));
        translaterList.add(new StructTranslater("tong_time", "归集时间"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00000340")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000340() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000340");
        gaggsjConfigParameter.setExchangeServiceName("高级职称证书");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("ZJHM", "证件号码"));
        viewInvokeParamList.add(new ViewInvokeParam("XM", "姓名"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("ZJHM,XM");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("xM", "姓名"));
        translaterList.add(new StructTranslater("zJHM", "证件号码"));
        translaterList.add(new StructTranslater("xB", "性别"));
        translaterList.add(new StructTranslater("cSNY", "出生年月"));
        translaterList.add(new StructTranslater("sZXS", "所在县市"));
        translaterList.add(new StructTranslater("zJLB", "证件类别"));
        translaterList.add(new StructTranslater("zGMC", "资格名称"));
        translaterList.add(new StructTranslater("jBMC", "级别名称"));
        translaterList.add(new StructTranslater("zZZT", "在职状态"));
        translaterList.add(new StructTranslater("sZDS", "主管部门"));
        translaterList.add(new StructTranslater("sSXL", "所属系列"));
        translaterList.add(new StructTranslater("pWHMC", "评委会名称"));
        translaterList.add(new StructTranslater("qDFS", "取得方式"));
        translaterList.add(new StructTranslater("zSBH", "证书编号"));
        translaterList.add(new StructTranslater("lRDW", "录入单位"));
        translaterList.add(new StructTranslater("tXDZ", "通信地址"));
        translaterList.add(new StructTranslater("qDZGSJ", "取得资格时间"));
        translaterList.add(new StructTranslater("zYMC", "专业名称"));
        translaterList.add(new StructTranslater("gZDW", "工作单位"));
        translaterList.add(new StructTranslater("bYXX", "毕业学校"));
        translaterList.add(new StructTranslater("dWXZ", "单位性质"));
        translaterList.add(new StructTranslater("sXZY", "所学专业"));
        translaterList.add(new StructTranslater("zGXW", "最高学位"));
        translaterList.add(new StructTranslater("zGXL", "最高学历"));
        translaterList.add(new StructTranslater("dH", "联系电话"));
        translaterList.add(new StructTranslater("fZSJ", "发证时间"));
        translaterList.add(new StructTranslater("fZDW", "发证单位"));
        translaterList.add(new StructTranslater("yB", "邮政编码"));
        translaterList.add(new StructTranslater("wH", "公布文号"));
        translaterList.add(new StructTranslater("zSCX", "证书查询"));
        translaterList.add(new StructTranslater("bZ", "备注"));
        translaterList.add(new StructTranslater("tong_time", "归集时间"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00000462")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000462() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000462");
        gaggsjConfigParameter.setExchangeServiceName("学生信息");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("XSXM", "学生姓名"));
        viewInvokeParamList.add(new ViewInvokeParam("SFZJH", "身份证件号"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("XSXM,SFZJH");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("BJMC", "班级"));
        translaterList.add(new StructTranslater("RXNY", "入学年月"));
        translaterList.add(new StructTranslater("XSXM", "学生姓名"));
        translaterList.add(new StructTranslater("NJMC", "年级"));
        translaterList.add(new StructTranslater("DQMC", "地区名称"));
        translaterList.add(new StructTranslater("XXBSM", "学校标识码"));
        translaterList.add(new StructTranslater("DQBM", "地区编码"));
        translaterList.add(new StructTranslater("NJM", "年级码"));
        translaterList.add(new StructTranslater("ZY", "专业"));
        translaterList.add(new StructTranslater("SFZJH", "身份证件号"));
        translaterList.add(new StructTranslater("ZZMM", "政治面貌"));
        translaterList.add(new StructTranslater("GRBSM", "学号"));
        translaterList.add(new StructTranslater("XXMC", "学校名称"));
        translaterList.add(new StructTranslater("XSZT", "状态"));
        translaterList.add(new StructTranslater("XZ", "学制"));
        translaterList.add(new StructTranslater("XD", "学段"));
        translaterList.add(new StructTranslater("Biz_time", "业务库数据产生时间"));
        translaterList.add(new StructTranslater("XSID", "学生ID"));
        translaterList.add(new StructTranslater("SFZJLX", "身份证件类型"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX3300000971")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000971() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000971");
        gaggsjConfigParameter.setExchangeServiceName("教育部高校学历姓名及证件号码查询接口");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("xm", "姓名"));
        viewInvokeParamList.add(new ViewInvokeParam("zjhm", "证件号码"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("xm,zjhm");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructXmlConvertor dataConvertor = new GaggsjPlatformDataStructXmlConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("xm", "姓名"));
        translaterList.add(new StructTranslater("zjhm", "证件号码"));
        translaterList.add(new StructTranslater("reqid", "请求标识码"));
        translaterList.add(new StructTranslater("yxmc", "毕业学校名称"));
        translaterList.add(new StructTranslater("zymc", "专业名称"));
        translaterList.add(new StructTranslater("cc", "层次"));
        translaterList.add(new StructTranslater("rxrq", "入学日期"));
        translaterList.add(new StructTranslater("byrq", "毕业日期"));
        translaterList.add(new StructTranslater("xxxs", "学习形式"));
        translaterList.add(new StructTranslater("zsbh", "学历证书编号"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX3300000400")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000400() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000400");
        gaggsjConfigParameter.setExchangeServiceName("省人社厅专业技术人员资格证书");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("zjhm", "证件号码"));
        viewInvokeParamList.add(new ViewInvokeParam("name", "姓名", false));
        viewInvokeParamList.add(new ViewInvokeParam("zzname", "证照名称", false));
        viewInvokeParamList.add(new ViewInvokeParam("hgnf", "合格年份", false));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);
        gaggsjConfigParameter.setSendDataBuilder(new MapSendDataBuilderImpl());
        GaggsjPlatformDataLicenceConvertor dataLicenceConvertor = new GaggsjPlatformDataLicenceConvertor();

        dataLicenceConvertor.setUrlPath("ELC_LICENCE_FILE@URL");
        dataLicenceConvertor.setAutoPdfUpload(true);
        dataLicenceConvertor.setStructPath("ELC_LICENCE_STRUCT@DATA");

        dataLicenceConvertor.setPdf2JpgWidth(0);
        dataLicenceConvertor.setPdf2JpgScale(1);
        dataLicenceConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("FZDW", "发证单位"));
        translaterList.add(new StructTranslater("QFSJ", "签发时间"));
        translaterList.add(new StructTranslater("KSXM_1", "考生姓名"));
        translaterList.add(new StructTranslater("ZZBH", "证照编号"));
        translaterList.add(new StructTranslater("YXQQS", "出生日期"));
        translaterList.add(new StructTranslater("YXQJZ", "有效期截止"));
        translaterList.add(new StructTranslater("ZJLB", "证件类别"));
        translaterList.add(new StructTranslater("ZJHM", "证件号码"));
        translaterList.add(new StructTranslater("HGNF", "合格年份"));
        translaterList.add(new StructTranslater("BKZYMC", "报考专业名称"));
        translaterList.add(new StructTranslater("YXNDS", "有效年度数"));
        translaterList.add(new StructTranslater("ZSGLH", "证书管理号"));
        translaterList.add(new StructTranslater("ZSCX", "证书查询"));
        translaterList.add(new StructTranslater("BKJBMC", "报考级别名称"));
        translaterList.add(new StructTranslater("YXQQSND", "有效期起始年度"));
        translaterList.add(new StructTranslater("KSSJ", "考试时间"));
        translaterList.add(new StructTranslater("KSKM", "考试科目"));
        translaterList.add(new StructTranslater("ZZNAME", "证照名称"));
        translaterList.add(new StructTranslater("ZPLJ", "照片路径"));
        translaterList.add(new StructTranslater("STATUS", "状态"));
        dataLicenceConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataLicenceConvertor);
        return gaggsjConfigParameter;
    }


    @Bean(name = "gaggsjConfigParameterWGX3300000426")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000426() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000426");
        gaggsjConfigParameter.setExchangeServiceName("省人社厅高级职称证书");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("zjhm", "证件号码"));
        viewInvokeParamList.add(new ViewInvokeParam("name", "姓名", false));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);
        gaggsjConfigParameter.setSendDataBuilder(new MapSendDataBuilderImpl());
        GaggsjPlatformDataLicenceConvertor dataLicenceConvertor = new GaggsjPlatformDataLicenceConvertor();

        dataLicenceConvertor.setUrlPath("ELC_LICENCE_FILE@URL");
        dataLicenceConvertor.setAutoPdfUpload(true);
        dataLicenceConvertor.setStructPath("ELC_LICENCE_STRUCT@DATA");

        dataLicenceConvertor.setPdf2JpgWidth(0);
        dataLicenceConvertor.setPdf2JpgScale(1);
        dataLicenceConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("ZSBH", "证书编号"));
        translaterList.add(new StructTranslater("ZJHM", "证件号码"));
        translaterList.add(new StructTranslater("XM", "姓名"));
        translaterList.add(new StructTranslater("XB", "性别"));
        translaterList.add(new StructTranslater("ZYMC", "专业名称"));
        translaterList.add(new StructTranslater("ZGMC", "资格名称"));
        translaterList.add(new StructTranslater("QDZGSJ", "取得资格时间"));
        translaterList.add(new StructTranslater("FZSJ", "发证时间"));
        translaterList.add(new StructTranslater("PWHMC", "评委会名称"));
        translaterList.add(new StructTranslater("FZDW", "发证单位"));
        translaterList.add(new StructTranslater("CSNY", "出生年月"));
        translaterList.add(new StructTranslater("ZSCX", "证书查询"));
        translaterList.add(new StructTranslater("ZXYZM", "在线验证码"));
        dataLicenceConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataLicenceConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX3300000596")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300000596() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00000596");
        gaggsjConfigParameter.setExchangeServiceName("法人企业信息查询");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("identificationId", "身份证件号码"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);
        gaggsjConfigParameter.setSendDataBuilder(new MapSendDataBuilderImpl());
        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();

        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");

        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("lv_pripid", "隶属企业主体身份代码"));
        translaterList.add(new StructTranslater("proloc", "生产经营地"));
        translaterList.add(new StructTranslater("sfgzqzx", "是否个转企二次转型"));
        translaterList.add(new StructTranslater("yiedistrict", "生产经营地所在行政区划"));
        translaterList.add(new StructTranslater("industryco", "行业代码（中文）"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00001777")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300001777() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00001777");
        gaggsjConfigParameter.setExchangeServiceName("火化信息");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("cardId", "公民身份号码"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("cardId");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructConvertor dataConvertor = new GaggsjPlatformDataStructConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("dURIDNO", "身份证号码"));
        translaterList.add(new StructTranslater("dURNAME", "姓名"));
        translaterList.add(new StructTranslater("dURSEX", "性别"));
        translaterList.add(new StructTranslater("dURNATION", "民族"));
        translaterList.add(new StructTranslater("DURBIRTHDATE", "出生日期"));
        translaterList.add(new StructTranslater("dURHOUSEHOLD", "户籍所在地"));
        translaterList.add(new StructTranslater("dURNATIONALITY", "国籍"));
        translaterList.add(new StructTranslater("dURPVDNO", "所在殡仪馆"));
        translaterList.add(new StructTranslater("durdeathdate", "死亡日期"));
        translaterList.add(new StructTranslater("dURCREMATIONDATE", "火化日期"));
        translaterList.add(new StructTranslater("dURID", "火化编号"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00002017")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300002017() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00002017");
        gaggsjConfigParameter.setExchangeServiceName("省教育厅普通高等学校毕业证");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("sfzh", "公民身份号码"));
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("sfzh");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataLicenceConvertor dataConvertor = new GaggsjPlatformDataLicenceConvertor();
        dataConvertor.setUrlPath("");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setStructPath("_SELF");
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(2);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("xm", "姓名"));
        translaterList.add(new StructTranslater("xb", "性别"));
        translaterList.add(new StructTranslater("csrq", "出生日期"));
        translaterList.add(new StructTranslater("xxmc", "学校名称"));
        translaterList.add(new StructTranslater("zy", "专业"));
        translaterList.add(new StructTranslater("xxxs", "学习形式"));
        translaterList.add(new StructTranslater("cc", "层次"));
        translaterList.add(new StructTranslater("rxrq", "入学日期"));
        translaterList.add(new StructTranslater("bjyrq", "毕（结）业日期"));
        translaterList.add(new StructTranslater("xz", "学制"));
        translaterList.add(new StructTranslater("bjy", "毕（结）业"));
        translaterList.add(new StructTranslater("zsbh", "证书编号"));
        translaterList.add(new StructTranslater("xyzxm", "校（院）长姓名"));
        translaterList.add(new StructTranslater("zp", "照片"));
        translaterList.add(new StructTranslater("xlxx", "学历类型"));
        translaterList.add(new StructTranslater("certStatus", "证照状态"));
        translaterList.add(new StructTranslater("elcLicenceCode", "电子证照编码"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }

    @Bean(name = "gaggsjConfigParameterWGX33-00006629")
    public GaggsjConfigParameter gaggsjConfigParameterWGX3300006629() {
        GaggsjConfigParameterImpl gaggsjConfigParameter = getCommonGaggsjConfigParameterImpl("WGX33-00006629");
        gaggsjConfigParameter.setExchangeServiceName("个人参保证明获取PDF");

        List<ViewInvokeParam> viewInvokeParamList = new ArrayList<>();
        viewInvokeParamList.add(new ViewInvokeParam("idcard", "公民身份号码"));
        viewInvokeParamList.add(new ViewInvokeParam("name", "姓名"));
        ViewInvokeParam sjgb = new ViewInvokeParam("sjgb", "是否查询机关养老");
        sjgb.setComponent(GaggsjConstants.COMPONENT_SELECT);
        Map<String, String> sjgbDict = new HashMap<>();
        sjgbDict.put("0", "否");
        sjgbDict.put("1", "是");
        sjgb.setDefaultValue(Constants.NO);
        sjgb.setDictionary(sjgbDict);
        viewInvokeParamList.add(sjgb);

        ViewInvokeParam model = new ViewInvokeParam("model", "查询类型");
        model.setComponent(GaggsjConstants.COMPONENT_SELECT);
        Map<String, String> modelDict = new HashMap<>();
        modelDict.put("1", "个人参保");
        modelDict.put("2", "历年基本养老");
        modelDict.put("3", "养老待遇发放");
        model.setDefaultValue("1");
        model.setDictionary(modelDict);
        viewInvokeParamList.add(model);

        ViewInvokeParam areaAk = new ViewInvokeParam("areaAK", "参保地区的业务代码", false);
        areaAk.setComponent(GaggsjConstants.COMPONENT_API_SELECT);
        areaAk.setDictType(CkDictTypeConstants.DM_CBDQYWDM);
        viewInvokeParamList.add(areaAk);

        ViewInvokeParam month = new ViewInvokeParam("month", "查询月份");
        month.setComponent(GaggsjConstants.COMPONENT_SELECT);
        Map<String, String> monthDict = new HashMap<>();
        monthDict.put("12", "12个月");
        monthDict.put("24", "24个月");
        monthDict.put("36", "36个月");
        monthDict.put("48", "48个月");
        month.setDefaultValue("12");
        month.setDictionary(monthDict);
        viewInvokeParamList.add(month);
        gaggsjConfigParameter.setViewInvokeParamList(viewInvokeParamList);

        MapSendDataBuilderImpl mapSendDataBuilder = new MapSendDataBuilderImpl();
        mapSendDataBuilder.setRequiredField("areaAK,sjgb,model,idcard,month,name");
        gaggsjConfigParameter.setSendDataBuilder(mapSendDataBuilder);

        GaggsjPlatformDataStructJSONStringConvertor dataConvertor = new GaggsjPlatformDataStructJSONStringConvertor();
        dataConvertor.setUrlPath("downloadUrl");
        dataConvertor.setAutoPdfUpload(true);
        dataConvertor.setPdf2JpgWidth(0);
        dataConvertor.setPdf2JpgScale(1);
        dataConvertor.setPdf2JpgQuality(0.9f);

        List<StructTranslater> translaterList = new ArrayList<>();
        translaterList.add(new StructTranslater("success", "是否成功标志"));
        translaterList.add(new StructTranslater("downloadUrl", "PDF文件路径"));
        translaterList.add(new StructTranslater("fileName", "PDF文件名"));
        translaterList.add(new StructTranslater("message", "信息提示"));
        dataConvertor.setTranslaterList(translaterList);

        gaggsjConfigParameter.setDataConvertor(dataConvertor);
        return gaggsjConfigParameter;
    }


    private GaggsjConfigParameterImpl getCommonGaggsjConfigParameterImpl(String configId) {
        GaggsjConfigParameterImpl gaggsjConfigParameter = new GaggsjConfigParameterImpl();
        gaggsjConfigParameter.setConfigId(configId);
        gaggsjConfigParameter.setExchangeServiceId(configId);
        gaggsjConfigParameter.setInvokeServiceName(SERVICE_NAME);
        gaggsjConfigParameter.setAppUrl(APP_URL);
        gaggsjConfigParameter.setAppId(APP_ID);
        gaggsjConfigParameter.setPowerMatters("");
        gaggsjConfigParameter.setSubPowerMatters("");
        return gaggsjConfigParameter;
    }

}